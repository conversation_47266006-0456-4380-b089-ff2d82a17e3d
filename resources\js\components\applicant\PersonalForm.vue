<template>
    <div>
        <form @submit.prevent="null">
            <div class="row">
                <div class="col-12">
                    <label class="font-weight-bold" for="first_name"
                        >Full Name:</label
                    >
                </div>
                <div class="col-12 col-lg-4">
                    <input
                        class="form-control mb-30 bg-gray"
                        :class="{
                            'is-invalid':
                                submitted && $v.form.first_name.$error,
                        }"
                        type="text"
                        name="first_name"
                        v-model="form.first_name"
                        id="first_name"
                        placeholder="First Name"
                    />
                </div>
                <div class="col-12 col-lg-4">
                    <input
                        class="form-control mb-30 bg-gray"
                        type="text"
                        v-model="form.middle_names"
                        name="middle_names"
                        placeholder="Middle Names"
                    />
                </div>
                <div class="col-12 col-lg-4">
                    <input
                        class="form-control mb-30 bg-gray"
                        type="text"
                        :class="{
                            'is-invalid': submitted && $v.form.last_name.$error,
                        }"
                        v-model="form.last_name"
                        name="last_name"
                        placeholder="Last Name"
                    />
                </div>
                <div class="col-12 col-lg-12">
                    <input
                    :class="{
                        'is-invalid': submitted && $v.form.gov_id.$error,
                    }"
                        class="form-control mb-30 bg-gray"
                        type="text"
                        name="gov_id"
                        v-model="form.gov_id"
                        placeholder="National Id Number"
                    />
                </div>
                <div class="col-12 col-lg-6">
                    <datepicker
                        format="dd MMMM yyyy"
                        :initialView="'year'"
                        @selected="checkDateValidity($event)"
                        name="dob"
                        v-model="form.dob"
                        placeholder="Date of Birth"
                        :input-class="
                            date_invalid || (submitted && $v.form.dob.$error)
                                ? 'form-control is-invalid'
                                : 'mb-30 form-control'
                        "
                    ></datepicker>
                    <div class="mb-30" v-if="!date_invalid && !form.dob"></div>
                    <div
                        class="mb-30 mt-2 invalid-feedback d-block"
                        v-if="date_invalid && form.dob"
                    >
                        You must be above 16 years but younger than 65.
                    </div>
                </div>
                <div class="col-12 col-lg-6">
                    <select
                        v-model="form.gender"
                        :class="{ 'is-invalid': submitted && $v.form.gender.$error,}"
                        class=" form-control mb-30 bg-gray " id="gender">
                        <option value="" disabled selected>
                            Gender
                        </option>
                        <option value="m">Male</option>
                        <option value="f">Female</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <label class="font-weight-bold" for="email">Contact:</label>
                </div>
                <div class="col-12 col-lg-6">
                    <input
                        class="form-control bg-gray"
                        @focus="clearEmailErr()"
                        id="email"
                        type="email"
                        v-model="form.email"
                        :class="{
                            'is-invalid':
                                email_taken ||
                                (submitted && $v.form.email.$error),
                        }"
                        placeholder="Email Address (Optional)"
                    />
                    <div class="mb-30" v-if="!email_taken"></div>
                    <div
                        class="mb-30 mt-2 invalid-feedback d-block"
                        v-if="email_taken"
                    >
                        Email already taken
                    </div>
                </div>

                <div class="col-12 col-lg-6">
                    <input
                        class="form-control bg-gray"
                        type="tel"
                        v-model="phone"
                        name="phone"
                        maxlength="29"
                        placeholder="Mobile Number"
                    />
                    <div class="mb-30 mt-1"></div>
                </div>
            </div>

            <div class="row">
                <div class="col-12">
                    <label class="font-weight-bold" for="street"
                        >Physical Address:</label
                    >
                </div>
                <div class="col-12 col-lg-6">
                    <input
                        class="form-control mb-30 bg-gray"
                        :class="{
                            'is-invalid': submitted && $v.form.street.$error,
                        }"
                        type="text"
                        v-model="form.street"
                        id="street"
                        name="street"
                        placeholder="Address Line 1"
                    />
                </div>
                <div class="col-12 col-lg-6">
                    <input
                        class="form-control mb-30 bg-gray"
                        type="text"
                        v-model="form.apartment"
                        id="apartment"
                        name="apartment"
                        placeholder="Address Line 2"
                    />
                </div>
                <div class="col-12 col-lg-6">
                    <input
                        class="form-control mb-30 bg-gray"
                        type="text"
                        :class="{
                            'is-invalid': submitted && $v.form.city.$error,
                        }"
                        v-model="form.city"
                        name="city"
                        placeholder="City"
                    />
                </div>
                <!-- Country field hidden since only Zimbabwe is available -->
                <input type="hidden" v-model="form.country" value="ZW" />


            </div>
            <div class="row">
                <div class="col-sm-6">
                    <label class="font-weight-bold" for="street">{{branchdesc}} (Optional)</label>
                    <select
                    v-model="form.branch"
                    :class="{    'is-invalid': submitted && $v.form.branch.$error,}"
                    class="custom-select form-control mb-30 bg-gray selectpicker"
                    id="branch">
                        <option selected>
                            Other
                        </option>
                        <option v-for="n in branches" :key="n.id" :value="n.id">{{n.name}}</option>
                    </select>
                </div>
                <div class="col-sm-6">
                    <label class="font-weight-bold" for="street">Registering ambassador (Optional)</label>
                    <select
                    v-model="form.ambassador_id"
                    :class="{
                        'is-invalid': submitted && $v.form.ambassador_id.$error,
                    }"
                    class="custom-select form-control mb-30 bg-gray selectpicker"
                    id="ambassador_id">
                        <option value="" selected>
                            None
                        </option>
                        <option v-for="n in ambassadors" :key="n.id" :value="n.id">{{n.name}}</option>
                    </select>
                </div>



            </div>
            <div class="col-12 text-right">
                <button
                    class="btn radix-btn"
                    type="button"
                    :disabled="submitted && validating_email"
                    @click="handlePersonal()"
                >
                    Next Step
                </button>
            </div>
        </form>
    </div>
</template>

<script>
import { required, email } from "vuelidate/lib/validators";
import { isUnique } from "../../services/api";

export default {
    props: ['branches', 'ambassadors', 'branchdesc'],
    components: {
        Datepicker: () => import("vuejs-datepicker"),
    },

    data() {
        return {
            phone: "",
            form: {},
            email_taken: false,
            submitted: false,
            date_invalid: false,
            validating_email: false,
        };
    },
    validations: {
        form: {
            first_name: {  required },
            last_name: { required },
            gov_id: { required },
            ambassador_id: {  },
            street: { required },
            email: {
                email,
            },
            phone: { required },
        city: { required },
            country: { },
            gender: {required },
            branch: {  },
            dob: { required },
        },
    },
    mounted() {
        // Set Zimbabwe as default country since it's the only option
        this.form.country = 'ZW';
    },
    methods: {
        handlePersonal() {
            this.submitted = true;

            this.$v.$touch();

            if (this.$v.$invalid)
                return this.scrollErrorToView();

            if(this.form.email?.trim()=="") this.form.email = null;

            this.validating_email = true;

            if(this.form.email==null){
                return this.$emit("done", this.form);
            }


            isUnique(this.form.email)
                .then(({ data }) => {
                    // debbuger;
                    if (data && data.unique) {
                        return this.$emit("done", this.form);
                    }
                    this.email_taken = true;
                    this.scrollErrorToView();
                })
                .catch((err) => {
                    console.error({ err });
                    this.email_taken = true;
                    this.scrollErrorToView();
                })
                .finally(() => {
                    this.submitted = false;
                    this.validating_email = false;
                });
        },
        checkDateValidity(d) {
            const sixteenYearsAgo = moment().subtract(16, "years");
            const sixfiveYearsAgo = moment().subtract(65, "years");
            const selectedDate = moment(d);
            if (selectedDate.isAfter(sixteenYearsAgo) && sixfiveYearsAgo.isAfter(selectedDate)) {
                this.date_invalid = true;
            } else {
                this.date_invalid = false;
            }
        },

        scrollErrorToView() {
            setTimeout(() => {
                const elem = $(`.is-invalid`).first();
                $(elem).focus();
            }, 100);
        },
        clearEmailErr() {
            setTimeout(() => {
                this.email_taken = false;
            }, 5000);
        },
    },
    watch: {
        phone(p) {
            this.form.phone = p;
        },
    },
};
</script>

<template>
  <div class="radix--blog--area mb-5 mt-3">
    <div class="container">
      <div class="row">

        <div class="col-12 mb-2 mb-md-0">
          <div class="account-side-area">
            <div class="single-widget-area mb-3">
              <ul class="catagories-list row" id="myScrollableRow">
                <li
                  class="pl-3 col text-center"
                  v-for="link in links"
                  @click="viewForm(link.step)"
                  :key="link.step"
                  :style="completed[link.step] ? '' : 'cursor: not-allowed;'"
                >
                  <a :class="isActive(link.step)" :style="completed[link.step] ? '' : 'cursor: not-allowed;'" href="javascript:;">
                    <!-- <i :class="'lni ' + link.icon"></i> -->
                    <span class="menu-step" :style="isActive(link.step)!='' ? '    background-color: var(--primary);' : ''">{{link.step}}</span>
                    <br>
                    <span :style="isActive(link.step)!='' ? '    color: var(--primary);' : ''">{{link.title}}</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="col-12"><hr></hr></div>
        <div class="col-12 ">
            <div v-show="step === 1">
                <div id="step-1">
                    <div class="row" >
                        <div class="col-6 btn group">
                            <h2 class="text-left">{{app_name}}</h2>
                            <!-- <img src="storage/img/l" style="max-height: 54px;" alt /> -->
                        </div>
                        <div v-if="show_underwriter" class="col-6 text-right">
                            <p class="p-0 m-0">Underwritten by</p>
                            <img src="storage/img/zimnatlogo.png" style="max-height: 54px;" alt />
                        </div>
                    </div>
                  <h5 class="mb-1">Select Services</h5>
                </div>
                <customise-form @done="formCompleted($event, 1)" :forms="forms" :categories="categories"></customise-form>
            </div>

          <div v-show="step === 2">
            <div id="step-2">
              <h5 class="mb-1">Become a Member</h5>
              <!-- <p>Please note that all applications require the approval of the Funeral Cover Association.</p> -->
            </div>

            <personal-form @done="formCompleted($event, 2)" :branches="branches"  :ambassadors="ambassadors" :branchdesc="branchdesc" ></personal-form>
          </div>

          <div v-show="step === 3">
            <div id="step-3">
              <h5 class="mb-1">Next of kin details</h5>
              <p>The person we can contact in case of emergency.</p>
            </div>
            <nextof-kin @done="formCompleted($event, 3)"></nextof-kin>
          </div>
          <div v-show="step === 4">
            <div id="step-4">
              <h5 class="mb-1">Dependants details</h5>
              <!-- <p>You can add up to 4 dependants</p> -->
            </div>
            <nominee-container :customise_nominee_package="customise_nominee_package" :max_nominees="max_nominees" :nominees="nominees" @done="formCompleted($event, 4)"></nominee-container>
          </div>
          <div v-show="step === 5">
            <div id="step-5">
              <h5 class="mb-1">Agree to terms</h5>
            </div>
            <terms-form @done="formCompleted($event, 5)" :forms="forms"></terms-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['max_nominees', 'customise_nominee_package','categories', 'branches', 'ambassadors', 'branchdesc','show_underwriter', 'app_name'],
  mounted() {
    console.log(this.app_name)
},
  components: {
    PersonalForm: () => import("./applicant/PersonalForm"),
    NextofKin: () => import("./applicant/NextofKin"),
    NomineeContainer: () => import("./applicant/NomineeContainer"),
    TermsForm: () => import("./applicant/TermsForm"),
    CustomiseForm: () => import("./applicant/CustomiseForm"),
  },
  data() {
    return {
      step: 1,
      forms: {},
      nominees:[],
      completed: [],
      links: [
        {
          title: "Customize",
          step: 1,
          icon: "lni-bar-chart"
        },
        {
          title: "Personal",
          step: 2,
          icon: "lni-user"
        },
        {
          title: "Next of Kin",
          step: 3,
          icon: "lni-users"
        },
        {
          title: "Dependant",
          step: 4,
          icon: "lni-customer"
        },
        {
          title: "Terms",
          step: 5,
          icon: "lni-radio-button"
        }
      ]
    };
  },

  methods: {
    viewForm(step) {
      if(!this.completed[step]) return;
      this.step = step;
    },
    isActive(step) {
      if (step === this.step) return "active-step";
      return "";
    },
    formCompleted(event, step) {
      this.completed[step] = true;
      this.forms = { ...this.forms, ...event };
      console.log("Previous Form\n", this.forms);
      if (step < this.links.length + 1) {
        this.step += 1;
      }
    },
    addNominee(i) {
    //   const time = new Date().getTime();
      this.nominees.push(i);
    },
  },
  watch: {
    step(step) {

    console.log(parseInt(this.forms.additionalMembers)-1);
    this.nominees = [];
    for(let iss=0; iss<parseInt(this.forms.additionalMembers); iss++){
        console.log("Adding nominee");
      this.addNominee(iss);
      console.log(this.nominees);
    }


      if(typeof $ !== "undefined"){
        $('html, body').animate({
          scrollTop: ($(`#step-${step}`).offset().top + 24) + "px"
        }, "slow");
      }
    }
  }
};

    window.addEventListener('mousewheel', function(e) {
        // e.preventDefault();
        const step = -100; // Adjust the step value as needed
        const newPos = window.pageXOffset + (e.wheelDelta < 0 ? step : -step);
        document.body.scrollLeft = newPos;
    });
</script>
<style>
.menu-step{
    color: white;
    background-color: var(--base)
;
    /* padding: 10px; */
    /* border: 4px solid var(--secondary-color); */
    border-radius: 50%;
    height: 30px;
    width: 30px;
    padding-top: 0px;
    text-align: center;
}

@media  screen and (min-width: 990px) {
    .menu-step{
        color: white;
        background-color: var(--base)
;
        /* padding: 10px; */
        /* border: 4px solid var(--secondary-color); */
        border-radius: 50%;
        height: 30px;
        width: 30px;
        padding-top: 0px;
        text-align: center;
    }
}

@media  screen and (max-width : 990px) {
    .menu-step{
        color: white;
        background-color: var(--base)
;
        /* padding: 10px; */
        /* border: 4px solid var(--secondary-color); */
        border-radius: 50%;
        height: 30px;
        width: 30px;
        padding-top: 3px;
        text-align: center;
    }

}
</style>

$(function() {
    // Get the form.
    var form = $('#main_contact_form');
    // Get the messages div.
    var formMessages = $('#success_fail_info');
    // Set up an event listener for the contact form.
    $(form).submit(function(e) {
        // Stop the browser from submitting the form.
        e.preventDefault();

        // Serialize the form data.
        var formData = $(form).serialize();

        // Submit the form using AJAX.
        $.ajax({
                type: 'POST',
                url: $(form).attr('action'),
                data: formData
            })
            .done(function(response) {
                // Make sure that the formMessages div has the 'success' class.
                $(formMessages).removeClass('text-danger');
                $(formMessages).addClass('text-success');

                // Set the message text.
                $(formMessages).text('Thanks! Message has been sent.');

                // Clear the form.
                $('#name').val('');
                $('#email').val('');
                $('#message').val('');
            })
            .fail(function(data) {
                // Make sure that the formMessages div has the 'error' class.
                $(formMessages).removeClass('text-success');
                $(formMessages).addClass('text-danger');

                // Set the message text.
                $(formMessages).text("Oops! An error occurred.");

            });
    });
});
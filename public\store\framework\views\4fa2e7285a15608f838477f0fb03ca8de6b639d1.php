<?php $__env->startSection('page'); ?>
Sign In
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
        <!-- Register Thumbnail-->
        <div class="d-none d-lg-block col-12 col-lg-6">
            <div class="register-thumbnail mb-50">
                <div class="mx-auto" style="width: 66%;">
                    <img src="<?php echo e(asset('storage/bg/login.png')); ?>" alt="">
                </div>
            </div>
        </div>
        <!-- Register Card-->
        <div class="col-12 col-lg-6">
        <?php if($errors->any()): ?>
            <div class="alert alert-danger" role="alert">
                <ul>
                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
            <div class="card-body">
                <h5>Failed</h5>

                <form id="redirectForm">
                <div class="  py-3 mr-sm-2">
                    <label class=" " for="const">
                    Payment Failed
                    </label>
                </div>
                </form>
            </div>
            </div>
        <?php else: ?>
            <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
                <div class="card-body">
                    <h5>Success</h5>

                    <form id="redirectForm">
                    <div class="  py-3 mr-sm-2">
                        <label class=" " for="const">
                        Payment Received Check Your Email
                        </label>
                        <p>Didn't receive confirmation email? <a href="/contact-us">Get in touch</a></p>
                    </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>



        </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/pages/recpay.blade.php ENDPATH**/ ?>


<?php $__env->startSection('page'); ?>
Next of Kin
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <?php if($errors->any()): ?>
        <div class="alert alert-danger" role="alert">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <?php endif; ?>
        <div class="card card-user">
            <div class="card-header">
                <h5 class="card-title">Edit Next of Kin</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('members-area.nok')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Full Name</label>
                                <input required name="nok_full_name" type="text" class="form-control" value="<?php echo e($nok->full_name); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <dob-input :unlocked="true" :name="'nok_dob'" :val="'<?php echo e($nok->dob); ?>'"></dob-input>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="exampleInputEmail1">Email address</label>
                                <input required name="nok_email" type="email" class="form-control" value="<?php echo e($nok->email); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Phone</label>
                                <input required name="nok_phone" type="text" class="form-control" value="<?php echo e($nok->phone); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Street</label>
                                <input required name="nok_street" type="text" class="form-control" value="<?php echo e($nok->street); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>City</label>
                                <input required name="nok_city" type="text" class="form-control" value="<?php echo e($nok->city); ?>">
                            </div>
                        </div>
                        <country-picker classes="col-md-4" value="<?php echo e($nok->country); ?>" input_name="nok_country"></country-picker>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>ZIP</label>
                                <input name="nok_zip" type="text" class="form-control" value="<?php echo e($nok->zip); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="update ml-auto mr-auto">
                            <button type="submit" class="btn btn-primary ">Update Next of Kin</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('member.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/nok.blade.php ENDPATH**/ ?>

(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[31],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js&":
/*!******************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ "./node_modules/axios/index.js");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(axios__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ __webpack_exports__["default"] = ({
  props: ['route', 'user', 'client_id'],
  mounted: function mounted() {
    var _this = this;
    console.log(this.user, this.client_id);
    this.paynowUrl = "/subscription-payment/submit/paynow/" + this.user.id;
    this.clicknpayUrl = "/api/clicknpay/deposit/" + this.user.id;
    console.log(this.paynowUrl, this.user.id);
    $("#subscriptionPaymentModal").on("show.bs.modal", function (e) {
      if (!_this.open) {
        var scr = document.createElement("script");
        scr.src = "https://www.paypal.com/sdk/js?client-id=".concat(_this.client_id, "&currency=GBP");
        scr.addEventListener("load", _this.loadPayPal);
        scr.addEventListener("error", _this.loadError);
        document.body.append(scr);
        _this.script = scr;
      }
    });
    $('#subscriptionPaymentModal').on('hidden.bs.modal', function (e) {
      if (_this.error) {
        _this.error = false;
        $(_this.script).remove();
        _this.load = false;
      }
    });
  },
  data: function data() {
    return {
      open: false,
      error: false,
      pollUrl: null,
      paymentMethod: null,
      amount: 100,
      phone: null,
      phoneClone: null,
      timerCount: 60,
      timerEnabled: false,
      payment_ref: null,
      paynowUrl: null,
      clicknpayUrl: null,
      script: null
    };
  },
  methods: {
    finalise: function finalise(payment_ref) {
      var _this2 = this;
      console.log(payment_ref);
      $.notify({
        icon: "nc-icon nc-check-2",
        message: "Paid Joining Fee Successfully. Please wait to be redirected to reset password."
      }, {
        type: "primary",
        timer: 5000,
        placement: {
          from: "top",
          align: "right"
        }
      });
      this.payment_ref = payment_ref;
      setTimeout(function () {
        return _this2.$refs.form.submit();
      }, 80);
    },
    loadError: function loadError() {
      this.error = true;
    },
    loadPayPal: function loadPayPal(e) {
      var _this3 = this;
      this.open = true;
      console.log(e, "Loaded Successfully");
      if (typeof paypal === "undefined") {
        this.error = true;
        return;
      }
      paypal.Buttons({
        createOrder: function createOrder(data, action) {
          _this3.amount = parseFloat(100);
          _this3.amount = _this3.amount;
          _this3.amount = _this3.amount.toFixed(2);
          _this3.amount1 = _this3.amount / 0.971 + 0.31;
          _this3.amount1 = _this3.amount1.toFixed(2);
          console.log("Paypal payment of: ", _this3.amount);
          return action.order.create({
            application_context: {
              brand_name: "Funeral Cover Association",
              user_action: "PAY_NOW",
              shipping_preference: "NO_SHIPPING"
            },
            purchase_units: [{
              description: "Joining fee for applicant: ".concat(_this3.user.id, " (").concat(_this3.user.first_name, ")"),
              amount: {
                currency_code: "GBP",
                value: _this3.amount1
              }
            }]
          });
        },
        onApprove: function onApprove(data, actions) {
          return actions.order.capture().then(function (details) {
            if (details.status == "COMPLETED") {
              _this3.orderID = data.orderID;
              _this3.finalise(data.orderID);
            } else {
              $.notify({
                icon: "nc-icon nc-simple-remove",
                message: "Error saving your payment, contact support with ID: ".concat(data.orderID)
              }, {
                type: "danger",
                timer: 8000,
                placement: {
                  from: "top",
                  align: "right"
                }
              });
            }
          });
        }
      }).render(this.$refs.paypal);
    },
    pay: function pay(url) {
      var _this$phone,
        _this4 = this;
      console.log(this.phone.length);
      if (this.phone == null || ((_this$phone = this.phone) === null || _this$phone === void 0 ? void 0 : _this$phone.toString().length) < 8) {
        return $.notify({
          icon: "nc-icon nc-check-2",
          message: "Enter Valid Ecocash Number."
        }, {
          type: "warning",
          timer: 5000,
          placement: {
            from: "top",
            align: "right"
          }
        });
      } else {
        var loader = this.$loading.show({
          backgroundColor: "#000",
          color: "#ae2227",
          canCancel: false
        });
        this.phoneClone = this.phone;
        axios__WEBPACK_IMPORTED_MODULE_0___default.a.get(url).then(function (response) {
          console.log(response.data);
          loader.hide();
          _this4.pollUrl = response.data.pollUrl;
          _this4.timerEnabled = true;
        })["catch"](function (error) {
          console.error('Error fetching data:', error);
          loader.hide();
        });
      }
    },
    sleep: function sleep(ms) {
      return new Promise(function (resolve) {
        return setTimeout(resolve, ms);
      });
    },
    checkPaymment: function checkPaymment() {
      var _this5 = this;
      this.timerCount = 60;
      this.timerEnabled = false;
      var loader = this.$loading.show({
        backgroundColor: "#000",
        color: "#ae2227",
        canCancel: false
      });
      var url = '/api/clicknpay/deposit-check/' + this.phoneClone + '/' + this.pollUrl;
      axios__WEBPACK_IMPORTED_MODULE_0___default.a.get(url).then(function (response) {
        console.log(response.data);
        loader.hide();
        _this5.timerCount = 60;
        if (response.data.status == "COMPLETED") {
          $.notify({
            icon: "nc-icon nc-check-2",
            message: response.data.message
          }, {
            type: "primary",
            timer: 5000,
            placement: {
              from: "top",
              align: "right"
            }
          });
          _this5.sleep(4000).then(function () {
            console.log(" HIe");
            window.location.href = "/login";
          });
        } else {
          $.notify({
            icon: "nc-icon nc-check-2",
            message: "Payment pending."
          }, {
            type: "warning",
            timer: 5000,
            placement: {
              from: "top",
              align: "right"
            }
          });
          _this5.timerEnabled = true;
        }
      })["catch"](function (error) {
        console.error('Error fetching data:', error);
        loader.hide();
        _this5.timerEnabled = true;
      });
    }
  },
  watch: {
    timerEnabled: function timerEnabled(value) {
      var _this6 = this;
      if (value) {
        setTimeout(function () {
          _this6.timerCount--;
        }, 1000);
      }
    },
    timerCount: {
      handler: function handler(value) {
        var _this7 = this;
        if (value > 0 && this.timerEnabled) {
          setTimeout(function () {
            _this7.timerCount--;
          }, 1000);
        } else if (this.timerEnabled) {
          this.timerCount = 60;
          this.timerEnabled = false;
          this.checkPaymment();
        }
      },
      immediate: true // This ensures the watcher is triggered upon creation
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04&":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04& ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "modal fade",
    attrs: {
      id: "subscriptionPaymentModal",
      tabindex: "-1",
      role: "dialog",
      "aria-labelledby": "subscriptionPaymentModalTitle",
      "aria-hidden": "true"
    }
  }, [_c("div", {
    staticClass: "modal-dialog",
    attrs: {
      role: "document"
    }
  }, [_c("div", {
    staticClass: "modal-content"
  }, [_c("div", {
    staticClass: "modal-header"
  }, [_vm.pollUrl == null ? _c("h5", {
    staticClass: "modal-title",
    attrs: {
      id: "subscriptionPaymentModalTitle"
    }
  }, [_vm._v("Subscription Payment")]) : _vm._e(), _vm._v(" "), _vm.pollUrl != null ? _c("h5", {
    staticClass: "modal-title",
    attrs: {
      id: "subscriptionPaymentModalTitle"
    }
  }, [_vm._v("Checking Payment In  " + _vm._s(_vm.timerCount) + "s ")]) : _vm._e(), _vm._v(" "), _vm._m(0)]), _vm._v(" "), _vm.pollUrl == null ? _c("div", {
    staticClass: "modal-body"
  }, [_c("div", {
    staticClass: "col-12 mb-2"
  }, [_c("label", {
    attrs: {
      "for": "_amount"
    }
  }, [_vm._v("Deposit Amount")]), _vm._v(" "), _c("p", [_vm._v("Please note payment will include additional transaction charges that are not part of the deposit.")]), _vm._v(" "), _c("div", {
    staticClass: "input-group mb-3"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model.number",
      value: _vm.amount,
      expression: "amount",
      modifiers: {
        number: true
      }
    }],
    staticClass: "form-control",
    attrs: {
      id: "_amount",
      value: "3",
      type: "number",
      placeholder: "Amount",
      "aria-label": "Amount",
      "aria-describedby": "amount"
    },
    domProps: {
      value: _vm.amount
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.amount = _vm._n($event.target.value);
      },
      blur: function blur($event) {
        return _vm.$forceUpdate();
      }
    }
  })])]), _vm._v(" "), _c("h5", {
    staticClass: "h6"
  }, [_vm._v("Payment method:")]), _vm._v(" "), _vm.paymentMethod == "clicknpay" ? _c("div", {
    staticClass: "col-12 mb-2"
  }, [_c("label", {
    attrs: {
      "for": "_phone"
    }
  }, [_vm._v("Enter ecocash number")]), _vm._v(" "), _c("div", {
    staticClass: "input-group mb-3"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model.number",
      value: _vm.phone,
      expression: "phone",
      modifiers: {
        number: true
      }
    }],
    staticClass: "form-control",
    attrs: {
      id: "_phone",
      value: "3",
      type: "phone",
      placeholder: "Phone Number",
      "aria-label": "Phone Number",
      "aria-describedby": "Phone Number"
    },
    domProps: {
      value: _vm.phone
    },
    on: {
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.phone = _vm._n($event.target.value);
      },
      blur: function blur($event) {
        return _vm.$forceUpdate();
      }
    }
  })])]) : _vm._e(), _vm._v(" "), _vm.paymentMethod == "clicknpay" ? _c("button", {
    staticClass: "btn btn-success w-100",
    on: {
      click: function click($event) {
        return _vm.pay(_vm.clicknpayUrl + "/" + _vm.amount + "/" + _vm.phone);
      }
    }
  }, [_vm._v("\n          Make Payment\n        ")]) : _vm._e(), _vm._v(" "), _c("br"), _c("br"), _vm.paymentMethod != "clicknpay" ? _c("a", {
    staticClass: "btn btn-success w-100",
    attrs: {
      href: _vm.paynowUrl + "/" + _vm.amount
    }
  }, [_vm._v("\n          Paynow\n        ")]) : _vm._e()]) : _vm._e(), _vm._v(" "), _vm.pollUrl != null ? _c("div", {
    staticClass: "modal-body"
  }, [_vm._m(1), _vm._v(" "), _vm.open ? _c("h5", {
    staticClass: "h6"
  }, [_vm._v("Checking in: " + _vm._s(_vm.timerCount))]) : _vm._e(), _vm._v(" "), _c("button", {
    staticClass: "btn btn-success w-100",
    on: {
      click: function click($event) {
        return _vm.checkPaymment();
      }
    }
  }, [_vm._v("\n          Check Now\n        ")])]) : _vm._e()])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("button", {
    staticClass: "close",
    attrs: {
      type: "button",
      "data-dismiss": "modal",
      "aria-label": "Close"
    }
  }, [_c("span", {
    attrs: {
      "aria-hidden": "true"
    }
  }, [_vm._v("×")])]);
}, function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", {
    staticClass: "col-12 mb-2"
  }, [_c("label", {
    attrs: {
      "for": "_amount"
    }
  }, [_vm._v("Waiting for Payment")])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js":
/*!********************************************************************!*\
  !*** ./node_modules/vue-loader/lib/runtime/componentNormalizer.js ***!
  \********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return normalizeComponent; });
/* globals __VUE_SSR_CONTEXT__ */

// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
// This module is a runtime utility for cleaner component module output and will
// be included in the final webpack user bundle.

function normalizeComponent(
  scriptExports,
  render,
  staticRenderFns,
  functionalTemplate,
  injectStyles,
  scopeId,
  moduleIdentifier /* server only */,
  shadowMode /* vue-cli only */
) {
  // Vue.extend constructor export interop
  var options =
    typeof scriptExports === 'function' ? scriptExports.options : scriptExports

  // render functions
  if (render) {
    options.render = render
    options.staticRenderFns = staticRenderFns
    options._compiled = true
  }

  // functional template
  if (functionalTemplate) {
    options.functional = true
  }

  // scopedId
  if (scopeId) {
    options._scopeId = 'data-v-' + scopeId
  }

  var hook
  if (moduleIdentifier) {
    // server build
    hook = function (context) {
      // 2.3 injection
      context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
      // 2.2 with runInNewContext: true
      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__
      }
      // inject component styles
      if (injectStyles) {
        injectStyles.call(this, context)
      }
      // register component module identifier for async chunk inferrence
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier)
      }
    }
    // used by ssr in case component is cached and beforeCreate
    // never gets called
    options._ssrRegister = hook
  } else if (injectStyles) {
    hook = shadowMode
      ? function () {
          injectStyles.call(
            this,
            (options.functional ? this.parent : this).$root.$options.shadowRoot
          )
        }
      : injectStyles
  }

  if (hook) {
    if (options.functional) {
      // for template-only hot-reload because in that case the render fn doesn't
      // go through the normalizer
      options._injectStyles = hook
      // register for functional component in vue file
      var originalRender = options.render
      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context)
        return originalRender(h, context)
      }
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]
    }
  }

  return {
    exports: scriptExports,
    options: options
  }
}


/***/ }),

/***/ "./resources/js/components/member/SubscriptionPaymentModal.vue":
/*!*********************************************************************!*\
  !*** ./resources/js/components/member/SubscriptionPaymentModal.vue ***!
  \*********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04& */ "./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04&");
/* harmony import */ var _SubscriptionPaymentModal_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./SubscriptionPaymentModal.vue?vue&type=script&lang=js& */ "./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _SubscriptionPaymentModal_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__["render"],
  _SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/components/member/SubscriptionPaymentModal.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js&":
/*!**********************************************************************************************!*\
  !*** ./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SubscriptionPaymentModal_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./SubscriptionPaymentModal.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_SubscriptionPaymentModal_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04&":
/*!****************************************************************************************************!*\
  !*** ./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04& ***!
  \****************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../../node_modules/vue-loader/lib??vue-loader-options!./SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/member/SubscriptionPaymentModal.vue?vue&type=template&id=e2a38c04&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_SubscriptionPaymentModal_vue_vue_type_template_id_e2a38c04___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);

<?php $__env->startSection('page_header'); ?>
<div class="container-fluid">
    <h1 class="page-title">
        <i class="<?php echo e($dataType->icon); ?>"></i> <?php echo e($dataType->getTranslatedAttribute('display_name_plural')); ?>

    </h1>
    <?php if($usesSoftDeletes): ?>
        <input type="checkbox" <?php if($showSoftDeleted): ?> checked <?php endif; ?> id="show_soft_deletes" data-toggle="toggle" data-on="Hide Terminated" data-off="Show Terminated">
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <?php echo \Illuminate\View\Factory::parentPlaceholder('content'); ?>
    <input type="hidden" id="total">
<?php $__env->stopSection(); ?>
<?php $__env->startSection('javascript'); ?>
<?php echo \Illuminate\View\Factory::parentPlaceholder('javascript'); ?>
<script>
    $( document ).ready(function() {
        table = $('#dataTable').dataTable();
        table.fnDestroy();
        $('#dataTable').DataTable({
            dom: 'lBfrtip',
            lengthMenu: [50,100,500,1000,2000,5000,10000,50000,100000],
            buttons: [
                {
                    text: 'Download PDF',
                    className: 'btn btn-default mr-15',
                    download: 'open',
                    title: "Funeral Cover Members",
                    filename: 'Corp24ChemaMembers',
                    extend: 'pdfHtml5', //'excelHtml5'
                    exportOptions: {
                        columns: "thead th:not(.actions)",
                        orthogonal: 'export'
                    }
                }
        ]
        });
        $('.mr-15').css('margin-left', '15px');
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('voyager::bread.browse', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/vendor/voyager/members/browse.blade.php ENDPATH**/ ?>

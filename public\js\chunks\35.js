(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[35],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js&":
/*!******************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js& ***!
  \******************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vuelidate/lib/validators */ "./node_modules/vuelidate/lib/validators/index.js");
/* harmony import */ var vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/api */ "./resources/js/services/api.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }


var mustBeTrue = function mustBeTrue(value) {
  return value;
};
/* harmony default export */ __webpack_exports__["default"] = ({
  props: ['forms'],
  data: function data() {
    return {
      submitted: false,
      form: {}
    };
  },
  validations: {
    form: {
      terms_read: {
        required: vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__["required"],
        mustBeTrue: mustBeTrue
      },
      details_true: {
        required: vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__["required"],
        mustBeTrue: mustBeTrue
      },
      terms_agreed: {
        required: vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__["required"],
        mustBeTrue: mustBeTrue
      }
    }
  },
  methods: {
    submit: function submit() {
      var _this = this;
      this.submitted = true;
      this.$v.$touch();
      if (this.$v.$invalid) return;
      var loader = this.$loading.show({
        backgroundColor: "#000",
        color: "#ae2227",
        canCancel: false
      });
      var form_data = _objectSpread(_objectSpread({}, this.forms), this.form);
      Object(_services_api__WEBPACK_IMPORTED_MODULE_1__["submitJoiningDetails"])(form_data).then(function (_ref) {
        var route = _ref.route;
        loader.hide();
        if (!route) {
          return _this.$vToastify.error("Oops! Something went wrong. Try again or Contact support");
        }
        window.location.replace(route);
      })["catch"](function (err) {
        var msg = "Oops! Failed to submit. Try again or contact support";
        try {
          var _err$response$data = err.response.data,
            errors = _err$response$data.errors,
            message = _err$response$data.message;
          if (message) {
            msg = message;
          }
          if (errors) {
            Object.keys(errors).forEach(function (e) {
              setTimeout(function () {
                _this.$vToastify.error(errors[e][0]);
              }, 1000);
            });
          }
        } catch (error) {}
        _this.$vToastify.error(msg);
        loader.hide();
      });
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03&":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03& ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", [_c("div", {
    staticClass: "custom-control custom-checkbox mt-3 mr-sm-2"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.form.terms_read,
      expression: "form.terms_read"
    }],
    staticClass: "custom-control-input",
    "class": {
      "is-invalid": _vm.submitted && !_vm.form.terms_read
    },
    attrs: {
      id: "const1",
      type: "checkbox"
    },
    domProps: {
      checked: Array.isArray(_vm.form.terms_read) ? _vm._i(_vm.form.terms_read, null) > -1 : _vm.form.terms_read
    },
    on: {
      change: function change($event) {
        var $$a = _vm.form.terms_read,
          $$el = $event.target,
          $$c = $$el.checked ? true : false;
        if (Array.isArray($$a)) {
          var $$v = null,
            $$i = _vm._i($$a, $$v);
          if ($$el.checked) {
            $$i < 0 && _vm.$set(_vm.form, "terms_read", $$a.concat([$$v]));
          } else {
            $$i > -1 && _vm.$set(_vm.form, "terms_read", $$a.slice(0, $$i).concat($$a.slice($$i + 1)));
          }
        } else {
          _vm.$set(_vm.form, "terms_read", $$c);
        }
      }
    }
  }), _vm._v(" "), _vm._m(0)]), _vm._v(" "), _c("div", {
    staticClass: "custom-control custom-checkbox mt-3 mr-sm-2"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.form.details_true,
      expression: "form.details_true"
    }],
    staticClass: "custom-control-input",
    "class": {
      "is-invalid": _vm.submitted && !_vm.form.details_true
    },
    attrs: {
      id: "info1",
      type: "checkbox"
    },
    domProps: {
      checked: Array.isArray(_vm.form.details_true) ? _vm._i(_vm.form.details_true, null) > -1 : _vm.form.details_true
    },
    on: {
      change: function change($event) {
        var $$a = _vm.form.details_true,
          $$el = $event.target,
          $$c = $$el.checked ? true : false;
        if (Array.isArray($$a)) {
          var $$v = null,
            $$i = _vm._i($$a, $$v);
          if ($$el.checked) {
            $$i < 0 && _vm.$set(_vm.form, "details_true", $$a.concat([$$v]));
          } else {
            $$i > -1 && _vm.$set(_vm.form, "details_true", $$a.slice(0, $$i).concat($$a.slice($$i + 1)));
          }
        } else {
          _vm.$set(_vm.form, "details_true", $$c);
        }
      }
    }
  }), _vm._v(" "), _c("label", {
    staticClass: "custom-control-label disable-select",
    attrs: {
      "for": "info1"
    }
  }, [_vm._v("I certify that the information provided is true and correct in all respect")])]), _vm._v(" "), _c("div", {
    staticClass: "custom-control custom-checkbox mt-3 mr-sm-2"
  }, [_c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.form.terms_agreed,
      expression: "form.terms_agreed"
    }],
    staticClass: "custom-control-input",
    "class": {
      "is-invalid": _vm.submitted && !_vm.form.terms_agreed
    },
    attrs: {
      id: "resident1",
      type: "checkbox"
    },
    domProps: {
      checked: Array.isArray(_vm.form.terms_agreed) ? _vm._i(_vm.form.terms_agreed, null) > -1 : _vm.form.terms_agreed
    },
    on: {
      change: function change($event) {
        var $$a = _vm.form.terms_agreed,
          $$el = $event.target,
          $$c = $$el.checked ? true : false;
        if (Array.isArray($$a)) {
          var $$v = null,
            $$i = _vm._i($$a, $$v);
          if ($$el.checked) {
            $$i < 0 && _vm.$set(_vm.form, "terms_agreed", $$a.concat([$$v]));
          } else {
            $$i > -1 && _vm.$set(_vm.form, "terms_agreed", $$a.slice(0, $$i).concat($$a.slice($$i + 1)));
          }
        } else {
          _vm.$set(_vm.form, "terms_agreed", $$c);
        }
      }
    }
  }), _vm._v(" "), _c("label", {
    staticClass: "custom-control-label disable-select",
    attrs: {
      "for": "resident1"
    }
  }, [_vm._v("I accept the terms and conditions")])]), _vm._v(" "), _c("div", {
    staticClass: "row mt-3"
  }, [_c("p", [_vm._v("You will be required to pay your subscription fee after form submission.")]), _vm._v(" "), _c("div", {
    staticClass: "col-12 text-center",
    on: {
      click: function click($event) {
        return _vm.submit();
      }
    }
  }, [_c("button", {
    staticClass: "btn radix-btn"
  }, [_vm._v("Submit Details")])])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("label", {
    staticClass: "custom-control-label disable-select",
    attrs: {
      "for": "const1"
    }
  }, [_vm._v("\n      I have read and understand the\n      "), _c("a", {
    attrs: {
      href: ""
    }
  }, [_vm._v("Terms and Conditions")]), _vm._v(" of Funeral Cover Association\n    ")]);
}];
render._withStripped = true;


/***/ }),

/***/ "./resources/js/components/applicant/TermsForm.vue":
/*!*********************************************************!*\
  !*** ./resources/js/components/applicant/TermsForm.vue ***!
  \*********************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TermsForm.vue?vue&type=template&id=329aaf03& */ "./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03&");
/* harmony import */ var _TermsForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TermsForm.vue?vue&type=script&lang=js& */ "./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _TermsForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__["render"],
  _TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/components/applicant/TermsForm.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js&":
/*!**********************************************************************************!*\
  !*** ./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TermsForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./TermsForm.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/TermsForm.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_TermsForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03&":
/*!****************************************************************************************!*\
  !*** ./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03& ***!
  \****************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../../node_modules/vue-loader/lib??vue-loader-options!./TermsForm.vue?vue&type=template&id=329aaf03& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/TermsForm.vue?vue&type=template&id=329aaf03&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_TermsForm_vue_vue_type_template_id_329aaf03___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
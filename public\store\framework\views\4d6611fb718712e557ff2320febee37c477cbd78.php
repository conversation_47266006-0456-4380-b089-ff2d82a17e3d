<?php $__env->startSection('page'); ?>
Sign In
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <!-- Register Thumbnail-->
      <div class="d-none d-lg-block col-12 col-lg-6">
        <div class="register-thumbnail mb-50">
            <div class="mx-auto" style="width: 66%;">
                <img src="<?php echo e(asset('storage/bg/login.png')); ?>" alt="">
            </div>
        </div>
      </div>
      <!-- Register Card-->
      <div class="col-12 col-lg-6">
       <?php if($errors->any()): ?>
        <div class="alert alert-danger" role="alert">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <?php endif; ?>
        <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
          <div class="card-body">
            <h5>Initial Subscription fee payment: </h5>
            <h4> <?php echo e($applicant->first_name); ?> <?php echo e($applicant->last_name); ?></h4>

            <ul>
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
             <li style="list-style: initial;"><?php echo e($s->title); ?> $<?php echo e($s->price); ?>/month<li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <ul>
            With <?php echo e(count($applicant->nominees??[])); ?> additional members <br><br>

            <h5>Amount payable $<?php echo e($total); ?></h5>
            <!-- <div class="collapse navbar-collapse justify-content-end" id="navigation"> -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a type="button" data-toggle="modal" data-target="#joiningPaymentModal"
                        class="nav-link btn btn-success btn-sm" href="javascript:;">
                        <i class="nc-icon nc-share-66"></i>
                        Make payment
                    </a>
                </li>
            </ul>
        <!-- </div> -->
            <!-- Register Form-->

          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<joining-payment-modal
            :user="<?php echo e($applicant); ?>"
            :client_id="'<?php echo e(env('PAYPAL_CLIENT')); ?>'"
            :route="'<?php echo e(route('application.submit_deposit', $applicant->id)); ?>'">
            <?php echo csrf_field(); ?>
        </joining-payment-modal>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/pages/joinpayment.blade.php ENDPATH**/ ?>
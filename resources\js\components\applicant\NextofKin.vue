<template>
  <div>
    <!-- <h6>Enter Full name as it appears on their national ID</h6> -->
    <form @submit.prevent="handelNOK()">
      <div class="row">
        <div class="col-12">
          <label class="font-weight-bold" for="nok_full_name">Next of kin's Full name:</label>
        </div>
        <div class="col-12 col-lg-6">
          <input
            class="form-control mb-30 bg-gray"
            :class="{ 'is-invalid': submitted && $v.form.nok_full_name.$error }"
            type="text"
            name="nok_full_name"
            v-model="form.nok_full_name"
            id="nok_full_name"
            placeholder="Full Name"
          />
        </div>

        <div class="col-12 col-lg-6">
          <input
            class="form-control mb-30 bg-gray"
            id="nok_email"
            type="email"
            v-model="form.nok_email"
            placeholder="Email Address (Optional)"
          />
        </div>
        <div class="col-12 col-lg-6">
          <input
            class="form-control bg-gray"
            type="tel"
            v-model="nok_phone"
            name="nok_phone"
            maxlength="20"
            placeholder="Mobile Number"
          />
          <div class="mb-30 mt-1"></div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <label class="font-weight-bold" for="street">Next of kin's Physical Address:</label>
        </div>
        <div class="col-12 col-lg-6">
          <input
            class="form-control mb-30 bg-gray"
            :class="{ 'is-invalid': submitted && $v.form.nok_street.$error }"
            type="text"
            v-model="form.nok_street"
            id="nok_street"
            name="nok_street"
            placeholder="Address Line 1"
          />
        </div>
        <!-- <div class="col-12 col-lg-6">
          <input
            class="form-control mb-30 bg-gray"
            type="text"
            v-model="form.nok_apartment"
            id="nok_apartment"
            name="nok_apartment"
            placeholder="Address Line 2"
          />
        </div> -->
        <div class="col-12 col-lg-6">
          <input
            class="form-control mb-30 bg-gray"
            type="text"
            :class="{ 'is-invalid': submitted && $v.form.nok_city.$error }"
            v-model="form.nok_city"
            name="nok_city"
            placeholder="City"
          />
        </div>
        <div class="col-12 col-lg-6">
          <country-select
            v-model="form.nok_country"
            :class="{ 'is-invalid': submitted && $v.form.nok_country.$error }"
            class="custom-select form-control mb-30 bg-gray"
            :country="form.nok_country"
            :countryName="true"
            topCountry="ZW"
          />
        </div>
        <!-- <div class="col-12 col-lg-4">
          <input
            class="form-control mb-30 bg-gray"
            type="text"
            name="nok_id"
            v-model="form.nok_id"
            placeholder="Id Number"
          />
        </div> -->
      </div>
      <div class="row">
        <div class="col-4 text-right">
          <button class="btn radix-btn" @click="skip()" type="button">Skip Step</button>
        </div>
        <div class="col-4 text-right">
          <button class="btn radix-btn" type="submit">Next Step</button>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
import { required, email } from "vuelidate/lib/validators";

export default {
  components: {
    Datepicker: () => import("vuejs-datepicker")
  },
  data() {
    return {
      submitted: false,
      nok_phone: "",
      form: {}
    };
  },
  validations: {
    form: {
      nok_full_name: { required },
      nok_email: { email },
      nok_phone: {  },
      nok_street: { required },
      nok_city: { required },
      nok_country: { required }
    }
  },

  methods: {
    skip(){
        this.submitted = true;
        return this.$emit("done");
    },
    handelNOK() {

        console.log("wii");
      this.submitted = true;
      this.$v.$touch();
      if (this.$v.$invalid) {
        this.scrollErrorToView();
        return;
      }

      // Handle empty email field
      if(this.form.nok_email?.trim()=="") this.form.nok_email = null;

      this.$emit("done", this.form);
    },


    scrollErrorToView() {
      setTimeout(() => {
        const elem = $(`.is-invalid`).first();
        $(elem).focus();
      }, 100);
    }
  },
  watch: {
    nok_phone(p) {
      this.form.nok_phone = p;
    }
  }
};
</script>

(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[18],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Footer.vue?vue&type=script&lang=js&":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/Footer.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony default export */ __webpack_exports__["default"] = ({});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Footer.vue?vue&type=template&id=61a7c374&":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/Footer.vue?vue&type=template&id=61a7c374& ***!
  \***************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm._m(0);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c("footer", {
    staticClass: "footer-area footer2 section-padding-120"
  }, [_c("div", {
    staticClass: "container"
  }, [_c("div", {
    staticClass: "row justify-content-between"
  }, [_c("div", {
    staticClass: "col-12 col-sm-10 col-lg-3"
  }, [_c("div", {
    staticClass: "footer-widget-area mb-70"
  }, [_c("a", {
    staticClass: "d-block mb-4",
    attrs: {
      href: "index.html"
    }
  }, [_c("img", {
    attrs: {
      src: "img/core-img/logo-white.png",
      alt: ""
    }
  })]), _vm._v(" "), _c("p", [_vm._v("It's crafted with the latest trend of design & coded with all modern approaches.")]), _vm._v(" "), _c("div", {
    staticClass: "newsletter-form"
  }, [_c("form", {
    attrs: {
      action: "#"
    }
  }, [_c("input", {
    staticClass: "form-control",
    attrs: {
      type: "email",
      placeholder: "Enter email & press enter"
    }
  }), _vm._v(" "), _c("button", {
    staticClass: "btn d-none",
    attrs: {
      type: "submit"
    }
  }, [_vm._v("Go")])])]), _vm._v(" "), _c("div", {
    staticClass: "footer-social-icon d-flex align-items-center"
  }, [_c("a", {
    attrs: {
      href: "#",
      "data-toggle": "tooltip",
      "data-placement": "top",
      title: "Facbook"
    }
  }, [_c("i", {
    staticClass: "fa fa-facebook"
  })]), _c("a", {
    attrs: {
      href: "#",
      "data-toggle": "tooltip",
      "data-placement": "top",
      title: "Twitter"
    }
  }, [_c("i", {
    staticClass: "fa fa-twitter"
  })]), _c("a", {
    attrs: {
      href: "#",
      "data-toggle": "tooltip",
      "data-placement": "top",
      title: "Instagram"
    }
  }, [_c("i", {
    staticClass: "fa fa-instagram"
  })]), _c("a", {
    attrs: {
      href: "#",
      "data-toggle": "tooltip",
      "data-placement": "top",
      title: "Linkedin"
    }
  }, [_c("i", {
    staticClass: "fa fa-linkedin"
  })]), _c("a", {
    attrs: {
      href: "#",
      "data-toggle": "tooltip",
      "data-placement": "top",
      title: "Youtube"
    }
  }, [_c("i", {
    staticClass: "fa fa-youtube"
  })])])])]), _vm._v(" "), _c("div", {
    staticClass: "col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2"
  }, [_c("div", {
    staticClass: "footer-widget-area mb-70"
  }, [_c("h5", {
    staticClass: "widget-title"
  }, [_vm._v("Important Links")]), _vm._v(" "), _c("ul", [_c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Terms & Conditions")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("About Licences")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Help & Support")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Careers")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Privacy Policy")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Community & Forum")])])])])]), _vm._v(" "), _c("div", {
    staticClass: "col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2"
  }, [_c("div", {
    staticClass: "footer-widget-area mb-70"
  }, [_c("h5", {
    staticClass: "widget-title"
  }, [_vm._v("Our Products")]), _vm._v(" "), _c("ul", [_c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Apland Landing")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Ecaps Admin")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Bigshop Ecommerce")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Classy Multipurpose")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Educamp Education")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Champ Portfolio")])])])])]), _vm._v(" "), _c("div", {
    staticClass: "col-12 col-sm-6 col-md-4 col-lg-3 col-xl-2"
  }, [_c("div", {
    staticClass: "footer-widget-area mb-70"
  }, [_c("h5", {
    staticClass: "widget-title"
  }, [_vm._v("My Account")]), _vm._v(" "), _c("ul", [_c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Community & Forum")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("About Licences")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Careers")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Terms & Conditions")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Privacy Policy")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Help & Support")])])])])])])]), _vm._v(" "), _c("div", {
    staticClass: "container"
  }, [_c("div", {
    staticClass: "row align-items-center"
  }, [_c("div", {
    staticClass: "col-12 col-md-6 col-lg-5"
  }, [_c("div", {
    staticClass: "footer--content-text"
  }, [_c("p", {
    staticClass: "mb-0"
  }, [_vm._v("All rights reserved by "), _c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Designing World")])])])]), _vm._v(" "), _c("div", {
    staticClass: "col-12 col-md-6 col-lg-5"
  }, [_c("div", {
    staticClass: "footer-nav"
  }, [_c("ul", {
    staticClass: "d-flex"
  }, [_c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Privacy Policy")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Terms & Conditions")])]), _vm._v(" "), _c("li", [_c("a", {
    attrs: {
      href: "#",
      target: "_blank"
    }
  }, [_vm._v("Support")])])])])]), _vm._v(" "), _c("div", {
    staticClass: "col-12 col-lg-2"
  }, [_c("div", {
    staticClass: "language-dropdown text-center text-lg-right mt-4 mt-lg-0"
  }, [_c("div", {
    staticClass: "btn-group dropup"
  }, [_c("button", {
    staticClass: "btn radix-btn-2 dropdown-toggle text-white",
    attrs: {
      type: "button",
      "data-toggle": "dropdown",
      "aria-haspopup": "true",
      "aria-expanded": "false"
    }
  }, [_vm._v("Language")]), _vm._v(" "), _c("div", {
    staticClass: "dropdown-menu dropdown-menu-right"
  }, [_c("a", {
    staticClass: "dropdown-item",
    attrs: {
      href: "#"
    }
  }, [_c("span", {
    staticClass: "mr-2 flag-icon flag-icon-sa"
  }), _vm._v("Arabic")]), _c("a", {
    staticClass: "dropdown-item",
    attrs: {
      href: "#"
    }
  }, [_c("span", {
    staticClass: "mr-2 flag-icon flag-icon-bd"
  }), _vm._v("Bengali")]), _c("a", {
    staticClass: "dropdown-item",
    attrs: {
      href: "#"
    }
  }, [_c("span", {
    staticClass: "mr-2 flag-icon flag-icon-us"
  }), _vm._v("English")]), _c("a", {
    staticClass: "dropdown-item",
    attrs: {
      href: "#"
    }
  }, [_c("span", {
    staticClass: "mr-2 flag-icon flag-icon-my"
  }), _vm._v("Malay")]), _c("a", {
    staticClass: "dropdown-item",
    attrs: {
      href: "#"
    }
  }, [_c("span", {
    staticClass: "mr-2 flag-icon flag-icon-es"
  }), _vm._v("Spanish")])])])])])])])]);
}];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js":
/*!********************************************************************!*\
  !*** ./node_modules/vue-loader/lib/runtime/componentNormalizer.js ***!
  \********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "default", function() { return normalizeComponent; });
/* globals __VUE_SSR_CONTEXT__ */

// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).
// This module is a runtime utility for cleaner component module output and will
// be included in the final webpack user bundle.

function normalizeComponent(
  scriptExports,
  render,
  staticRenderFns,
  functionalTemplate,
  injectStyles,
  scopeId,
  moduleIdentifier /* server only */,
  shadowMode /* vue-cli only */
) {
  // Vue.extend constructor export interop
  var options =
    typeof scriptExports === 'function' ? scriptExports.options : scriptExports

  // render functions
  if (render) {
    options.render = render
    options.staticRenderFns = staticRenderFns
    options._compiled = true
  }

  // functional template
  if (functionalTemplate) {
    options.functional = true
  }

  // scopedId
  if (scopeId) {
    options._scopeId = 'data-v-' + scopeId
  }

  var hook
  if (moduleIdentifier) {
    // server build
    hook = function (context) {
      // 2.3 injection
      context =
        context || // cached call
        (this.$vnode && this.$vnode.ssrContext) || // stateful
        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional
      // 2.2 with runInNewContext: true
      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {
        context = __VUE_SSR_CONTEXT__
      }
      // inject component styles
      if (injectStyles) {
        injectStyles.call(this, context)
      }
      // register component module identifier for async chunk inferrence
      if (context && context._registeredComponents) {
        context._registeredComponents.add(moduleIdentifier)
      }
    }
    // used by ssr in case component is cached and beforeCreate
    // never gets called
    options._ssrRegister = hook
  } else if (injectStyles) {
    hook = shadowMode
      ? function () {
          injectStyles.call(
            this,
            (options.functional ? this.parent : this).$root.$options.shadowRoot
          )
        }
      : injectStyles
  }

  if (hook) {
    if (options.functional) {
      // for template-only hot-reload because in that case the render fn doesn't
      // go through the normalizer
      options._injectStyles = hook
      // register for functional component in vue file
      var originalRender = options.render
      options.render = function renderWithStyleInjection(h, context) {
        hook.call(context)
        return originalRender(h, context)
      }
    } else {
      // inject component registration as beforeCreate hook
      var existing = options.beforeCreate
      options.beforeCreate = existing ? [].concat(existing, hook) : [hook]
    }
  }

  return {
    exports: scriptExports,
    options: options
  }
}


/***/ }),

/***/ "./resources/js/components/Footer.vue":
/*!********************************************!*\
  !*** ./resources/js/components/Footer.vue ***!
  \********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Footer.vue?vue&type=template&id=61a7c374& */ "./resources/js/components/Footer.vue?vue&type=template&id=61a7c374&");
/* harmony import */ var _Footer_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Footer.vue?vue&type=script&lang=js& */ "./resources/js/components/Footer.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _Footer_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__["render"],
  _Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/components/Footer.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/components/Footer.vue?vue&type=script&lang=js&":
/*!*********************************************************************!*\
  !*** ./resources/js/components/Footer.vue?vue&type=script&lang=js& ***!
  \*********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Footer_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib??ref--4-0!../../../node_modules/vue-loader/lib??vue-loader-options!./Footer.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Footer.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Footer_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/components/Footer.vue?vue&type=template&id=61a7c374&":
/*!***************************************************************************!*\
  !*** ./resources/js/components/Footer.vue?vue&type=template&id=61a7c374& ***!
  \***************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib??ref--4-0!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../node_modules/vue-loader/lib??vue-loader-options!./Footer.vue?vue&type=template&id=61a7c374& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Footer.vue?vue&type=template&id=61a7c374&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Footer_vue_vue_type_template_id_61a7c374___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
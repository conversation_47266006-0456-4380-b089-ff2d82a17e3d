

<?php $__env->startSection('page'); ?>
Member
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <?php if(Auth::user()->role_id==1): ?>
        <div class="col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-body ">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-warning">
                                <i class="nc-icon nc-single-02 text-warning"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">Members</p>
                                <p class="card-title"><?php echo e($members_count); ?><p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer ">
                    <hr>
                    <div class="stats">
                        <i class="fa fa-refresh"></i>
                        Update Now
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <div class="col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-body ">
                <div class="row">
                    <div class="col-5 col-md-4">
                        <div class="icon-big text-center icon-warning">
                            <i class="nc-icon nc-money-coins text-success"></i>
                        </div>
                    </div>
                    <div class="col-7 col-md-8">
                        <div class="numbers">
                            <p class="card-category">Transactions</p>
                            <a href="<?php echo e(route("members-area.deposits")); ?>" class="card-title">View</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer ">
                <hr>
                <div class="stats">
                    <i class="fa fa-calendar-o"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- column -->
    <div class="col-md-12">
        <div class="card mb-5">
            <div class="card-body">
                <h4 class="card-title"><i class="nc-icon nc-sound-wave"></i>Registered Services</h4>
            </div>
            <div class="comment-widgets">
                <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="d-flex flex-row mb-3 mt-0">
                    <div class="p-2"><img src="<?php echo e(asset('storage/'.$s?->photo)); ?>" alt width="50"
                            class="rounded-circle"></div>
                    <div class="comment-text w-100">
                        <span>
                            <h5><?php echo e(str_limit($s->title)); ?></h5>
                        </span>
                        <span class="m-b-15 d-block">
                            <?php echo e(str_limit($s->description)); ?>

                        </span>
                        <div class="comment-footer pr-2">
                            <span class="text-muted float-right">$<?php echo e($s->price); ?>/month</span>

                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="card-body">
                        <h4 >No services found</h4>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('member.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/index.blade.php ENDPATH**/ ?>
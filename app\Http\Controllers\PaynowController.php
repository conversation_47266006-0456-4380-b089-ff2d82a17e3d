<?php
//Also refer to deposits controller for deposits code

namespace App\Http\Controllers;

use App\User;
use DateTime;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Nominee;
use App\Models\Member;
use App\Http\Controllers\DepositController;
use App\Models\Applicant;
use Paynow\Payments\Paynow;
use App\Service;
use Illuminate\Http\Request;
use App\Notifications\PaymentCompleted;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\Deposit;
use App\Notifications\DepositNotification;
use Exception;
use Illuminate\Support\Facades\Notification;



class PaynowController extends Controller
{

    public function newMemberPaymentRequest($applicantId){

        $applicant =  Applicant::find($applicantId);
        if(!isset($applicant))  return abort(404);
        $member = Member::whereEmail($applicant->email)->first();
        if($member != null && $applicant->email!=null)  return back()->withErrors(["message" => "A member with that email already exists"]);

        $serviceNames = $applicant->getServiceNames();
        $servicesTotal = $applicant->getServiceTotal();
        $invoice = $applicant->invoiceJoining("".$applicantId.now());


        $paynow = new Paynow(
            env('PAYNOW_ID'),
            env('PAYNOW_KEY'),
            env('APP_URL').'paynow/order/payment-complete/'.$invoice->id,
            env('APP_URL').'api/payment-process-complete'
        );

        $payment = $paynow->createPayment( $invoice->id ,$applicant->email);
        $payment->add(($applicant->first_name)." ".strval($applicant->last_name)." for".$serviceNames, $servicesTotal);
        $payment->setDescription("New member initial payment for".strval($applicant->first_name)." ".strval($applicant->last_name)." for".$serviceNames);
        // dd($payment);
        $response = $paynow->send($payment);

        if(!$response->success){
           logger("An error occured while communicating with Paynow Service. This is a temporary issue try again later. If this error persists contact us and we will be glad to help.");
           return response()->json([
            'message' => 'Paypal failed',
            'amount'=> "",
            'responseCode'=> 200
         ]);
        }
        else{
            $invoice->poll_url = $response->pollUrl();
            $invoice->save();
            return redirect($response->redirectUrl());
        }
    }

    public function processCompletedJoiningFee(int $order, Request $request){
        $paynow = new Paynow(
            env('PAYNOW_ID'),
            env('PAYNOW_KEY'),
            env('APP_URL').'paynow/order/payment-complete/'.$order,
            env('APP_URL').'/api/payment-process-complete'
        );
        $invoice = Invoice::where('id', $order)->first();
        $status = $paynow->pollTransaction($invoice->poll_url);

        if($status->paid()){
            $memCon = new MemberController();
            $memCon->createPaidMember($invoice,$status->amount(),$invoice->poll_url, 'Paynow');
            return view('pages.recpay');
        }else return null;
    }

    public function memberDeposit(Member $member, $total){

        $invoice = Invoice::create([
            "invoice_date" =>  new DateTime(),
            "type" => "Deposit",
            "description" => "Member deposit paynow",
            "subtotal" => $total,
            "obituary_id" => 0,
            "total" => $total,
            "member_id" => $member->id,
            "status" => "unpaid",
            "due_date" => new DateTime(),
        ]);

        InvoiceItem::create([
            "title" => "Member deposit" ,
            "amount" => $total,
            "invoice_id" => $invoice->id
        ]);



        $paynow = new Paynow(
            env('PAYNOW_ID'),
            env('PAYNOW_KEY'),
            env('APP_URL').'paynow/deposit/payment-complete/'.$invoice->id,
            env('APP_URL').'api/payment-process-complete'
        );


        $payment = $paynow->createPayment( $invoice->id ,$member->email);

        $payment->add("Member Deposit", $total);

        $payment->setDescription("Member deposit");

        $response = $paynow->send($payment);

        if(!$response->success){
           logger("  <h2>An error occured while communicating with Paynow Service. This is a temporary issue try again later.
           If this error persists contact us and we will be glad to help.</h2>
            <p></p>");
            logger([$response]);
        }
        else{
            $invoice->poll_url = $response->pollUrl();
            $invoice->save();
            return redirect($response->redirectUrl());
        }
    }






    public function depositreceived($order)
    {
        $paynow = new Paynow(
            env('PAYNOW_ID'),
            env('PAYNOW_KEY'),
            env('APP_URL').'paynow/deposit/payment-complete/'.$order,
            env('APP_URL').'api/payment-process-complete'
        );

        $pollurl = Invoice::find($order)->poll_url;

        $status = $paynow->pollTransaction($pollurl);

        $order = Invoice::where('id', $order)->first();
        if($status->paid()){
                $order->status = "paid";
                $order->save();

                $member = Member::find($order->member_id);
                $user = User::find($member->user_id);

                if (!isset($member)) {
                    $amount = $order->total;
                    my_log("Deposit Received for user ID: {$user->id} {$user->name}", "System couldn't save. Please enter manually\nDeposited Amount: {$amount}");
                    logger("Deposit Received for user ID: {$user->id} {$user->name} System couldn't save. Please enter manually\nDeposited Amount: {$amount}");
                }else{

                    $member->memberDeposit($order->total, $pollurl, "Paynow", "Paynow deposit for member" );

                    try{
                        Notification::route('mail', $member->email)->notify(new DepositNotification($order->total));
                    }catch(Exception $e){
                        logger("An error occured while attempting to record a member deposit via payno. The deposit was however successfull: ".$e->getMessage());
                    }
                    return view('pages.recpay');
                }}
                else{
                    return null;
                }



    }

}

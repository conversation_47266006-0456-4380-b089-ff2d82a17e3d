/* ----------------------------------------------------------
:: Template Name: Classy Nav - Responsive Mega Menu Plugins
:: Author: Designing World
:: Author URL: https://themeforest.net/user/designing-world/portfolio
:: Version: 1.1.0
:: Created: May 16, 2018
:: Last Updated: Jan 28, 2019
---------------------------------------------------------- */

.breakpoint-on .dd-trigger::after,
.icon-classy-nav-down-arrow:before {
    content: "\e900"
}

.classy-nav-container {
    position: relative;
    z-index: 1;
    background-color: #fff;
}

.classy-navbar .nav-brand,
.classy-navbar .nav-brand:focus,
.classy-navbar .nav-brand:hover {
    font-size: 26px;
    font-weight: 500
}

.justify-content-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.justify-content-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.classy-navbar {
    width: 100%;
    height: 80px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -ms-grid-row-align: center;
    align-items: center
}

.classy-navbar .nav-brand {
    color: #232323;
    display: inline-block;
    margin-right: 30px
}

.classynav ul li {
    display: inline-block;
    clear: both;
    position: inherit;
    z-index: 10
}

.classynav ul li.cn-dropdown-item,
.classynav ul li.cn-dropdown-item ul li {
    position: relative;
    z-index: 10
}

.classynav ul li ul li {
    display: block
}

.classynav ul li ul li a {
    padding: 0 20px
}

.classynav ul li a {
    padding: 0 15px;
    display: block;
    height: 40px;
    font-size: 14px;
    line-height: 39px
}

.classycloseIcon,
.dd-trigger {
    position: absolute;
    display: none
}

.classynav ul li .megamenu li a {
    padding: 0 12px
}

.classynav ul li div.single-mega a {
    height: auto;
    line-height: 1
}

@font-face {
    font-family: classyfonts;
    src: url(../../fonts/classy-fonts.eot?fftrrv);
    src: url(../../fonts/classy-fonts.eot?fftrrv#iefix) format("embedded-opentype"), url(../../fonts/classy-fonts.ttf?fftrrv) format("truetype"), url(../../fonts/classy-fonts.woff?fftrrv) format("woff"), url(../../fonts/classy-fonts.svg?fftrrv#classyfonts) format("svg");
    font-weight: 400;
    font-style: normal
}

.icon-classy-nav-down-arrow {
    font-family: classyfonts;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.classynav ul li.has-down > a:after,
.classynav ul li.megamenu-item > a:after {
    font-family: classyfonts;
    content: "\e900";
    font-size: 12px;
    color: #000;
    padding-left: 5px;
    -webkit-transition-duration: .5s;
    transition-duration: .5s
}

.classynav ul li ul li.has-down > a::after {
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    position: absolute;
    top: 2px;
    right: 10px;
    z-index: 3
}

.dd-trigger {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: transparent;
    cursor: pointer;
    z-index: 500;
    border-radius: 0
}

.classycloseIcon {
    top: 20px;
    right: 20px;
    z-index: 12
}

.classycloseIcon .cross-wrap {
    width: 26px;
    height: 26px;
    cursor: pointer;
    position: relative
}

.classycloseIcon .cross-wrap span {
    position: absolute;
    display: block;
    width: 100%;
    height: 2px;
    border-radius: 6px;
    background: #232323
}

.classycloseIcon .cross-wrap span.top {
    top: 12px;
    left: 0;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.classycloseIcon .cross-wrap span.bottom {
    bottom: 12px;
    left: 0;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.classy-navbar-toggler {
    border: none;
    background-color: transparent;
    cursor: pointer;
    display: none
}

.classy-navbar-toggler .navbarToggler {
    display: inline-block;
    cursor: pointer
}

.classy-navbar-toggler .navbarToggler span {
    position: relative;
    background-color: #707070;
    border-radius: 3px;
    display: block;
    height: 3px;
    margin-top: 5px;
    padding: 0;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    width: 30px;
    cursor: pointer
}

.classy-navbar-toggler .navbarToggler.active span:nth-of-type(1) {
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    top: 8px
}

.classy-navbar-toggler .navbarToggler.active span:nth-of-type(2) {
    opacity: 0
}

.classy-navbar-toggler .navbarToggler.active span:nth-of-type(3) {
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    top: -8px
}

.classynav ul li .megamenu {
    position: absolute;
    width: 100%;
    left: 0;
    top: 100%;
    background-color: #fff;
    z-index: 200;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .15)
}

.classynav ul li .megamenu .single-mega.cn-col-5 {
    width: 20%;
    float: left;
    padding: 15px;
    border-right: 1px solid #f2f4f8
}

.classynav ul li .megamenu .single-mega.cn-col-5:last-of-type {
    border-right: none
}

.classynav ul li .megamenu .single-mega.cn-col-4 {
    width: 25%;
    float: left;
    padding: 15px;
    border-right: 1px solid #f2f4f8
}

.classynav ul li .megamenu .single-mega.cn-col-4:last-of-type {
    border-right: none
}

.classynav ul li .megamenu .single-mega.cn-col-3 {
    width: 33.3333334%;
    float: left;
    padding: 15px;
    border-right: 1px solid #f2f4f8
}

.classynav ul li .megamenu .single-mega.cn-col-3:last-of-type {
    border-right: none
}

.classynav ul li .megamenu .single-mega .title {
    font-size: 14px;
    border-bottom: 1px solid #f2f4f8;
    padding: 8px 12px
}

.classynav ul li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li a {
    border-bottom: 1px solid rgba(242, 244, 248, .7)
}

.classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:last-child a {
    border-bottom: none
}

.breakpoint-off .classynav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    -ms-grid-row-align: center;
    align-items: center
}

.breakpoint-off .classynav ul li .dropdown {
    width: 180px;
    position: absolute;
    background-color: #fff;
    top: 120%;
    left: 0;
    z-index: 100;
    height: auto;
    box-shadow: 0 1px 5px rgba(0, 0, 0, .1);
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    opacity: 0;
    visibility: hidden;
    padding: 10px 0
}

.breakpoint-off .classynav ul li .dropdown li .dropdown {
    top: 10px;
    left: 180px;
    z-index: 200;
    opacity: 0;
    visibility: hidden
}

.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown {
    opacity: 0;
    visibility: hidden;
    top: 120%
}

.breakpoint-off .classynav ul li:hover .dropdown {
    opacity: 1;
    visibility: visible;
    top: 100%
}

.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li .dropdown li:hover .dropdown,
.breakpoint-off .classynav ul li .dropdown li:hover .dropdown {
    opacity: 1;
    visibility: visible;
    top: -10px
}

.breakpoint-off .classynav ul li .megamenu {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    width: 100%;
    left: 0;
    top: 120%;
    background-color: #fff;
    z-index: 200;
    box-shadow: 0 1px 4px rgba(0, 0, 0, .15);
    -webkit-transition-duration: .3s;
    transition-duration: .3s
}

.breakpoint-off .classynav ul li.megamenu-item:focus .megamenu,
.breakpoint-off .classynav ul li.megamenu-item:hover .megamenu {
    top: 100%;
    visibility: visible;
    opacity: 1
}

.breakpoint-on .classy-navbar-toggler,
.breakpoint-on .classycloseIcon {
    display: block
}

.breakpoint-on .classy-navbar .classy-menu {
    background-color: #fff;
    position: fixed;
    top: 0;
    left: -340px;
    z-index: 9999;
    width: 320px;
    height: 100%;
    -webkit-transition-duration: .5s;
    transition-duration: .5s;
    padding: 0;
    box-shadow: 0 5px 20px rgba(0, 0, 0, .1);
    display: block;
    overflow-x: hidden;
    overflow-y: scroll
}

.breakpoint-on .classynav ul li .dropdown,
.breakpoint-on .classynav ul li .dropdown li .dropdown {
    width: 100%;
    top: 0;
    left: 0;
    position: relative
}

.breakpoint-on .classy-navbar .classy-menu.menu-on {
    left: 0
}

.breakpoint-on .classynav ul li {
    display: block;
    position: relative;
    clear: both;
    z-index: 10
}

.breakpoint-on .classynav ul li a {
    padding: 0 15px;
    height: 45px;
    line-height: 45px
}

.breakpoint-on .classynav ul li .dropdown {
    box-shadow: none
}

.breakpoint-on .classynav ul li.megamenu-item {
    position: relative;
    z-index: 10
}

.breakpoint-on .dd-trigger {
    height: 31px;
    width: 31px;
    background-color: #2c3e50;
    top: 7px;
    right: 15px;
    left: auto;
    display: block;
    border-radius: 2px
}

.breakpoint-on .classynav ul li .dropdown,
.breakpoint-on .classynav ul li .megamenu,
.breakpoint-on .classynav ul li.has-down > a::after,
.breakpoint-on .classynav ul li.megamenu-item > a::after {
    display: none
}

.breakpoint-on .dd-trigger::after {
    font-family: classyfonts;
    font-size: 12px;
    color: #fff;
    -webkit-transition-duration: .5s;
    transition-duration: .5s;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.breakpoint-on .classynav {
    padding-top: 80px;
    margin-bottom: 80px
}

.breakpoint-on .classynav ul {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
    width: 100%
}

.breakpoint-on .classynav ul li .megamenu {
    position: relative;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 200;
    box-shadow: none
}

.breakpoint-on .classynav ul li .megamenu .single-mega.cn-col-3,
.breakpoint-on .classynav ul li .megamenu .single-mega.cn-col-4,
.breakpoint-on .classynav ul li .megamenu .single-mega.cn-col-5 {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #f2f4f8
}

.breakpoint-on .classynav > ul > li > a {
    background-color: #f2f4f8;
    border-bottom: 1px solid rgba(255, 255, 255, .5)
}

.breakpoint-on .classynav ul li ul.dropdown li ul li {
    margin-left: 15px
}

.breakpoint-on .classynav ul li.has-down.active > .dd-trigger,
.breakpoint-on .classynav ul li.megamenu-item.active > .dd-trigger {
    background-color: #27ae60;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

/*# sourceMappingURL=classy-nav.css.map */
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/join','ApplicantController@store');

Route::get('/member/{memberId}','Member<PERSON>ontroller@findById');
Route::post('/clicknpay/myplan/ssd-deposit','ClicknPayController@chatbotMemberDeposit');
Route::post('/clicknpay/member/ssd-deposit','ClicknPayController@ssdMemberDeposit');

Route::get('/clicknpay/deposit/{member}/{total}/{phone}','ClicknPayController@memberDeposit');
Route::get('/clicknpay/deposit-check/{phone}/{clientCorrelator}','ClicknPayController@checkMemberDeposit');

Route::get('/clicknpay/pay-joining/{applicantId}/{phone}','ClicknPayController@newMemberPayment');
Route::get('/clicknpay/pay-joining/check/{phone}/{clientCorrelator}','ClicknPayController@checkNewMemberPayment');
Route::post('/clicknpay/chatbot/new-member','ClicknPayController@checkChatBotNewMemberPayment');

Route::post('/unique-email', 'ApplicantController@checkEmail');

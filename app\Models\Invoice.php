<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
    use SoftDeletes;

    protected $fillable = [
        "id",
        "user_id",
        "applicant_id",
        "member_id",
        "obituary_id",
        "description",
        "type",
        "status",
        "invoice_date",
        "poll_url",
        "due_date",
        "subtotal",
        "total",
        "type",
        "reminder",
        "created_at",
        "updated_at",
    ];

    public function member(){
        return $this->belongsTo(Member::class);
    }
    public function obituary(){
        return $this->belongsTo(Obituary::class);
    }
    public function invoice_items(){
        return $this->hasMany(InvoiceItem::class);
    }
}

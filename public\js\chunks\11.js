(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[11],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js&":
/*!**********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! vuelidate/lib/validators */ "./node_modules/vuelidate/lib/validators/index.js");
/* harmony import */ var vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/api */ "./resources/js/services/api.js");


var mustBeTrue = function mustBeTrue(value) {
  return value;
};
/* harmony default export */ __webpack_exports__["default"] = ({
  props: ['forms', 'categories'],
  data: function data() {
    return {
      showText: 0,
      submitted: false,
      form: {},
      totalPayable: 0,
      eachPayable: 0,
      additionalMembers: 0,
      consultationSelected: true
    };
  },
  mounted: function mounted() {
    var consultations = this.categories.filter(function (item) {
      return item.id == 1;
    })[0];
    console.log(consultations);
    this.form.services = [];
  },
  validations: {
    form: {
      services: {
        required: vuelidate_lib_validators__WEBPACK_IMPORTED_MODULE_0__["required"],
        mustBeTrue: mustBeTrue
      }
    }
  },
  methods: {
    submit: function submit() {
      return this.$emit('done', this.form);
    },
    checkConsultation: function checkConsultation() {
      // let consultations = this.categories.filter(item => item.id == 1)[0].services;
      // consultations = [...consultations, ...this.categories.filter(item => item.id == 6)[0].services];
      // consultations = [...consultations, ...this.categories.filter(item => item.id == 7)[0].services];

      // let consultationIds = [];
      // consultations.forEach(e => {
      //     consultationIds.push(e.id);
      // });
      // let selectedConsultations = this.form.services.filter(item => consultationIds.includes(item));

      // if(!selectedConsultations.length)
      // {this.consultationSelected = false;}
      // else{ this.consultationSelected = true}

      this.consultationSelected = true;
    },
    updateTotal: function updateTotal(service, category) {
      var _this$form$services,
        _this = this;
      var serviceId = service.id;
      this.totalPayable = parseFloat(0);
      this.eachPayable = parseFloat(0);
      var cateServices = [];
      if ((_this$form$services = this.form.services) !== null && _this$form$services !== void 0 && _this$form$services.includes(service.id)) {
        this.form.services = this.form.services.filter(function (item) {
          return item !== serviceId;
        });
        this.form.servicesFull = this.form.servicesFull.filter(function (item) {
          return item !== service;
        });
      } else {
        category.services.forEach(function (e) {
          cateServices.push(e.id);
        });
        var toRemove = new Set(cateServices);
        this.form.services = this.form.services.filter(function (x) {
          return !toRemove.has(x);
        });
        if (this.form.services == null || this.form.services.length == 0) {
          this.form.services = [serviceId];
          this.form.servicesFull = [service];
        } else if (this.form.services.includes(serviceId)) {
          this.form.services = this.form.services.filter(function (item) {
            return item !== serviceId;
          });
          this.form.servicesFull = this.form.servicesFull.filter(function (item) {
            return item !== service;
          });
        } else {
          this.form.services.push(serviceId);
          this.form.servicesFull.push(service);
        }
        this.form.servicesFull.forEach(function (e) {
          _this.eachPayable += parseFloat(e.price);
        });
      }
      console.log(this.form.services);
      this.addDependants();
      this.checkConsultation();
    },
    addDependants: function addDependants() {
      if (this.additionalMembers < 0) this.additionalMembers = 0;
      this.form.additionalMembers = this.additionalMembers;
      // console.log(this.additionalMembers);
      this.additionalMembers = parseFloat(this.additionalMembers);
      this.totalPayable = (this.additionalMembers + 1) * this.eachPayable;
    }
  }
});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434&":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434& ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div", [_vm._l(_vm.categories, function (item3, index2) {
    return _c("div", {
      key: index2,
      staticClass: "custom-checkbox mt-3"
    }, [_c("div", {
      staticClass: "card text-center",
      style: "background-image: url(storage/img/categories/" + ".png);background-repeat: no-repeat;background-color: var(--base); background-size: cover; background-position: center;"
    }, [_c("div", {
      staticClass: "card-body"
    }, [_c("h5", {
      staticClass: "card-title text-white",
      attrs: {
        id: "category-" + item3.id
      }
    }, [_vm._v(_vm._s(item3.name))])])]), _vm._v(" "), _c("div", {
      staticClass: "row m-0"
    }, _vm._l(item3.services, function (item, index) {
      var _vm$form$services$inc, _vm$form$services;
      return _c("div", {
        key: index,
        staticClass: "p-0 custom-checkbox mt-3 col-lg-6 col-xl-4"
      }, [_c("div", {
        staticClass: "m-2 card",
        staticStyle: {
          padding: "2.5rem",
          height: "100%",
          "background-color": "#fcfcfc"
        },
        on: {
          mouseenter: function mouseenter($event) {
            _vm.showText = item.id;
          },
          mouseleave: function mouseleave($event) {
            _vm.showText = item.id;
          }
        }
      }, [_c("input", {
        staticClass: "custom-control-input",
        "class": {
          "is-invalid": _vm.submitted && !_vm.form.services.contains(item.id)
        },
        attrs: {
          id: "const_serv" + item.id,
          type: "checkbox"
        },
        domProps: {
          checked: (_vm$form$services$inc = (_vm$form$services = _vm.form.services) === null || _vm$form$services === void 0 ? void 0 : _vm$form$services.includes(item.id)) !== null && _vm$form$services$inc !== void 0 ? _vm$form$services$inc : false
        },
        on: {
          change: function change($event) {
            return _vm.updateTotal(item, item3);
          }
        }
      }), _vm._v(" "), _c("label", {
        staticClass: "custom-control-label disable-select",
        attrs: {
          "for": "const_serv" + item.id
        }
      }, [_vm._v("\n          " + _vm._s(item.title) + "\n          "), _c("br"), _vm._v(" "), _c("small", {
        directives: [{
          name: "show",
          rawName: "v-show",
          value: _vm.showText != item.id,
          expression: "showText!=item.id"
        }],
        staticClass: "mt-1 line-clamp-4"
      }, [_vm._v(_vm._s(item.description))]), _vm._v(" "), _c("small", {
        directives: [{
          name: "show",
          rawName: "v-show",
          value: _vm.showText == item.id,
          expression: "showText==item.id"
        }],
        staticClass: "mt-1",
        staticStyle: {
          display: "-webkit-box"
        }
      }, [_vm._v(_vm._s(item.description))]), _vm._v(" "), _c("br"), _vm._v(" "), _c("b", {
        staticClass: "mt-1"
      }, [_vm._v("USD$" + _vm._s(item.price) + " per month")])])])]);
    }), 0)]);
  }), _vm._v(" "), _c("div", {
    staticClass: "mt-3 mr-sm-2"
  }, [_c("br"), _c("h5", {
    attrs: {
      "for": "full_name"
    }
  }, [_vm._v("How many depedants do you wish to add?")]), _vm._v(" "), _c("input", {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.additionalMembers,
      expression: "additionalMembers"
    }],
    staticClass: "form-control mb-30 bg-gray",
    staticStyle: {
      width: "auto"
    },
    attrs: {
      type: "number",
      name: "additionalMembers",
      min: "0",
      id: "additionalMembers",
      placeholder: "0"
    },
    domProps: {
      value: _vm.additionalMembers
    },
    on: {
      change: function change($event) {
        return _vm.addDependants();
      },
      input: function input($event) {
        if ($event.target.composing) return;
        _vm.additionalMembers = $event.target.value;
      }
    }
  })]), _vm._v(" "), _c("div", {
    staticClass: "mt-3 mr-sm-2"
  }, [_vm.totalPayable <= 0 && _vm.consultationSelected ? _c("div", {
    staticClass: "col-12 text-right"
  }, [_c("button", {
    staticClass: "btn radix-btn"
  }, [_vm._v("Select Required Services")])]) : _vm._e(), _vm._v(" "), !_vm.consultationSelected ? _c("div", {
    staticClass: "col-12 text-right"
  }, [_c("small", {
    staticClass: "text-danger px-4"
  }, [_vm._v("Please add one item from consultation or hospitalisation cover to your selection.")]), _vm._v(" "), _c("a", {
    staticClass: "btn radix-btn",
    attrs: {
      href: "#category-1"
    }
  }, [_vm._v("Add At Least One Consultation or Hospitalisation  Cover Package")])]) : _vm._e(), _vm._v(" "), _vm.totalPayable > 0 && _vm.consultationSelected ? _c("div", {
    staticClass: "col-12 text-right",
    on: {
      click: function click($event) {
        return _vm.submit();
      }
    }
  }, [_c("button", {
    staticClass: "btn radix-btn"
  }, [_vm._v("Next Step")])]) : _vm._e()])], 2);
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./node_modules/css-loader/index.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/css-loader??ref--6-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--6-2!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(/*! ../../../../node_modules/css-loader/lib/css-base.js */ "./node_modules/css-loader/lib/css-base.js")(false);
// imports


// module
exports.push([module.i, "\n.disable-select {\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  user-select: none;\r\n  cursor: pointer;\n}\n.line-clamp-4 {\r\n    overflow: hidden;\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-line-clamp: 4;\n}\r\n", ""]);

// exports


/***/ }),

/***/ "./node_modules/style-loader/index.js!./node_modules/css-loader/index.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/style-loader!./node_modules/css-loader??ref--6-1!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src??ref--6-2!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {


var content = __webpack_require__(/*! !../../../../node_modules/css-loader??ref--6-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src??ref--6-2!../../../../node_modules/vue-loader/lib??vue-loader-options!./CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& */ "./node_modules/css-loader/index.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&");

if(typeof content === 'string') content = [[module.i, content, '']];

var transform;
var insertInto;



var options = {"hmr":true}

options.transform = transform
options.insertInto = undefined;

var update = __webpack_require__(/*! ../../../../node_modules/style-loader/lib/addStyles.js */ "./node_modules/style-loader/lib/addStyles.js")(content, options);

if(content.locals) module.exports = content.locals;

if(false) {}

/***/ }),

/***/ "./resources/js/components/applicant/CustomiseForm.vue":
/*!*************************************************************!*\
  !*** ./resources/js/components/applicant/CustomiseForm.vue ***!
  \*************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CustomiseForm.vue?vue&type=template&id=6cc24434& */ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434&");
/* harmony import */ var _CustomiseForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CustomiseForm.vue?vue&type=script&lang=js& */ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& */ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&");
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");






/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _CustomiseForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__["render"],
  _CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/components/applicant/CustomiseForm.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js&":
/*!**************************************************************************************!*\
  !*** ./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib??vue-loader-options!./CustomiseForm.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&":
/*!**********************************************************************************************************!*\
  !*** ./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& ***!
  \**********************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_style_loader_index_js_node_modules_css_loader_index_js_ref_6_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_2_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/style-loader!../../../../node_modules/css-loader??ref--6-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src??ref--6-2!../../../../node_modules/vue-loader/lib??vue-loader-options!./CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css& */ "./node_modules/style-loader/index.js!./node_modules/css-loader/index.js?!./node_modules/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/postcss-loader/src/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=style&index=0&id=6cc24434&lang=css&");
/* harmony import */ var _node_modules_style_loader_index_js_node_modules_css_loader_index_js_ref_6_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_2_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_style_loader_index_js_node_modules_css_loader_index_js_ref_6_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_2_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _node_modules_style_loader_index_js_node_modules_css_loader_index_js_ref_6_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_2_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _node_modules_style_loader_index_js_node_modules_css_loader_index_js_ref_6_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_2_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_style_index_0_id_6cc24434_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));


/***/ }),

/***/ "./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434&":
/*!********************************************************************************************!*\
  !*** ./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434& ***!
  \********************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../node_modules/babel-loader/lib??ref--4-0!../../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../../node_modules/vue-loader/lib??vue-loader-options!./CustomiseForm.vue?vue&type=template&id=6cc24434& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/applicant/CustomiseForm.vue?vue&type=template&id=6cc24434&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_CustomiseForm_vue_vue_type_template_id_6cc24434___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ })

}]);
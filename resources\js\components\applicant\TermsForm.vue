<template>
  <div>
    <div class="custom-control custom-checkbox mt-3 mr-sm-2">
      <input
        v-model="form.terms_read"
        :class="{ 'is-invalid': submitted && !form.terms_read }"
        class="custom-control-input" id="const1" type="checkbox" />
      <label class="custom-control-label disable-select" for="const1">
        I have read and understand the
        <a href="">Terms and Conditions</a> of Funeral Cover Association
      </label>
    </div>
    <div class="custom-control custom-checkbox mt-3 mr-sm-2">
      <input
        v-model="form.details_true"
        :class="{ 'is-invalid': submitted && !form.details_true }"
        class="custom-control-input" id="info1" type="checkbox" />
      <label
        class="custom-control-label disable-select"
        for="info1"
      >I certify that the information provided is true and correct in all respect</label>
    </div>
    <div class="custom-control custom-checkbox mt-3 mr-sm-2">
      <input
        v-model="form.terms_agreed"
        :class="{ 'is-invalid': submitted && !form.terms_agreed }"
        class="custom-control-input" id="resident1" type="checkbox" />
      <label
        class="custom-control-label disable-select"
        for="resident1"
      >I accept the terms and conditions</label>
    </div>
    <div class="row mt-3">
        <p
      >You will be required to pay your subscription fee after form submission.</p>
      <div class="col-12 text-center" @click="submit()">
        <button class="btn radix-btn">Submit Details</button>
      </div>
    </div>
  </div>
</template>

<script>
import { required } from "vuelidate/lib/validators";
import { submitJoiningDetails } from "../../services/api";

const mustBeTrue = (value) => value;

export default {
  props: ['forms'],
  data(){
    return {
      submitted: false,
      form: {}
    }
  },
  validations: {
    form: {
      terms_read: { required,mustBeTrue },
      details_true: { required, mustBeTrue },
      terms_agreed: { required, mustBeTrue },
    }
  },
  methods: {
    submit(){

      this.submitted = true;
      this.$v.$touch();
      if (this.$v.$invalid) return;
      let loader = this.$loading.show({
        backgroundColor: "#000",
        color: "#ae2227",
        canCancel: false
      });
      const form_data = { ...this.forms, ...this.form };
      submitJoiningDetails(form_data)
        .then(({ route }) => {
          loader.hide();
          if(!route) {
            return this.$vToastify.error(
            "Oops! Something went wrong. Try again or Contact support"
          );
          }
          window.location.replace(route);
        })
        .catch(err => {
          let msg = "Oops! Failed to submit. Try again or contact support";
          try {
            const {response: {data: {errors, message}}} = err;
            if(message){
              msg = message
            }
            if(errors){
              Object.keys(errors).forEach(e => {
                setTimeout(() => {
                  this.$vToastify.error(errors[e][0]);
                }, 1000);
              });
            }
          } catch (error) {}
          this.$vToastify.error(msg);
          loader.hide();
        });
    }
  }
};
</script>



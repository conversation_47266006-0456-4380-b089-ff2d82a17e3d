<template>
  <div>
    <h6>Enter Fullname & D.O.B as it appears on their IDs</h6>
    <div class="row">
      <div class="col-12"></div>
      <div class="col-12 col-lg-6">
        <label for="full_name">Full Name:</label>
        <input
          class="form-control mb-30 bg-gray"
          :class="{ 'is-invalid': submitted && $v.form.full_name.$error }"
          type="text"
          name="full_name"
          @keyup="$emit('fullName', form.full_name)"
          v-model="form.full_name"
          id="full_name"
          placeholder="Full Name"
        />
      </div>
      <div class="col-12 col-lg-6">
        <label for="nominee_email">Email:</label>
        <input
          class="form-control mb-30 bg-gray"
          id="nominee_email"
          type="email"
          v-model="form.email"
          placeholder="Email Address (Optional)"
        />
      </div>
      <div class="col-12">
        <datepicker
          format="dd MMMM yyyy"
          :initialView="'year'"
          name="dob"
          v-model="form.dob"
          :input-class="(submitted && $v.form.dob.$error) ? 'form-control mb-30 is-invalid' : 'form-control mb-30'"
          placeholder="Date of Birth"
        ></datepicker>
      </div>
      <div class="col-12 col-lg-6" v-if="customise_nominee_package">
        <label for="gov_id">Select Package</label>

        <select
        v-model="form.service_id"
        :class="{
            'is-invalid': submitted && $v.form.service_id.$error,
        }"
        class="custom-select form-control mb-30 bg-gray selectpicker"
        id="service_id"
        >
            <option value="" disabled selected>
                Select Package
            </option>
            <option value="2" >$1 for $1000</option>
            <option value="1">$2 for $2000</option>
        </select>
        <p v-if="packageError || (submitted && $v.form.service_id.$error)" class="text-danger">Please select a package</p>
    </div>
    </div>
    <div class="row">
      <div class="col-12">
        <label for="gov_id">Id Number:</label>
        <input
          v-model="form.gov_id"
          class="custom-select form-control mb-30 bg-gray"
          id="gov_id"
          name="gov_id"
        >
          <!-- <option value="undefined" disabled selected>Select</option>
          <option value="birth">Birth</option>
          <option value="descent">Descent</option>
          <option value="spouse">Spouse</option>
        </select> -->
      </div>
    </div>
  </div>
</template>

<script>
import { required, email } from "vuelidate/lib/validators";

export default {
    props: ['customise_nominee_package'],
  components: {
    Datepicker: () => import("vuejs-datepicker")
  },
  mounted() {
    // Always set up the event listener first
    this.$eventBus.$on("validate:nominee", skip => {
      if (skip) {
        return this.$emit("nomineeValid", this.form);
      }
      this.submitted = true;
      this.$v.$touch();

      // Check package validation if customise_nominee_package is enabled
      if (this.customise_nominee_package && (!this.form.service_id || this.form.service_id === "")) {
        this.packageError = true;
        this.scrollErrorToView();
        return;
      } else {
        this.packageError = false;
      }

      if (this.$v.$invalid) {
        this.scrollErrorToView();
        return;
      }
      this.$emit("nomineeValid", this.form);
    });
  },
  data() {
    return {
      submitted: false,
      packageError: false,
      form: {}
    };
  },
  validations() {
    const validations = {
      form: {
        full_name: { required },
        dob: {  },
        service_id: { },
        email: {  },
        gov_id: {  }
      }
    };

    // Make service_id required when customise_nominee_package is true
    if (this.customise_nominee_package) {
      validations.form.service_id = { required };
    }

    return validations;
  },
  watch: {
    'form.service_id'(newValue) {
      // Reset package error when service_id changes
      if (this.customise_nominee_package && newValue) {
        this.packageError = false;
      }
    }
  },
  methods: {
    scrollErrorToView() {
      setTimeout(() => {
        const elem = $(`.is-invalid`).first();
        $(elem).focus();
      }, 100);
    }
  }
};
</script>

<style>
</style>

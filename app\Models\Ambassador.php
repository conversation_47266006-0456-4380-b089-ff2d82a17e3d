<?php

namespace App\Models;

use App\Service;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Ambassador extends Model
{
    use Notifiable;

    protected $fillable = [
        "id",
        "name",
        "email",
        "phone"
    ];


    public function members(){
        return $this->hasMany(Member::class);
    }

    public function applicants(){
        return $this->hasMany(Applicant::class);
    }

}

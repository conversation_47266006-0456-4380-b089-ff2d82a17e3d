ln -s /home/<USER>/project_name/storage/app/public /home/<USER>/project_name/public/storage

 

 ln -s /home/<USER>/ukz.joemags.co.zw/storage/app/public /home/<USER>/ukz.joemags.co.zw/public/storage >/dev/null 2>&1



 ln -s /home/<USER>/Code/Clients/UKZ/ukz-lara/storage/app/public /home/<USER>/Code/Clients/UKZ/ukz-lara/public/storage

 ln -s /home/<USER>/nhembe.co.zw/storage/app/public /home/<USER>/nhembe.co.zw/public/storage >/dev/null 2>&1


  ln -s /home/<USER>/ukz.joemags.co.zw/storage/app/public /home/<USER>/ukz.joemags.co.zw/public/storage >/dev/null 2>&1


You should move your deploy_path folder outside public_html and make a symlink like this:

ln -s /your/deploy/path/current /path/to/public_html

ln -s /home/<USER>/core_ukz/storage/app/public /home/<USER>/core_ukz/public/storage
https://blog.netgloo.com/2016/01/29/deploy-laravel-application-on-shared-hosting/
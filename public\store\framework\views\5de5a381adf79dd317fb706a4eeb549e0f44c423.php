<?php echo $__env->make('inc.licence', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<!doctype html>
<html lang="en">

<head>
    <?php echo $__env->make('inc.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <style type="text/css">
        :root {
        --base: {{setting('site.main_color')}} !important;
        --primary: {{setting('site.primary_color')}} !important;
        --secondary: #fdd76e;
        }
    </style>
</head>

<body class="">
    <!-- <div class="preloader" id="preloader">
        <div class="spinner-grow text-light" role="status"><span class="sr-only">Loading...</span></div>
    </div> -->
    <div class="wrapper" id="corp24-app">
        <!-- <member-nav :user="<?php echo e($user); ?>" :nav_links="<?php echo e($nav_links); ?>"></member-nav> -->
        <member-nav :user="<?php echo e($user); ?>" :nav_links="<?php echo e($nav_links); ?>" :balance="<?php echo e(Auth::user()->balanceFloat); ?>"></member-nav>
        <div class="main-panel" style="min-height: 100vh;">
            <?php echo $__env->make('inc.nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <div class="content">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
            <?php echo $__env->make('inc.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- Modal -->

        </div>
        <new-message :level="'<?php echo e(session('level') ?? 'primary'); ?>'" :message="'<?php echo e(session('message') ?? null); ?>'">
        </new-message>

        <member-loaded></member-loaded>
        <deposit-modal
            :user="<?php echo e($user); ?>"
            :client_id="'<?php echo e(env('PAYPAL_CLIENT')); ?>'"
            :route="'<?php echo e(route('members-area.submit_deposit')); ?>'">
            <?php echo csrf_field(); ?>
        </deposit-modal>
    </div>

    <script src="<?php echo e(asset('js/corp24.app.js?id='.time())); ?>"></script>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/master.blade.php ENDPATH**/ ?>

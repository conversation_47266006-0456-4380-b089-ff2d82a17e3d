@extends('master')

@section('page')
Sign In
@endsection

@section('content')
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <!-- Register Thumbnail-->
      <div class="d-none d-lg-block col-12 col-lg-6">
        <div class="register-thumbnail mb-50">
        <div class="mx-auto" style="width: 66%;">
                <img src="{{'/'.'storage/' .str_replace("\\", "/", setting('site.home_image'))}}" alt="">
            </div>
      </div>
      </div>
      <!-- Register Card-->
      <div class="col-12 col-lg-6">
       @if ($errors->any())
        <div class="alert alert-danger" role="alert">
            <ul>
                @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif
        <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
          <div class="card-body">
            <h5>Applicant already approved: </h5>
            <h4> Applicant already approved. Please login or contact admin.</h4>
            If you havent already set your password, proceed to reset your password.
            <div class="col-12 text-right" >
                <a class="btn radix-btn" href="/forgot-password">Reset Password</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<joining-payment-modal
            :user="{{$applicant}}"
            :client_id="'{{env('PAYPAL_CLIENT')}}'"
            :route="'{{route('application.submit_deposit', $applicant->id)}}'">
            @csrf
        </joining-payment-modal>
@endsection

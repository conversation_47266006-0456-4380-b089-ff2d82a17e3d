{"version": 3, "sources": ["../../../build/scss/parts/adminlte.pages.scss", "adminlte.pages.css", "../../../node_modules/bootstrap/scss/_close.scss", "../../../node_modules/bootstrap/scss/vendor/_rfs.scss", "../../../build/scss/_bootstrap-variables.scss", "../../../node_modules/bootstrap/scss/mixins/_hover.scss", "../../../build/scss/mixins/_animations.scss", "../../../build/scss/pages/_mailbox.scss", "../../../node_modules/bootstrap/scss/mixins/_lists.scss", "../../../build/scss/pages/_lockscreen.scss", "../../../node_modules/bootstrap/scss/mixins/_border-radius.scss", "../../../build/scss/mixins/_dark-mode.scss", "../../../build/scss/pages/_login_and_register.scss", "../../../build/scss/pages/_404_500_errors.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "../../../build/scss/pages/_invoice.scss", "../../../build/scss/pages/_profile.scss", "../../../build/scss/pages/_e-commerce.scss", "../../../node_modules/bootstrap/scss/mixins/_image.scss", "../../../node_modules/bootstrap/scss/mixins/_box-shadow.scss", "../../../build/scss/pages/_projects.scss", "../../../build/scss/pages/_iframe.scss", "../../../build/scss/_variables.scss", "../../../build/scss/mixins/_touch-support.scss", "../../../build/scss/pages/_kanban.scss"], "names": [], "mappings": "AAAA;;;;;;ECME;ACNF;EACE,YAAY;ECmLR,iBAAW;EDjLf,gBE0P+B;EFzP/B,cAAc;EACd,WEgBa;EFfb,yBEKa;EFJb,WAAW;ADQb;;AIHE;EHDE,WEUW;EFTX,qBAAqB;ADQzB;;AIJE;EHCI,YAAY;ADOlB;;ACIA;EACE,UAAU;EACV,6BAA6B;EAC7B,SAAS;ADDX;;ACOA;EACE,oBAAoB;ADJtB;;AK7BA;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;ELgCZ;EK7BA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EL+BrC;EK5BA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EL8BZ;EK3BA;IACE,8DAAsD;IAAtD,sDAAsD;EL6BxD;EK1BA;IACE,qCAA6B;IAA7B,6BAA6B;EL4B/B;AACF;;AKnDA;EACE;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,mCAAmC;IACnC,UAAU;ELgCZ;EK7BA;IACE,+DAAuD;IAAvD,uDAAuD;IACvD,mCAAmC;EL+BrC;EK5BA;IACE,8DAAsD;IAAtD,sDAAsD;IACtD,UAAU;EL8BZ;EK3BA;IACE,8DAAsD;IAAtD,sDAAsD;EL6BxD;EK1BA;IACE,qCAA6B;IAA7B,6BAA6B;EL4B/B;AACF;;AKxBA;EACE;IACE,UAAU;EL2BZ;EKxBA;IACE,UAAU;EL0BZ;AACF;;AKjCA;EACE;IACE,UAAU;EL2BZ;EKxBA;IACE,UAAU;EL0BZ;AACF;;AKvBA;EACE;IACE,UAAU;EL0BZ;EKvBA;IACE,UAAU;ELyBZ;AACF;;AKhCA;EACE;IACE,UAAU;EL0BZ;EKvBA;IACE,UAAU;ELyBZ;AACF;;AKtBA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;ELyB7C;EKvBA;IACE,sDAA8C;IAA9C,8CAA8C;ELyBhD;EKvBA;IACE,kDAA0C;IAA1C,0CAA0C;ELyB5C;EKvBA;IACE,iDAAyC;IAAzC,yCAAyC;ELyB3C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;EKvBA;IACE,mDAA2C;IAA3C,2CAA2C;ELyB7C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;AACF;;AK1DA;EACE;IACE,mDAA2C;IAA3C,2CAA2C;ELyB7C;EKvBA;IACE,sDAA8C;IAA9C,8CAA8C;ELyBhD;EKvBA;IACE,kDAA0C;IAA1C,0CAA0C;ELyB5C;EKvBA;IACE,iDAAyC;IAAzC,yCAAyC;ELyB3C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,oDAA4C;IAA5C,4CAA4C;ELyB9C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;EKvBA;IACE,mDAA2C;IAA3C,2CAA2C;ELyB7C;EKvBA;IACE,qDAA6C;IAA7C,6CAA6C;ELyB/C;AACF;;AKtBA;EACE;IACE,uBAAe;IAAf,eAAe;ELyBjB;EKtBA;IACE,mEAA2D;IAA3D,2DAA2D;ELwB7D;EKrBA;IACE,iEAAyD;IAAzD,yDAAyD;ELuB3D;EKpBA;IACE,mEAA2D;IAA3D,2DAA2D;ELsB7D;EKnBA;IACE,iEAAyD;IAAzD,yDAAyD;ELqB3D;EKlBA;IACE,kEAA0D;IAA1D,0DAA0D;ELoB5D;EKjBA;IACE,uBAAe;IAAf,eAAe;ELmBjB;AACF;;AK9CA;EACE;IACE,uBAAe;IAAf,eAAe;ELyBjB;EKtBA;IACE,mEAA2D;IAA3D,2DAA2D;ELwB7D;EKrBA;IACE,iEAAyD;IAAzD,yDAAyD;ELuB3D;EKpBA;IACE,mEAA2D;IAA3D,2DAA2D;ELsB7D;EKnBA;IACE,iEAAyD;IAAzD,yDAAyD;ELqB3D;EKlBA;IACE,kEAA0D;IAA1D,0DAA0D;ELoB5D;EKjBA;IACE,uBAAe;IAAf,eAAe;ELmBjB;AACF;;AMlIA;EAEI,SAAS;ANoIb;;AMhIA;EACE,YAAY;ANmId;;AMpIA;EAII,6CHOW;AH6Hf;;AMhIA;EACE,6CHEa;EGDb,aAAa;ANmIf;;AMrIA;EAKI,eAAe;EACf,SAAS;ANoIb;;AM1IA;EAUI,SAAS;EACT,gBAAgB;ANoIpB;;AMhIA;EACE,WAAW;EACX,eAAe;ANmIjB;;AMhIA;EACE,aAAa;ANmIf;;AMhIA;ECtCE,eAAe;EACf,gBAAgB;AP0KlB;;AMrIA;EAGI,sBAAsB;EACtB,WAAW;EACX,mBAAmB;EACnB,kBAAkB;EAClB,YAAY;ANsIhB;;AMlIA;EACE,WAAW;EACX,gBAAgB;ANqIlB;;AMlIA;;;EAGE,cAAc;ANqIhB;;AMlIA;EACE,yBHrDgB;EGsDhB,aAAa;ANqIf;;AMlIA;EACE,WAAW;EACX,eAAe;ANqIjB;;AMvIA;EAKI,qBAAqB;EACrB,mBAAmB;ANsIvB;;AMlIA;EACE,WAAW;EACX,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;ANqIpB;;AM1IA;EAQI,UAAU;ANsId;;AM9IA;EAWM,YAAY;EACZ,eAAe;ANuIrB;;AQ7NA;EACE,yBLOgB;AHyNlB;;AQjOA;EAKI,gBAAgB;EAChB,kBAAkB;ARgOtB;;AQ5NA;EACE,eAAe;EACf,gBAAgB;EAChB,mBAAmB;EACnB,kBAAkB;AR+NpB;;AQnOA;EAOI,cLJc;AHoOlB;;AQ5NA;EACE,cAAc;EACd,eAAe;EACf,gBAAgB;AR+NlB;;AQ1NA;EChBI,kBDiBwB;EAC1B,sBLzBa;EK0Bb,sBAAsB;EACtB,UAAU;EACV,kBAAkB;EAClB,YAAY;AR6Nd;;AQzNA;EC1BI,kBD2BwB;EAC1B,sBLnCa;EKoCb,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,UAAU;EACV,WAAW;AR4Nb;;AQnOA;EC1BI,kBDoC0B;EAC1B,YAAY;EACZ,WAAW;AR6Nf;;AQxNA;EACE,iBAAiB;AR2NnB;;AQ5NA;EAII,SAAS;AR4Nb;;AQhOA;EAQI,sBL1DW;EK2DX,SAAS;EACT,eAAe;AR4NnB;;AQxNA;EACE,gBAAgB;AR2NlB;;AUjSI;EF2EA,yBL9Dc;AHwRlB;;AUrSI;EF8EA,WLzEW;AHoSf;;AUzSI;EFiFA,yBLpEc;AHgSlB;;AU7SI;EFoFA,yBLzEc;AHsSlB;;AWnTA;;EAEE,iBAAiB;EACjB,gBAAgB;EAChB,oBAAoB;EACpB,kBAAkB;AXsTpB;;AW3TA;;EAQI,cRMc;AHkTlB;;AWpTA;;EAEE,sBAAmB;EAAnB,mBAAmB;EACnB,yBRNgB;EQOhB,oBAAa;EAAb,aAAa;EACb,0BAAsB;EAAtB,sBAAsB;EACtB,aAAa;EACb,qBAAuB;EAAvB,uBAAuB;AXuTzB;;AWpTA;;EAEE,YAAY;AXuTd;;AWrTE;EAJF;;IAKI,iBAAiB;IACjB,UAAU;EX0TZ;AACF;;AWjUA;;EAUI,gBAAgB;AX4TpB;;AWxTA;;EAEE,sBR/Ba;EQgCb,aAAa;EACb,WAAW;EACX,aAAa;AX2Tf;;AWhUA;;EASM,eAAe;AX4TrB;;AWrUA;;EAYQ,gBAAgB;AX8TxB;;AW1UA;;;;EAgBU,qBR6WgE;AH5C1E;;AWjVA;;EAsBU,gBAAgB;AXgU1B;;AWtVA;;;;EA2BU,qBRxBO;AH0VjB;;AW7VA;;EAiCU,gBAAgB;AXiU1B;;AWlWA;;EAqCU,qBRrCO;AHuWjB;;AWvWA;;EA2CM,6BAA6B;EAC7B,mCR2I8B;EQ1I9B,cAAc;EACd,gCRyI8B;EQxI9B,WAAW;EACX,wERkW4F;AHjClG;;AW5TA;;EAEE,SAAS;EACT,oBAAoB;EACpB,kBAAkB;AX+TpB;;AW5TA;EACE,cAAc;AX+ThB;;AU9ZI;;ECqGA,yBRxFc;EQyFd,qBR3Fc;EQ4Fd,WRlGW;AHgaf;;AUraI;;EC4GE,WRvGS;AHqaf;;AY5aA;EACE,mBAAmB;EACnB,YAAY;AZ+ad;;Aa5WI;EDrEJ;IAKI,WAAW;EZibb;AACF;;AYvbA;EAUI,WAAW;EACX,gBAAgB;EAChB,gBAAgB;AZibpB;;AaxXI;EDrEJ;IAeM,WAAW;IACX,kBAAkB;EZmbtB;AACF;;AYpcA;EAsBI,cAAc;EACd,kBAAkB;AZkbtB;;AapYI;EDrEJ;IA0BM,cAAc;EZoblB;AACF;;AY/cA;EA8BM,eAAe;EACf,gBAAgB;AZqbtB;;Aa/YI;EDrEJ;IAkCQ,kBAAkB;EZubxB;AACF;;Ac1dA;EACE,sBXMa;EWLb,sCXea;EWdb,kBAAkB;Ad6dpB;;Ac1dA;EACE,aAAa;Ad6df;;AUleI;EIUA,yBXGc;AHydlB;;AexeA;EACE,yBZWgB;EYVhB,cAAc;EACd,YAAY;EACZ,YAAY;Af2ed;;AexeA;EACE,eAAe;EACf,eAAe;Af2ejB;;AexeA;EACE,gCZDgB;EYEhB,WAAW;EACX,mBAAmB;EACnB,oBAAoB;Af2etB;;Ae/eA;EAOI,gBAAgB;EAChB,gBAAgB;EAChB,iBAAiB;Af4erB;;AerfA;EAaI,mBAAmB;EACnB,WAAW;Af4ef;;Ae1fA;EAkBI,WAAW;Af4ef;;AUxgBI;EKkCA,WZ7BW;EY8BX,qBZxBc;AHkgBlB;;AgB9gBA;ECME,eAAe;EAGf,YAAY;EDPZ,WAAW;AhBkhBb;;AgB9gBA;EACE,uBAAoB;EAApB,oBAAoB;EACpB,oBAAa;EAAb,aAAa;EACb,gBAAgB;AhBihBlB;;AgB7gBA;EEFM,0CfKS;EMHX,sBN6MgC;EazMlC,sBbXa;EaYb,yBbTgB;EaUhB,oBAAa;EAAb,aAAa;EACb,kBAAkB;EAClB,eAA4C;EAC5C,eAA+B;AhB+gBjC;;AgBxhBA;ECPE,eAAe;EAGf,YAAY;EDiBV,2BAAkB;EAAlB,kBAAkB;AhBihBtB;;AgB9hBA;EAiBI,WAAW;AhBihBf;;AgB5gBA;EAEI,mBAAmB;AhB8gBvB;;AmBpjBA;EAEI,sBAAsB;AnBsjB1B;;AmBxjBA;EAMI,gBAAgB;AnBsjBpB;;AmB5jBA;;EAYI,kBAAkB;EAClB,eAAe;EACf,aAAa;AnBqjBjB;;AmBnkBA;EAmBI,kBAAkB;AnBojBtB;;AoB3kBA;EAEI,aAAa;ApB6kBjB;;AoB/kBA;EAKI,yBAAyB;EACzB,wBAAwB;EACxB,4BAA4B;ApB8kBhC;;AoBrlBA;;EAWI,aAAa;ApB+kBjB;;AoB3kBA;EACE,gBAAgB;ApB8kBlB;;AoB/kBA;EAII,wBAAwB;ApB+kB5B;;AoB3kBA;EACE,YAAY;ApB8kBd;;AoB/kBA;EAKM,cjBYW;EiBXX,kBAAkB;EAClB,cAAc;EACd,cAAc;EACd,YAAY;EACZ,WAAW;EACX,kBAAkB;ApB8kBxB;;AoBzlBA;EAeQ,8BAAsB;EAAtB,sBAAsB;EACtB,gCC8Ic;ED9Id,wBC8Ic;ED7Id,iCAAyB;EAAzB,yBAAyB;EACzB,mBAAmB;ApB8kB3B;;AsBlnBE;EFkBF;IAsBQ,mBAAmB;EpB+kBzB;AACF;;AoBtmBA;EA0BM,gBAAgB;EAChB,WAAW;ApBglBjB;;AoB3mBA;EA8BQ,mBAAmB;ApBilB3B;;AoB/mBA;EAiCQ,kBAAkB;ApBklB1B;;AoBnnBA;EAsCY,8BAAsB;EAAtB,sBAAsB;EACtB,gCCuHU;EDvHV,wBCuHU;EDtHV,iCAAyB;EAAzB,yBAAyB;EACzB,mBAAmB;ApBilB/B;;AsB5oBE;EFkBF;IA4Cc,mBAAmB;EpBmlB/B;AACF;;AoBhoBA;EAmDM,kBAAkB;ApBilBxB;;AoBpoBA;EAsDM,aAAa;ApBklBnB;;AoBxoBA;EAyDM,WAAW;EACX,oBAAa;EAAb,aAAa;EACb,qBAAuB;EAAvB,uBAAuB;EACvB,sBAAmB;EAAnB,mBAAmB;ApBmlBzB;;AoB/oBA;EA+DM,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,aAAa;EACb,yBCzCW;ArB6nBjB;;AoBxpBA;EAuEQ,oBAAa;EAAb,aAAa;EACb,qBAAuB;EAAvB,uBAAuB;EACvB,sBAAmB;EAAnB,mBAAmB;EACnB,WAAW;EACX,YAAY;ApBqlBpB;;AoBhqBA;EAgFM,SAAS;EACT,WAAW;EACX,YAAY;EACZ,mBAAmB;ApBolBzB;;AoBvqBA;EAsFQ,4BAA4B;ApBqlBpC;;AoBjlBI;EACE,kBAAkB;EAClB,OAAO;EACP,MAAM;EACN,QAAQ;EACR,SAAS;EACT,yBAAyB;EACzB,YAAY;EACZ,gBAAgB;EAChB,ajB6ckC;AHuIxC;;AoB/kBA;EAEI,kCAA0B;EAA1B,0BAA0B;EAC1B,8BAA8B;EAC9B,UAAU;ApBilBd;;AU9sBI;EUoIE,yBjBvHY;AHqsBlB;;AuBxtBA;EACE,WAAW;AvB2tBb;;AuB5tBA;EAII,YAAY;EACZ,gBAAgB;EAChB,kBAAkB;AvB4tBtB;;AuBluBA;;EAUM,0BAAkB;EAAlB,uBAAkB;EAAlB,kBAAkB;EAClB,oBAAa;EAAb,aAAa;EACb,uBAAoB;EAApB,oBAAoB;AvB6tB1B;;AuBzuBA;EAgBI,kDAAuE;AvB6tB3E;;AuB7uBA;EAqBM,cAAc;AvB4tBpB;;AuBjvBA;EAyBM,YAAY;EACZ,qBAAqB;EACrB,eAAe;AvB4tBrB;;AuBvvBA;EA8BQ,cAAc;AvB6tBtB;;AuB3vBA;EAkCQ,oDAAyE;EACzE,gBAAgB;AvB6tBxB;;AuBhwBA;EAwCU,gBAAgB;EAChB,wBAAwB;AvB4tBlC;;AuBrwBA;EA4CU,qBAAqB;AvB6tB/B;;AuBzwBA;EA+CU,eAAe;AvB8tBzB;;AuB7wBA;EAsDM,0BAA0B;EAC1B,eAAe;EACf,gBAAgB;AvB2tBtB", "file": "adminlte.pages.css", "sourcesContent": ["/*!\n *   AdminLTE v3.2.0\n *     Only Pages\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n\n// Bootstrap\n// ---------------------------------------------------\n@import \"~bootstrap/scss/functions\";\n@import \"../bootstrap-variables\";\n@import \"~bootstrap/scss/mixins\";\n@import \"~bootstrap/scss/close\";\n\n// Variables and Mixins\n// ---------------------------------------------------\n@import \"../variables\";\n@import \"../variables-alt\";\n@import \"../mixins\";\n\n@import \"pages\";\n", "/*!\n *   AdminLTE v3.2.0\n *     Only Pages\n *   Author: Colorlib\n *   Website: AdminLTE.io <https://adminlte.io>\n *   License: Open source - MIT <https://opensource.org/licenses/MIT>\n */\n.close, .mailbox-attachment-close {\n  float: right;\n  font-size: 1.5rem;\n  font-weight: 700;\n  line-height: 1;\n  color: #000;\n  text-shadow: 0 1px 0 #fff;\n  opacity: .5;\n}\n\n.close:hover, .mailbox-attachment-close:hover {\n  color: #000;\n  text-decoration: none;\n}\n\n.close:not(:disabled):not(.disabled):hover, .mailbox-attachment-close:not(:disabled):not(.disabled):hover, .close:not(:disabled):not(.disabled):focus, .mailbox-attachment-close:not(:disabled):not(.disabled):focus {\n  opacity: .75;\n}\n\nbutton.close, button.mailbox-attachment-close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\na.close.disabled, a.disabled.mailbox-attachment-close {\n  pointer-events: none;\n}\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n  100% {\n    transform: none;\n  }\n}\n\n.mailbox-messages > .table {\n  margin: 0;\n}\n\n.mailbox-controls {\n  padding: 5px;\n}\n\n.mailbox-controls.with-border {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n}\n\n.mailbox-read-info {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n  padding: 10px;\n}\n\n.mailbox-read-info h3 {\n  font-size: 20px;\n  margin: 0;\n}\n\n.mailbox-read-info h5 {\n  margin: 0;\n  padding: 5px 0 0;\n}\n\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n\n.mailbox-read-message {\n  padding: 10px;\n}\n\n.mailbox-attachments {\n  padding-left: 0;\n  list-style: none;\n}\n\n.mailbox-attachments li {\n  border: 1px solid #eee;\n  float: left;\n  margin-bottom: 10px;\n  margin-right: 10px;\n  width: 200px;\n}\n\n.mailbox-attachment-name {\n  color: #666;\n  font-weight: 700;\n}\n\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n\n.mailbox-attachment-info {\n  background-color: #f8f9fa;\n  padding: 10px;\n}\n\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n}\n\n.mailbox-attachment-size > span {\n  display: inline-block;\n  padding-top: .75rem;\n}\n\n.mailbox-attachment-icon {\n  color: #666;\n  font-size: 65px;\n  max-height: 132.5px;\n  padding: 20px 10px;\n  text-align: center;\n}\n\n.mailbox-attachment-icon.has-img {\n  padding: 0;\n}\n\n.mailbox-attachment-icon.has-img > img {\n  height: auto;\n  max-width: 100%;\n}\n\n.lockscreen {\n  background-color: #e9ecef;\n}\n\n.lockscreen .lockscreen-name {\n  font-weight: 600;\n  text-align: center;\n}\n\n.lockscreen-logo {\n  font-size: 35px;\n  font-weight: 300;\n  margin-bottom: 25px;\n  text-align: center;\n}\n\n.lockscreen-logo a {\n  color: #495057;\n}\n\n.lockscreen-wrapper {\n  margin: 0 auto;\n  margin-top: 10%;\n  max-width: 400px;\n}\n\n.lockscreen-item {\n  border-radius: 4px;\n  background-color: #fff;\n  margin: 10px auto 30px;\n  padding: 0;\n  position: relative;\n  width: 290px;\n}\n\n.lockscreen-image {\n  border-radius: 50%;\n  background-color: #fff;\n  left: -10px;\n  padding: 5px;\n  position: absolute;\n  top: -25px;\n  z-index: 10;\n}\n\n.lockscreen-image > img {\n  border-radius: 50%;\n  height: 70px;\n  width: 70px;\n}\n\n.lockscreen-credentials {\n  margin-left: 70px;\n}\n\n.lockscreen-credentials .form-control {\n  border: 0;\n}\n\n.lockscreen-credentials .btn {\n  background-color: #fff;\n  border: 0;\n  padding: 0 10px;\n}\n\n.lockscreen-footer {\n  margin-top: 10px;\n}\n\n.dark-mode .lockscreen-item {\n  background-color: #343a40;\n}\n\n.dark-mode .lockscreen-logo a {\n  color: #fff;\n}\n\n.dark-mode .lockscreen-credentials .btn {\n  background-color: #343a40;\n}\n\n.dark-mode .lockscreen-image {\n  background-color: #6c757d;\n}\n\n.login-logo,\n.register-logo {\n  font-size: 2.1rem;\n  font-weight: 300;\n  margin-bottom: .9rem;\n  text-align: center;\n}\n\n.login-logo a,\n.register-logo a {\n  color: #495057;\n}\n\n.login-page,\n.register-page {\n  align-items: center;\n  background-color: #e9ecef;\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  justify-content: center;\n}\n\n.login-box,\n.register-box {\n  width: 360px;\n}\n\n@media (max-width: 576px) {\n  .login-box,\n  .register-box {\n    margin-top: .5rem;\n    width: 90%;\n  }\n}\n\n.login-box .card,\n.register-box .card {\n  margin-bottom: 0;\n}\n\n.login-card-body,\n.register-card-body {\n  background-color: #fff;\n  border-top: 0;\n  color: #666;\n  padding: 20px;\n}\n\n.login-card-body .input-group .form-control,\n.register-card-body .input-group .form-control {\n  border-right: 0;\n}\n\n.login-card-body .input-group .form-control:focus,\n.register-card-body .input-group .form-control:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control:focus ~ .input-group-prepend .input-group-text,\n.login-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control:focus ~ .input-group-prepend .input-group-text,\n.register-card-body .input-group .form-control:focus ~ .input-group-append .input-group-text {\n  border-color: #80bdff;\n}\n\n.login-card-body .input-group .form-control.is-valid:focus,\n.register-card-body .input-group .form-control.is-valid:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control.is-valid ~ .input-group-prepend .input-group-text,\n.login-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control.is-valid ~ .input-group-prepend .input-group-text,\n.register-card-body .input-group .form-control.is-valid ~ .input-group-append .input-group-text {\n  border-color: #28a745;\n}\n\n.login-card-body .input-group .form-control.is-invalid:focus,\n.register-card-body .input-group .form-control.is-invalid:focus {\n  box-shadow: none;\n}\n\n.login-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text,\n.register-card-body .input-group .form-control.is-invalid ~ .input-group-append .input-group-text {\n  border-color: #dc3545;\n}\n\n.login-card-body .input-group .input-group-text,\n.register-card-body .input-group .input-group-text {\n  background-color: transparent;\n  border-bottom-right-radius: 0.25rem;\n  border-left: 0;\n  border-top-right-radius: 0.25rem;\n  color: #777;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  padding: 0 20px 20px;\n  text-align: center;\n}\n\n.social-auth-links {\n  margin: 10px 0;\n}\n\n.dark-mode .login-card-body,\n.dark-mode .register-card-body {\n  background-color: #343a40;\n  border-color: #6c757d;\n  color: #fff;\n}\n\n.dark-mode .login-logo a,\n.dark-mode .register-logo a {\n  color: #fff;\n}\n\n.error-page {\n  margin: 20px auto 0;\n  width: 600px;\n}\n\n@media (max-width: 767.98px) {\n  .error-page {\n    width: 100%;\n  }\n}\n\n.error-page > .headline {\n  float: left;\n  font-size: 100px;\n  font-weight: 300;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .headline {\n    float: none;\n    text-align: center;\n  }\n}\n\n.error-page > .error-content {\n  display: block;\n  margin-left: 190px;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .error-content {\n    margin-left: 0;\n  }\n}\n\n.error-page > .error-content > h3 {\n  font-size: 25px;\n  font-weight: 300;\n}\n\n@media (max-width: 767.98px) {\n  .error-page > .error-content > h3 {\n    text-align: center;\n  }\n}\n\n.invoice {\n  background-color: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.125);\n  position: relative;\n}\n\n.invoice-title {\n  margin-top: 0;\n}\n\n.dark-mode .invoice {\n  background-color: #343a40;\n}\n\n.profile-user-img {\n  border: 3px solid #adb5bd;\n  margin: 0 auto;\n  padding: 3px;\n  width: 100px;\n}\n\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n\n.post {\n  border-bottom: 1px solid #adb5bd;\n  color: #666;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n}\n\n.post:last-of-type {\n  border-bottom: 0;\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n\n.post .user-block {\n  margin-bottom: 15px;\n  width: 100%;\n}\n\n.post .row {\n  width: 100%;\n}\n\n.dark-mode .post {\n  color: #fff;\n  border-color: #6c757d;\n}\n\n.product-image {\n  max-width: 100%;\n  height: auto;\n  width: 100%;\n}\n\n.product-image-thumbs {\n  align-items: stretch;\n  display: flex;\n  margin-top: 2rem;\n}\n\n.product-image-thumb {\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.075);\n  border-radius: 0.25rem;\n  background-color: #fff;\n  border: 1px solid #dee2e6;\n  display: flex;\n  margin-right: 1rem;\n  max-width: 7rem;\n  padding: 0.5rem;\n}\n\n.product-image-thumb img {\n  max-width: 100%;\n  height: auto;\n  align-self: center;\n}\n\n.product-image-thumb:hover {\n  opacity: .5;\n}\n\n.product-share a {\n  margin-right: .5rem;\n}\n\n.projects td {\n  vertical-align: middle;\n}\n\n.projects .list-inline {\n  margin-bottom: 0;\n}\n\n.projects img.table-avatar,\n.projects .table-avatar img {\n  border-radius: 50%;\n  display: inline;\n  width: 2.5rem;\n}\n\n.projects .project-state {\n  text-align: center;\n}\n\nbody.iframe-mode .main-sidebar {\n  display: none;\n}\n\nbody.iframe-mode .content-wrapper {\n  margin-left: 0 !important;\n  margin-top: 0 !important;\n  padding-bottom: 0 !important;\n}\n\nbody.iframe-mode .main-header,\nbody.iframe-mode .main-footer {\n  display: none;\n}\n\nbody.iframe-mode-fullscreen {\n  overflow: hidden;\n}\n\nbody.iframe-mode-fullscreen.layout-navbar-fixed .wrapper .content-wrapper {\n  margin-top: 0 !important;\n}\n\n.content-wrapper {\n  height: 100%;\n}\n\n.content-wrapper.iframe-mode .btn-iframe-close {\n  color: #dc3545;\n  position: absolute;\n  line-height: 1;\n  right: .125rem;\n  top: .125rem;\n  z-index: 10;\n  visibility: hidden;\n}\n\n.content-wrapper.iframe-mode .btn-iframe-close:hover, .content-wrapper.iframe-mode .btn-iframe-close:focus {\n  animation-name: fadeIn;\n  animation-duration: 0.3s;\n  animation-fill-mode: both;\n  visibility: visible;\n}\n\n@media (hover: none) and (pointer: coarse) {\n  .content-wrapper.iframe-mode .btn-iframe-close {\n    visibility: visible;\n  }\n}\n\n.content-wrapper.iframe-mode .navbar-nav {\n  overflow-y: auto;\n  width: 100%;\n}\n\n.content-wrapper.iframe-mode .navbar-nav .nav-link {\n  white-space: nowrap;\n}\n\n.content-wrapper.iframe-mode .navbar-nav .nav-item {\n  position: relative;\n}\n\n.content-wrapper.iframe-mode .navbar-nav .nav-item:hover .btn-iframe-close, .content-wrapper.iframe-mode .navbar-nav .nav-item:focus .btn-iframe-close {\n  animation-name: fadeIn;\n  animation-duration: 0.3s;\n  animation-fill-mode: both;\n  visibility: visible;\n}\n\n@media (hover: none) and (pointer: coarse) {\n  .content-wrapper.iframe-mode .navbar-nav .nav-item:hover .btn-iframe-close, .content-wrapper.iframe-mode .navbar-nav .nav-item:focus .btn-iframe-close {\n    visibility: visible;\n  }\n}\n\n.content-wrapper.iframe-mode .tab-content {\n  position: relative;\n}\n\n.content-wrapper.iframe-mode .tab-pane + .tab-empty {\n  display: none;\n}\n\n.content-wrapper.iframe-mode .tab-empty {\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.content-wrapper.iframe-mode .tab-loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  display: none;\n  background-color: #f4f6f9;\n}\n\n.content-wrapper.iframe-mode .tab-loading > div {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n}\n\n.content-wrapper.iframe-mode iframe {\n  border: 0;\n  width: 100%;\n  height: 100%;\n  margin-bottom: -8px;\n}\n\n.content-wrapper.iframe-mode iframe .content-wrapper {\n  padding-bottom: 0 !important;\n}\n\nbody.iframe-mode-fullscreen .content-wrapper.iframe-mode {\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  margin-left: 0 !important;\n  height: 100%;\n  min-height: 100%;\n  z-index: 1048;\n}\n\n.permanent-btn-iframe-close .btn-iframe-close {\n  animation: none !important;\n  visibility: visible !important;\n  opacity: 1;\n}\n\n.dark-mode .content-wrapper.iframe-mode .tab-loading {\n  background-color: #343a40;\n}\n\n.content-wrapper.kanban {\n  height: 1px;\n}\n\n.content-wrapper.kanban .content {\n  height: 100%;\n  overflow-x: auto;\n  overflow-y: hidden;\n}\n\n.content-wrapper.kanban .content .container,\n.content-wrapper.kanban .content .container-fluid {\n  width: max-content;\n  display: flex;\n  align-items: stretch;\n}\n\n.content-wrapper.kanban .content-header + .content {\n  height: calc(100% - ((2 * 15px) + (1.8rem * 1.2)));\n}\n\n.content-wrapper.kanban .card .card-body {\n  padding: .5rem;\n}\n\n.content-wrapper.kanban .card.card-row {\n  width: 340px;\n  display: inline-block;\n  margin: 0 .5rem;\n}\n\n.content-wrapper.kanban .card.card-row:first-child {\n  margin-left: 0;\n}\n\n.content-wrapper.kanban .card.card-row .card-body {\n  height: calc(100% - (12px + (1.8rem * 1.2) + .5rem));\n  overflow-y: auto;\n}\n\n.content-wrapper.kanban .card.card-row .card:last-child {\n  margin-bottom: 0;\n  border-bottom-width: 1px;\n}\n\n.content-wrapper.kanban .card.card-row .card .card-header {\n  padding: .5rem .75rem;\n}\n\n.content-wrapper.kanban .card.card-row .card .card-body {\n  padding: .75rem;\n}\n\n.content-wrapper.kanban .btn-tool.btn-link {\n  text-decoration: underline;\n  padding-left: 0;\n  padding-right: 0;\n}\n\n/*# sourceMappingURL=adminlte.pages.css.map */", ".close {\n  float: right;\n  @include font-size($close-font-size);\n  font-weight: $close-font-weight;\n  line-height: 1;\n  color: $close-color;\n  text-shadow: $close-text-shadow;\n  opacity: .5;\n\n  // Override <a>'s hover style\n  @include hover() {\n    color: $close-color;\n    text-decoration: none;\n  }\n\n  &:not(:disabled):not(.disabled) {\n    @include hover-focus() {\n      opacity: .75;\n    }\n  }\n}\n\n// Additional properties for button version\n// iOS requires the button element instead of an anchor tag.\n// If you want the anchor version, it requires `href=\"#\"`.\n// See https://developer.mozilla.org/en-US/docs/Web/Events/click#Safari_Mobile\n\n// stylelint-disable-next-line selector-no-qualifying-type\nbutton.close {\n  padding: 0;\n  background-color: transparent;\n  border: 0;\n}\n\n// Future-proof disabling of clicks on `<a>` elements\n\n// stylelint-disable-next-line selector-no-qualifying-type\na.close.disabled {\n  pointer-events: none;\n}\n", "// stylelint-disable property-blacklist, scss/dollar-variable-default\n\n// SCSS RFS mixin\n//\n// Automated responsive font sizes\n//\n// Licensed under MIT (https://github.com/twbs/rfs/blob/v8.x/LICENSE)\n\n// Configuration\n\n// Base font size\n$rfs-base-font-size: 1.25rem !default;\n$rfs-font-size-unit: rem !default;\n\n@if $rfs-font-size-unit != rem and $rfs-font-size-unit != px {\n  @error \"`#{$rfs-font-size-unit}` is not a valid unit for $rfs-font-size-unit. Use `px` or `rem`.\";\n}\n\n// Breakpoint at where font-size starts decreasing if screen width is smaller\n$rfs-breakpoint: 1200px !default;\n$rfs-breakpoint-unit: px !default;\n\n@if $rfs-breakpoint-unit != px and $rfs-breakpoint-unit != em and $rfs-breakpoint-unit != rem {\n  @error \"`#{$rfs-breakpoint-unit}` is not a valid unit for $rfs-breakpoint-unit. Use `px`, `em` or `rem`.\";\n}\n\n// Resize font size based on screen height and width\n$rfs-two-dimensional: false !default;\n\n// Factor of decrease\n$rfs-factor: 10 !default;\n\n@if type-of($rfs-factor) != \"number\" or $rfs-factor <= 1 {\n  @error \"`#{$rfs-factor}` is not a valid  $rfs-factor, it must be greater than 1.\";\n}\n\n// Generate enable or disable classes. Possibilities: false, \"enable\" or \"disable\"\n$rfs-class: false !default;\n\n// 1 rem = $rfs-rem-value px\n$rfs-rem-value: 16 !default;\n\n// Safari iframe resize bug: https://github.com/twbs/rfs/issues/14\n$rfs-safari-iframe-resize-bug-fix: false !default;\n\n// Disable RFS by setting $enable-responsive-font-sizes to false\n$enable-responsive-font-sizes: true !default;\n\n// Cache $rfs-base-font-size unit\n$rfs-base-font-size-unit: unit($rfs-base-font-size);\n\n@function divide($dividend, $divisor, $precision: 10) {\n  $sign: if($dividend > 0 and $divisor > 0 or $dividend < 0 and $divisor < 0, 1, -1);\n  $dividend: abs($dividend);\n  $divisor: abs($divisor);\n  @if $dividend == 0 {\n    @return 0;\n  }\n  @if $divisor == 0 {\n    @error \"Cannot divide by 0\";\n  }\n  $remainder: $dividend;\n  $result: 0;\n  $factor: 10;\n  @while ($remainder > 0 and $precision >= 0) {\n    $quotient: 0;\n    @while ($remainder >= $divisor) {\n      $remainder: $remainder - $divisor;\n      $quotient: $quotient + 1;\n    }\n    $result: $result * 10 + $quotient;\n    $factor: $factor * .1;\n    $remainder: $remainder * 10;\n    $precision: $precision - 1;\n    @if ($precision < 0 and $remainder >= $divisor * 5) {\n      $result: $result + 1;\n    }\n  }\n  $result: $result * $factor * $sign;\n  $dividend-unit: unit($dividend);\n  $divisor-unit: unit($divisor);\n  $unit-map: (\n    \"px\": 1px,\n    \"rem\": 1rem,\n    \"em\": 1em,\n    \"%\": 1%\n  );\n  @if ($dividend-unit != $divisor-unit and map-has-key($unit-map, $dividend-unit)) {\n    $result: $result * map-get($unit-map, $dividend-unit);\n  }\n  @return $result;\n}\n\n// Remove px-unit from $rfs-base-font-size for calculations\n@if $rfs-base-font-size-unit == \"px\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, $rfs-base-font-size * 0 + 1);\n}\n@else if $rfs-base-font-size-unit == \"rem\" {\n  $rfs-base-font-size: divide($rfs-base-font-size, divide($rfs-base-font-size * 0 + 1, $rfs-rem-value));\n}\n\n// Cache $rfs-breakpoint unit to prevent multiple calls\n$rfs-breakpoint-unit-cache: unit($rfs-breakpoint);\n\n// Remove unit from $rfs-breakpoint for calculations\n@if $rfs-breakpoint-unit-cache == \"px\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, $rfs-breakpoint * 0 + 1);\n}\n@else if $rfs-breakpoint-unit-cache == \"rem\" or $rfs-breakpoint-unit-cache == \"em\" {\n  $rfs-breakpoint: divide($rfs-breakpoint, divide($rfs-breakpoint * 0 + 1, $rfs-rem-value));\n}\n\n// Internal mixin that adds disable classes to the selector if needed.\n@mixin _rfs-disable-class {\n  @if $rfs-class == \"disable\" {\n    // Adding an extra class increases specificity, which prevents the media query to override the font size\n    &,\n    .disable-responsive-font-size &,\n    &.disable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin that adds enable classes to the selector if needed.\n@mixin _rfs-enable-class {\n  @if $rfs-class == \"enable\" {\n    .enable-responsive-font-size &,\n    &.enable-responsive-font-size {\n      @content;\n    }\n  }\n  @else {\n    @content;\n  }\n}\n\n// Internal mixin used to determine which media query needs to be used\n@mixin _rfs-media-query($mq-value) {\n  @if $rfs-two-dimensional {\n    @media (max-width: #{$mq-value}), (max-height: #{$mq-value}) {\n      @content;\n    }\n  }\n  @else {\n    @media (max-width: #{$mq-value}) {\n      @content;\n    }\n  }\n}\n\n// Responsive font size mixin\n@mixin rfs($fs, $important: false) {\n  // Cache $fs unit\n  $fs-unit: if(type-of($fs) == \"number\", unit($fs), false);\n\n  // Add !important suffix if needed\n  $rfs-suffix: if($important, \" !important\", \"\");\n\n  // If $fs isn't a number (like inherit) or $fs has a unit (not px or rem, like 1.5em) or $ is 0, just print the value\n  @if not $fs-unit or $fs-unit != \"\" and $fs-unit != \"px\" and $fs-unit != \"rem\" or $fs == 0 {\n    font-size: #{$fs}#{$rfs-suffix};\n  }\n  @else {\n    // Remove unit from $fs for calculations\n    @if $fs-unit == \"px\" {\n      $fs: divide($fs, $fs * 0 + 1);\n    }\n    @else if $fs-unit == \"rem\" {\n      $fs: divide($fs, divide($fs * 0 + 1, $rfs-rem-value));\n    }\n\n    // Set default font size\n    $rfs-static: if($rfs-font-size-unit == rem, #{divide($fs, $rfs-rem-value)}rem, #{$fs}px);\n\n    // Only add the media query if the font size is bigger than the minimum font size\n    @if $fs <= $rfs-base-font-size or not $enable-responsive-font-sizes {\n      font-size: #{$rfs-static}#{$rfs-suffix};\n    }\n    @else {\n      // Calculate the minimum font size for $fs\n      $fs-min: $rfs-base-font-size + divide($fs - $rfs-base-font-size, $rfs-factor);\n\n      // Calculate difference between $fs and the minimum font size\n      $fs-diff: $fs - $fs-min;\n\n      // Base font-size formatting\n      $min-width: if($rfs-font-size-unit == rem, #{divide($fs-min, $rfs-rem-value)}rem, #{$fs-min}px);\n\n      // Use `vmin` if two-dimensional is enabled\n      $variable-unit: if($rfs-two-dimensional, vmin, vw);\n\n      // Calculate the variable width between 0 and $rfs-breakpoint\n      $variable-width: #{divide($fs-diff * 100, $rfs-breakpoint)}#{$variable-unit};\n\n      // Set the calculated font-size\n      $rfs-fluid: calc(#{$min-width} + #{$variable-width}) #{$rfs-suffix};\n\n      // Breakpoint formatting\n      $mq-value: if($rfs-breakpoint-unit == px, #{$rfs-breakpoint}px, #{divide($rfs-breakpoint, $rfs-rem-value)}#{$rfs-breakpoint-unit});\n\n      @include _rfs-disable-class {\n        font-size: #{$rfs-static}#{$rfs-suffix};\n      }\n\n      @include _rfs-media-query($mq-value) {\n        @include _rfs-enable-class {\n          font-size: $rfs-fluid;\n        }\n\n        // Include safari iframe resize fix if needed\n        min-width: if($rfs-safari-iframe-resize-bug-fix, (0 * 1vw), null);\n      }\n    }\n  }\n}\n\n// The font-size & responsive-font-size mixins use RFS to rescale the font size\n@mixin font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n\n@mixin responsive-font-size($fs, $important: false) {\n  @include rfs($fs, $important);\n}\n", "// Variables\n//\n// Variables should follow the `$component-state-property-size` formula for\n// consistent naming. Ex: $nav-link-disabled-color and $modal-content-box-shadow-xs.\n\n\n//\n// Color system\n//\n\n// stylelint-disable\n$white:    #fff !default;\n$gray-100: #f8f9fa !default;\n$gray-200: #e9ecef !default;\n$gray-300: #dee2e6 !default;\n$gray-400: #ced4da !default;\n$gray-500: #adb5bd !default;\n$gray-600: #6c757d !default;\n$gray-700: #495057 !default;\n$gray-800: #343a40 !default;\n$gray-900: #212529 !default;\n$black:    #000 !default;\n\n$grays: () !default;\n$grays: map-merge((\n  \"100\": $gray-100,\n  \"200\": $gray-200,\n  \"300\": $gray-300,\n  \"400\": $gray-400,\n  \"500\": $gray-500,\n  \"600\": $gray-600,\n  \"700\": $gray-700,\n  \"800\": $gray-800,\n  \"900\": $gray-900\n), $grays);\n\n$blue:    #007bff !default;\n$indigo:  #6610f2 !default;\n$purple:  #6f42c1 !default;\n$pink:    #e83e8c !default;\n$red:     #dc3545 !default;\n$orange:  #fd7e14 !default;\n$yellow:  #ffc107 !default;\n$green:   #28a745 !default;\n$teal:    #20c997 !default;\n$cyan:    #17a2b8 !default;\n\n$colors: () !default;\n$colors: map-merge((\n  \"blue\":       $blue,\n  \"indigo\":     $indigo,\n  \"purple\":     $purple,\n  \"pink\":       $pink,\n  \"red\":        $red,\n  \"orange\":     $orange,\n  \"yellow\":     $yellow,\n  \"green\":      $green,\n  \"teal\":       $teal,\n  \"cyan\":       $cyan,\n  \"white\":      $white,\n  \"gray\":       $gray-600,\n  \"gray-dark\":  $gray-800\n), $colors);\n\n$primary:       $blue !default;\n$secondary:     $gray-600 !default;\n$success:       $green !default;\n$info:          $cyan !default;\n$warning:       $yellow !default;\n$danger:        $red !default;\n$light:         $gray-100 !default;\n$dark:          $gray-800 !default;\n\n$theme-colors: () !default;\n$theme-colors: map-merge((\n  \"primary\":    $primary,\n  \"secondary\":  $secondary,\n  \"success\":    $success,\n  \"info\":       $info,\n  \"warning\":    $warning,\n  \"danger\":     $danger,\n  \"light\":      $light,\n  \"dark\":       $dark\n), $theme-colors);\n// stylelint-enable\n\n// Set a specific jump point for requesting color jumps\n$theme-color-interval:      8% !default;\n\n// The yiq lightness value that determines when the lightness of color changes from \"dark\" to \"light\". Acceptable values are between 0 and 255.\n$yiq-contrasted-threshold: 150 !default;\n\n// Customize the light and dark text colors for use in our YIQ color contrast function.\n$yiq-text-dark: #1f2d3d !default;\n$yiq-text-light: $white !default;\n\n// Options\n//\n// Quickly modify global styling by enabling or disabling optional features.\n\n$enable-caret:                                true !default;\n$enable-rounded:                              true !default;\n$enable-shadows:                              true !default;\n$enable-gradients:                            false !default;\n$enable-transitions:                          true !default;\n$enable-prefers-reduced-motion-media-query:   true !default;\n$enable-hover-media-query:                    false !default; // Deprecated, no longer affects any compiled CSS\n$enable-grid-classes:                         true !default;\n$enable-pointer-cursor-for-buttons:           true !default;\n$enable-print-styles:                         true !default;\n$enable-responsive-font-sizes:                false !default;\n$enable-validation-icons:                     true !default;\n$enable-deprecation-messages:                 true !default;\n\n// Characters which are escaped by the escape-svg function\n$escaped-characters: (\n  (\"<\", \"%3c\"),\n  (\">\", \"%3e\"),\n  (\"#\", \"%23\"),\n  (\"(\", \"%28\"),\n  (\")\", \"%29\"),\n) !default;\n\n\n// Spacing\n//\n// Control the default styling of most Bootstrap elements by modifying these\n// variables. Mostly focused on spacing.\n// You can add more entries to the $spacers map, should you need more variation.\n\n// stylelint-disable\n$spacer: 1rem !default;\n$spacers: () !default;\n$spacers: map-merge((\n  0: 0,\n  1: ($spacer * .25),\n  2: ($spacer * .5),\n  3: $spacer,\n  4: ($spacer * 1.5),\n  5: ($spacer * 3)\n), $spacers);\n\n// This variable affects the `.h-*` and `.w-*` classes.\n$sizes: () !default;\n$sizes: map-merge((\n  25: 25%,\n  50: 50%,\n  75: 75%,\n  100: 100%\n), $sizes);\n// stylelint-enable\n\n// Body\n//\n// Settings for the `<body>` element.\n\n$body-bg:                   $white !default;\n$body-color:                $gray-900 !default;\n\n// Links\n//\n// Style anchor elements.\n\n$link-color:                theme-color(\"primary\") !default;\n$link-decoration:           none !default;\n$link-hover-color:          darken($link-color, 15%) !default;\n$link-hover-decoration:     none !default;\n\n// Paragraphs\n//\n// Style p element.\n\n$paragraph-margin-bottom:   1rem !default;\n\n\n// Grid breakpoints\n//\n// Define the minimum dimensions at which your layout will change,\n// adapting to different screen sizes, for use in media queries.\n\n$grid-breakpoints: (\n  xs: 0,\n  sm: 576px,\n  md: 768px,\n  lg: 992px,\n  xl: 1200px\n) !default;\n\n@include _assert-ascending($grid-breakpoints, \"$grid-breakpoints\");\n@include _assert-starts-at-zero($grid-breakpoints);\n\n\n// Grid containers\n//\n// Define the maximum width of `.container` for different screen sizes.\n\n$container-max-widths: (\n  sm: 540px,\n  md: 720px,\n  lg: 960px,\n  xl: 1140px\n) !default;\n\n@include _assert-ascending($container-max-widths, \"$container-max-widths\");\n\n\n// Grid columns\n//\n// Set the number of columns and specify the width of the gutters.\n\n$grid-columns:                12 !default;\n$grid-gutter-width:           15px !default;\n\n// Components\n//\n// Define common padding and border radius sizes and more.\n\n$line-height-lg:              1.5 !default;\n$line-height-sm:              1.5 !default;\n\n$border-width:                1px !default;\n$border-color:                $gray-300 !default;\n\n$border-radius:               .25rem !default;\n$border-radius-lg:            .3rem !default;\n$border-radius-sm:            .2rem !default;\n\n$component-active-color:      $white !default;\n$component-active-bg:         theme-color(\"primary\") !default;\n\n$caret-width:                 .3em !default;\n\n$transition-base:             all .2s ease-in-out !default;\n$transition-fade:             opacity .15s linear !default;\n$transition-collapse:         height .35s ease !default;\n\n\n// Fonts\n//\n// Font, line-height, and color for body text, headings, and more.\n\n// stylelint-disable value-keyword-case\n$font-family-sans-serif:      \"Source Sans Pro\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\" !default;\n$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace !default;\n$font-family-base:            $font-family-sans-serif !default;\n// stylelint-enable value-keyword-case\n\n$font-size-base:              1rem !default; // Assumes the browser default, typically `16px`\n$font-size-lg:                ($font-size-base * 1.25) !default;\n$font-size-sm:                ($font-size-base * .875) !default;\n\n$font-weight-light:           300 !default;\n$font-weight-normal:          400 !default;\n$font-weight-bold:            700 !default;\n\n$font-weight-base:            $font-weight-normal !default;\n$line-height-base:            1.5 !default;\n\n$h1-font-size:                $font-size-base * 2.5 !default;\n$h2-font-size:                $font-size-base * 2 !default;\n$h3-font-size:                $font-size-base * 1.75 !default;\n$h4-font-size:                $font-size-base * 1.5 !default;\n$h5-font-size:                $font-size-base * 1.25 !default;\n$h6-font-size:                $font-size-base !default;\n\n$headings-margin-bottom:      ($spacer * .5) !default;\n$headings-font-family:        inherit !default;\n$headings-font-weight:        500 !default;\n$headings-line-height:        1.2 !default;\n$headings-color:              inherit !default;\n\n$display1-size:               6rem !default;\n$display2-size:               5.5rem !default;\n$display3-size:               4.5rem !default;\n$display4-size:               3.5rem !default;\n\n$display1-weight:             300 !default;\n$display2-weight:             300 !default;\n$display3-weight:             300 !default;\n$display4-weight:             300 !default;\n$display-line-height:         $headings-line-height !default;\n\n$lead-font-size:              ($font-size-base * 1.25) !default;\n$lead-font-weight:            300 !default;\n\n$small-font-size:             80% !default;\n\n$text-muted:                  $gray-600 !default;\n\n$blockquote-small-color:      $gray-600 !default;\n$blockquote-font-size:        ($font-size-base * 1.25) !default;\n\n$hr-border-color:             rgba($black, .1) !default;\n$hr-border-width:             $border-width !default;\n\n$mark-padding:                .2em !default;\n\n$dt-font-weight:              $font-weight-bold !default;\n\n$kbd-box-shadow:              inset 0 -.1rem 0 rgba($black, .25) !default;\n$nested-kbd-font-weight:      $font-weight-bold !default;\n\n$list-inline-padding:         .5rem !default;\n\n$mark-bg:                     #fcf8e3 !default;\n\n$hr-margin-y:                 $spacer !default;\n\n\n// Tables\n//\n// Customizes the `.table` component with basic values, each used across all table variations.\n\n$table-cell-padding:          .75rem !default;\n$table-cell-padding-sm:       .3rem !default;\n\n$table-bg:                    transparent !default;\n$table-accent-bg:             rgba($black, .05) !default;\n$table-hover-bg:              rgba($black, .075) !default;\n$table-active-bg:             $table-hover-bg !default;\n\n$table-border-width:          $border-width !default;\n$table-border-color:          $gray-300 !default;\n\n$table-head-bg:               $gray-200 !default;\n$table-head-color:            $gray-700 !default;\n\n$table-dark-bg:               $gray-900 !default;\n$table-dark-accent-bg:        rgba($white, .05) !default;\n$table-dark-hover-bg:         rgba($white, .075) !default;\n$table-dark-border-color:     lighten($gray-900, 10%) !default;\n$table-dark-color:            $body-bg !default;\n\n\n// Buttons + Forms\n//\n// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.\n\n$input-btn-padding-y:         .375rem !default;\n$input-btn-padding-x:         .75rem !default;\n$input-btn-line-height:       $line-height-base !default;\n\n$input-btn-focus-width:       .2rem !default;\n$input-btn-focus-color:       rgba($component-active-bg, .25) !default;\n$input-btn-focus-box-shadow:  0 0 0 $input-btn-focus-width $input-btn-focus-color !default;\n\n$input-btn-padding-y-sm:      .25rem !default;\n$input-btn-padding-x-sm:      .5rem !default;\n$input-btn-line-height-sm:    $line-height-sm !default;\n\n$input-btn-padding-y-lg:      .5rem !default;\n$input-btn-padding-x-lg:      1rem !default;\n$input-btn-line-height-lg:    $line-height-lg !default;\n\n$input-btn-border-width:      $border-width !default;\n\n\n// Buttons\n//\n// For each of Bootstrap's buttons, define text, background, and border color.\n\n$btn-padding-y:               $input-btn-padding-y !default;\n$btn-padding-x:               $input-btn-padding-x !default;\n$btn-line-height:             $input-btn-line-height !default;\n\n$btn-padding-y-sm:            $input-btn-padding-y-sm !default;\n$btn-padding-x-sm:            $input-btn-padding-x-sm !default;\n$btn-line-height-sm:          $input-btn-line-height-sm !default;\n\n$btn-padding-y-lg:            $input-btn-padding-y-lg !default;\n$btn-padding-x-lg:            $input-btn-padding-x-lg !default;\n$btn-line-height-lg:          $input-btn-line-height-lg !default;\n\n$btn-border-width:            $input-btn-border-width !default;\n\n$btn-font-weight:             $font-weight-normal !default;\n$btn-box-shadow:              none !default;\n$btn-focus-width:             0 !default;\n$btn-focus-box-shadow:        none !default;\n$btn-disabled-opacity:        .65 !default;\n$btn-active-box-shadow:       none !default;\n\n$btn-link-disabled-color:     $gray-600 !default;\n\n$btn-block-spacing-y:         .5rem !default;\n\n// Allows for customizing button radius independently from global border radius\n$btn-border-radius:           $border-radius !default;\n$btn-border-radius-lg:        $border-radius-lg !default;\n$btn-border-radius-sm:        $border-radius-sm !default;\n\n$btn-transition:              color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n\n// Forms\n\n$input-padding-y:                       $input-btn-padding-y !default;\n$input-padding-x:                       $input-btn-padding-x !default;\n$input-line-height:                     $input-btn-line-height !default;\n\n$input-padding-y-sm:                    $input-btn-padding-y-sm !default;\n$input-padding-x-sm:                    $input-btn-padding-x-sm !default;\n$input-line-height-sm:                  $input-btn-line-height-sm !default;\n\n$input-padding-y-lg:                    $input-btn-padding-y-lg !default;\n$input-padding-x-lg:                    $input-btn-padding-x-lg !default;\n$input-line-height-lg:                  $input-btn-line-height-lg !default;\n\n$input-bg:                              $white !default;\n$input-disabled-bg:                     $gray-200 !default;\n\n$input-color:                           $gray-700 !default;\n$input-border-color:                    $gray-400 !default;\n$input-border-width:                    $input-btn-border-width !default;\n$input-box-shadow:                      inset 0 0 0 rgba($black, 0) !default;\n\n$input-border-radius:                   $border-radius !default;\n$input-border-radius-lg:                $border-radius-lg !default;\n$input-border-radius-sm:                $border-radius-sm !default;\n\n$input-focus-bg:                        $input-bg !default;\n$input-focus-border-color:              lighten($component-active-bg, 25%) !default;\n$input-focus-color:                     $input-color !default;\n$input-focus-width:                     0 !default;\n$input-focus-box-shadow:                none !default;\n\n$input-placeholder-color:               lighten($gray-600, 15%) !default;\n\n$input-height-border:                   $input-border-width * 2 !default;\n\n$input-height-inner:                    ($font-size-base * $input-btn-line-height) + ($input-btn-padding-y * 2) !default;\n$input-height-inner-half:               calc(#{$input-line-height * .5em} + #{$input-padding-y}) !default;\n$input-height-inner-quarter:            calc(#{$input-line-height * .25em} + #{$input-padding-y * .5}) !default;\n\n$input-height:                          calc(#{$input-height-inner} + #{$input-height-border}) !default;\n\n$input-height-inner-sm:                 ($font-size-sm * $input-btn-line-height-sm) + ($input-btn-padding-y-sm * 2) !default;\n$input-height-sm:                       calc(#{$input-height-inner-sm} + #{$input-height-border}) !default;\n\n$input-height-inner-lg:                 ($font-size-lg * $input-btn-line-height-lg) + ($input-btn-padding-y-lg * 2) !default;\n$input-height-lg:                       calc(#{$input-height-inner-lg} + #{$input-height-border}) !default;\n\n$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out !default;\n\n$form-text-margin-top:                  .25rem !default;\n\n$form-check-input-gutter:               1.25rem !default;\n$form-check-input-margin-y:             .3rem !default;\n$form-check-input-margin-x:             .25rem !default;\n\n$form-check-inline-margin-x:            .75rem !default;\n$form-check-inline-input-margin-x:      .3125rem !default;\n\n$form-group-margin-bottom:              1rem !default;\n\n$input-group-addon-color:               $input-color !default;\n$input-group-addon-bg:                  $gray-200 !default;\n$input-group-addon-border-color:        $input-border-color !default;\n\n$custom-control-gutter:                 .5rem !default;\n$custom-control-spacer-x:               1rem !default;\n\n$custom-control-indicator-size:         1rem !default;\n$custom-control-indicator-bg:           $gray-300 !default;\n$custom-control-indicator-bg-size:      50% 50% !default;\n$custom-control-indicator-box-shadow:   inset 0 .25rem .25rem rgba($black, .1) !default;\n\n$custom-control-indicator-disabled-bg:          $gray-200 !default;\n$custom-control-label-disabled-color:           $gray-600 !default;\n\n$custom-control-indicator-checked-color:        $component-active-color !default;\n$custom-control-indicator-checked-bg:           $component-active-bg !default;\n$custom-control-indicator-checked-disabled-bg:  rgba(theme-color(\"primary\"), .5) !default;\n$custom-control-indicator-checked-box-shadow:   none !default;\n\n$custom-control-indicator-focus-box-shadow:     0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n$custom-control-indicator-active-color:         $component-active-color !default;\n$custom-control-indicator-active-bg:            lighten($component-active-bg, 35%) !default;\n$custom-control-indicator-active-box-shadow:    none !default;\n\n$custom-checkbox-indicator-border-radius:       $border-radius !default;\n$custom-checkbox-indicator-icon-checked:        str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='#{$custom-control-indicator-checked-color}' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-checkbox-indicator-indeterminate-bg:    $component-active-bg !default;\n$custom-checkbox-indicator-indeterminate-color: $custom-control-indicator-checked-color !default;\n$custom-checkbox-indicator-icon-indeterminate:  str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='#{$custom-checkbox-indicator-indeterminate-color}' d='M0 2h4'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$custom-checkbox-indicator-indeterminate-box-shadow: none !default;\n\n$custom-radio-indicator-border-radius:          50% !default;\n$custom-radio-indicator-icon-checked:           str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='#{$custom-control-indicator-checked-color}'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$custom-select-padding-y:           .375rem !default;\n$custom-select-padding-x:          .75rem !default;\n$custom-select-height:              $input-height !default;\n$custom-select-indicator-padding:   1rem !default; // Extra padding to account for the presence of the background-image based indicator\n$custom-select-line-height:         $input-btn-line-height !default;\n$custom-select-color:               $input-color !default;\n$custom-select-disabled-color:      $gray-600 !default;\n$custom-select-bg:                  $white !default;\n$custom-select-disabled-bg:         $gray-200 !default;\n$custom-select-bg-size:             8px 10px !default; // In pixels because image dimensions\n$custom-select-indicator-color:     $gray-800 !default;\n$custom-select-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-background:          escape-svg($custom-select-indicator) right $custom-select-padding-x center / $custom-select-bg-size no-repeat !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n$custom-select-border-width:        $input-btn-border-width !default;\n$custom-select-border-color:        $input-border-color !default;\n$custom-select-border-radius:       $border-radius !default;\n\n$custom-select-focus-border-color:  $input-focus-border-color !default;\n$custom-select-focus-box-shadow:    none !default;\n\n$custom-select-font-size-sm:        75% !default;\n$custom-select-height-sm:           $input-height-sm !default;\n\n$custom-select-font-size-lg:        125% !default;\n$custom-select-height-lg:           $input-height-lg !default;\n\n$custom-file-height:                $input-height !default;\n$custom-file-focus-border-color:    $input-focus-border-color !default;\n$custom-file-focus-box-shadow:      $custom-select-focus-box-shadow !default;\n\n$custom-file-padding-y:             $input-btn-padding-y !default;\n$custom-file-padding-x:             $input-btn-padding-x !default;\n$custom-file-line-height:           $input-btn-line-height !default;\n$custom-file-color:                 $input-color !default;\n$custom-file-bg:                    $input-bg !default;\n$custom-file-border-width:          $input-btn-border-width !default;\n$custom-file-border-color:          $input-border-color !default;\n$custom-file-border-radius:         $input-border-radius !default;\n$custom-file-box-shadow:            $custom-select-focus-box-shadow !default;\n$custom-file-button-color:          $custom-file-color !default;\n$custom-file-button-bg:             $input-group-addon-bg !default;\n$custom-file-text: (\n  en: \"Browse\"\n) !default;\n\n$custom-range-thumb-focus-box-shadow:        0 0 0 1px $body-bg, $input-btn-focus-box-shadow !default;\n\n\n// Form validation\n$form-feedback-margin-top:          $form-text-margin-top !default;\n$form-feedback-font-size:           $small-font-size !default;\n$form-feedback-valid-color:         theme-color(\"success\") !default;\n$form-feedback-invalid-color:       theme-color(\"danger\") !default;\n\n\n// Dropdowns\n//\n// Dropdown menu container and contents.\n\n$dropdown-min-width:                10rem !default;\n$dropdown-padding-y:                .5rem !default;\n$dropdown-spacer:                   .125rem !default;\n$dropdown-bg:                       $white !default;\n$dropdown-border-color:             rgba($black, .15) !default;\n$dropdown-border-radius:            $border-radius !default;\n$dropdown-border-width:             $border-width !default;\n$dropdown-divider-bg:               $gray-200 !default;\n$dropdown-box-shadow:               0 .5rem 1rem rgba($black, .175) !default;\n\n$dropdown-link-color:               $gray-900 !default;\n$dropdown-link-hover-color:         darken($gray-900, 5%) !default;\n$dropdown-link-hover-bg:            $gray-100 !default;\n\n$dropdown-link-active-color:        $component-active-color !default;\n$dropdown-link-active-bg:           $component-active-bg !default;\n\n$dropdown-link-disabled-color:      $gray-600 !default;\n\n$dropdown-item-padding-y:           .25rem !default;\n$dropdown-item-padding-x:           1rem !default;\n\n$dropdown-header-color:             $gray-600 !default;\n\n\n// Z-index master list\n//\n// Warning: Avoid customizing these values. They're used for a bird's eye view\n// of components dependent on the z-axis and are designed to all work together.\n\n$zindex-dropdown:                   1000 !default;\n$zindex-sticky:                     1020 !default;\n$zindex-fixed:                      1030 !default;\n$zindex-modal-backdrop:             1040 !default;\n$zindex-modal:                      1050 !default;\n$zindex-popover:                    1060 !default;\n$zindex-tooltip:                    1070 !default;\n\n// Navs\n\n$nav-link-padding-y:                .5rem !default;\n$nav-link-padding-x:                1rem !default;\n$nav-link-disabled-color:           $gray-600 !default;\n\n$nav-tabs-border-color:             $gray-300 !default;\n$nav-tabs-border-width:             $border-width !default;\n$nav-tabs-border-radius:            $border-radius !default;\n$nav-tabs-link-hover-border-color:  $gray-200 $gray-200 $nav-tabs-border-color !default;\n$nav-tabs-link-active-color:        $gray-700 !default;\n$nav-tabs-link-active-bg:           $body-bg !default;\n$nav-tabs-link-active-border-color: $gray-300 $gray-300 $nav-tabs-link-active-bg !default;\n\n$nav-pills-border-radius:           $border-radius !default;\n$nav-pills-link-active-color:       $component-active-color !default;\n$nav-pills-link-active-bg:          $component-active-bg !default;\n\n// Navbar\n\n$navbar-padding-y:                  ($spacer * .5) !default;\n$navbar-padding-x:                  ($spacer * .5) !default;\n\n$navbar-nav-link-padding-x:         1rem !default;\n\n$navbar-brand-font-size:            $font-size-lg !default;\n// Compute the navbar-brand padding-y so the navbar-brand will have the same height as navbar-text and nav-link\n$nav-link-height:                   ($font-size-base * $line-height-base + $nav-link-padding-y * 2) !default;\n$navbar-brand-height:               $navbar-brand-font-size * $line-height-base !default;\n$navbar-brand-padding-y:            ($nav-link-height - $navbar-brand-height) * .5 !default;\n\n$navbar-toggler-padding-y:          .25rem !default;\n$navbar-toggler-padding-x:          .75rem !default;\n$navbar-toggler-font-size:          $font-size-lg !default;\n$navbar-toggler-border-radius:      $btn-border-radius !default;\n\n$navbar-dark-color:                 rgba($white, .75) !default;\n$navbar-dark-hover-color:           rgba($white, 1) !default;\n$navbar-dark-active-color:          $white !default;\n$navbar-dark-disabled-color:        rgba($white, .25) !default;\n$navbar-dark-toggler-icon-bg:       str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-dark-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-dark-toggler-border-color:  rgba($white, .1) !default;\n\n$navbar-light-color:                rgba($black, .5) !default;\n$navbar-light-hover-color:          rgba($black, .7) !default;\n$navbar-light-active-color:         rgba($black, .9) !default;\n$navbar-light-disabled-color:       rgba($black, .3) !default;\n$navbar-light-toggler-icon-bg:      str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#{$navbar-light-color}' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$navbar-light-toggler-border-color: rgba($black, .1) !default;\n\n// Pagination\n\n$pagination-padding-y:              .5rem !default;\n$pagination-padding-x:              .75rem !default;\n$pagination-padding-y-sm:           .25rem !default;\n$pagination-padding-x-sm:           .5rem !default;\n$pagination-padding-y-lg:           .75rem !default;\n$pagination-padding-x-lg:           1.5rem !default;\n$pagination-line-height:            1.25 !default;\n\n$pagination-color:                  $link-color !default;\n$pagination-bg:                     $white !default;\n$pagination-border-width:           $border-width !default;\n$pagination-border-color:           $gray-300 !default;\n\n$pagination-focus-box-shadow:       $input-btn-focus-box-shadow !default;\n\n$pagination-hover-color:            $link-hover-color !default;\n$pagination-hover-bg:               $gray-200 !default;\n$pagination-hover-border-color:     $gray-300 !default;\n\n$pagination-active-color:           $component-active-color !default;\n$pagination-active-bg:              $component-active-bg !default;\n$pagination-active-border-color:    $pagination-active-bg !default;\n\n$pagination-disabled-color:         $gray-600 !default;\n$pagination-disabled-bg:            $white !default;\n$pagination-disabled-border-color:  $gray-300 !default;\n\n\n// Jumbotron\n\n$jumbotron-padding:                 2rem !default;\n$jumbotron-bg:                      $gray-200 !default;\n\n\n// Cards\n\n$card-spacer-y:                     .75rem !default;\n$card-spacer-x:                     1.25rem !default;\n$card-border-width:                 0 !default; //$border-width !default;\n$card-border-radius:                $border-radius !default;\n$card-border-color:                 rgba($black, .125) !default;\n$card-inner-border-radius:          calc(#{$card-border-radius} - #{$card-border-width}) !default;\n$card-cap-bg:                       rgba($black, .03) !default;\n$card-bg:                           $white !default;\n\n$card-img-overlay-padding:          1.25rem !default;\n\n$card-group-margin:                 ($grid-gutter-width * .5) !default;\n$card-deck-margin:                  $card-group-margin !default;\n\n$card-columns-count:                3 !default;\n$card-columns-gap:                  1.25rem !default;\n$card-columns-margin:               $card-spacer-y !default;\n\n\n// Tooltips\n\n$tooltip-font-size:           $font-size-sm !default;\n$tooltip-max-width:           200px !default;\n$tooltip-color:               $white !default;\n$tooltip-bg:                  $black !default;\n$tooltip-border-radius:        $border-radius !default;\n$tooltip-opacity:             .9 !default;\n$tooltip-padding-y:           .25rem !default;\n$tooltip-padding-x:           .5rem !default;\n$tooltip-margin:              0 !default;\n\n$tooltip-arrow-width:         .8rem !default;\n$tooltip-arrow-height:        .4rem !default;\n$tooltip-arrow-color:         $tooltip-bg !default;\n\n// Form tooltips must come after regular tooltips\n$form-feedback-tooltip-padding-y:     $tooltip-padding-y !default;\n$form-feedback-tooltip-padding-x:     $tooltip-padding-x !default;\n$form-feedback-tooltip-font-size:     $tooltip-font-size !default;\n$form-feedback-tooltip-line-height:   $line-height-base !default;\n$form-feedback-tooltip-opacity:       $tooltip-opacity !default;\n$form-feedback-tooltip-border-radius: $tooltip-border-radius !default;\n\n// Popovers\n\n$popover-font-size:                 $font-size-sm !default;\n$popover-bg:                        $white !default;\n$popover-max-width:                 276px !default;\n$popover-border-width:              $border-width !default;\n$popover-border-color:              rgba($black, .2) !default;\n$popover-border-radius:             $border-radius-lg !default;\n$popover-box-shadow:                0 .25rem .5rem rgba($black, .2) !default;\n\n$popover-header-bg:                 darken($popover-bg, 3%) !default;\n$popover-header-color:              $headings-color !default;\n$popover-header-padding-y:          .5rem !default;\n$popover-header-padding-x:          .75rem !default;\n\n$popover-body-color:                $body-color !default;\n$popover-body-padding-y:            $popover-header-padding-y !default;\n$popover-body-padding-x:            $popover-header-padding-x !default;\n\n$popover-arrow-width:               1rem !default;\n$popover-arrow-height:              .5rem !default;\n$popover-arrow-color:               $popover-bg !default;\n\n$popover-arrow-outer-color:         fade-in($popover-border-color, .05) !default;\n\n\n// Badges\n\n$badge-font-size:                   75% !default;\n$badge-font-weight:                 $font-weight-bold !default;\n$badge-padding-y:                   .25em !default;\n$badge-padding-x:                   .4em !default;\n$badge-border-radius:               $border-radius !default;\n\n$badge-pill-padding-x:              .6em !default;\n// Use a higher than normal value to ensure completely rounded edges when\n// customizing padding or font-size on labels.\n$badge-pill-border-radius:          10rem !default;\n\n\n// Modals\n\n// Padding applied to the modal body\n$modal-inner-padding:         1rem !default;\n\n$modal-dialog-margin:         .5rem !default;\n$modal-dialog-margin-y-sm-up: 1.75rem !default;\n\n$modal-title-line-height:           $line-height-base !default;\n\n$modal-content-bg:               $white !default;\n$modal-content-border-color:     rgba($black, .2) !default;\n$modal-content-border-width:     $border-width !default;\n$modal-content-border-radius:    $border-radius-lg !default;\n$modal-content-box-shadow-xs:    0 .25rem .5rem rgba($black, .5) !default;\n$modal-content-box-shadow-sm-up: 0 .5rem 1rem rgba($black, .5) !default;\n\n$modal-backdrop-bg:           $black !default;\n$modal-backdrop-opacity:      .5 !default;\n$modal-header-border-color:   $gray-200 !default;\n$modal-footer-border-color:   $modal-header-border-color !default;\n$modal-header-border-width:   $modal-content-border-width !default;\n$modal-footer-border-width:   $modal-header-border-width !default;\n$modal-header-padding:        1rem !default;\n\n$modal-lg:                          800px !default;\n$modal-md:                          500px !default;\n$modal-sm:                          300px !default;\n\n$modal-transition:                  transform .3s ease-out !default;\n\n\n// Alerts\n//\n// Define alert colors, border radius, and padding.\n\n$alert-padding-y:                   .75rem !default;\n$alert-padding-x:                   1.25rem !default;\n$alert-margin-bottom:               1rem !default;\n$alert-border-radius:               $border-radius !default;\n$alert-link-font-weight:            $font-weight-bold !default;\n$alert-border-width:                $border-width !default;\n\n$alert-bg-level:                    -10 !default;\n$alert-border-level:                -9 !default;\n$alert-color-level:                 6 !default;\n\n\n// Progress bars\n\n$progress-height:                   1rem !default;\n$progress-font-size:                ($font-size-base * .75) !default;\n$progress-bg:                       $gray-200 !default;\n$progress-border-radius:            $border-radius !default;\n$progress-box-shadow:               inset 0 .1rem .1rem rgba($black, .1) !default;\n$progress-bar-color:                $white !default;\n$progress-bar-bg:                   theme-color(\"primary\") !default;\n$progress-bar-animation-timing:     1s linear infinite !default;\n$progress-bar-transition:           width .6s ease !default;\n\n// List group\n\n$list-group-bg:                     $white !default;\n$list-group-border-color:           rgba($black, .125) !default;\n$list-group-border-width:           $border-width !default;\n$list-group-border-radius:          $border-radius !default;\n\n$list-group-item-padding-y:         .75rem !default;\n$list-group-item-padding-x:         1.25rem !default;\n\n$list-group-hover-bg:               $gray-100 !default;\n$list-group-active-color:           $component-active-color !default;\n$list-group-active-bg:              $component-active-bg !default;\n$list-group-active-border-color:    $list-group-active-bg !default;\n\n$list-group-disabled-color:         $gray-600 !default;\n$list-group-disabled-bg:            $list-group-bg !default;\n\n$list-group-action-color:           $gray-700 !default;\n$list-group-action-hover-color:     $list-group-action-color !default;\n\n$list-group-action-active-color:    $body-color !default;\n$list-group-action-active-bg:       $gray-200 !default;\n\n\n// Image thumbnails\n\n$thumbnail-padding:                 .25rem !default;\n$thumbnail-bg:                      $body-bg !default;\n$thumbnail-border-width:            $border-width !default;\n$thumbnail-border-color:            $gray-300 !default;\n$thumbnail-border-radius:           $border-radius !default;\n$thumbnail-box-shadow:              0 1px 2px rgba($black, .075) !default;\n\n\n// Figures\n\n$figure-caption-font-size:          90% !default;\n$figure-caption-color:              $gray-600 !default;\n\n\n// Breadcrumbs\n\n$breadcrumb-padding-y:              .75rem !default;\n$breadcrumb-padding-x:              1rem !default;\n$breadcrumb-item-padding:           .5rem !default;\n\n$breadcrumb-margin-bottom:          1rem !default;\n\n$breadcrumb-bg:                     $gray-200 !default;\n$breadcrumb-divider-color:          $gray-600 !default;\n$breadcrumb-active-color:           $gray-600 !default;\n$breadcrumb-divider:                \"/\" !default;\n\n\n// Carousel\n\n$carousel-control-color:            $white !default;\n$carousel-control-width:            15% !default;\n$carousel-control-opacity:          .5 !default;\n\n$carousel-indicator-width:          30px !default;\n$carousel-indicator-height:         3px !default;\n$carousel-indicator-spacer:         3px !default;\n$carousel-indicator-active-bg:      $white !default;\n\n$carousel-caption-width:            70% !default;\n$carousel-caption-color:            $white !default;\n\n$carousel-control-icon-width:       20px !default;\n\n$carousel-control-prev-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n$carousel-control-next-icon-bg:     str-replace(url(\"data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='#{$carousel-control-color}' viewBox='0 0 8 8'%3E%3Cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3E%3C/svg%3E\"), \"#\", \"%23\") !default;\n\n$carousel-transition:               transform .6s ease !default;\n\n\n// Close\n\n$close-font-size:                   $font-size-base * 1.5 !default;\n$close-font-weight:                 $font-weight-bold !default;\n$close-color:                       $black !default;\n$close-text-shadow:                 0 1px 0 $white !default;\n\n// Code\n\n$code-font-size:                    87.5% !default;\n$code-color:                        $pink !default;\n\n$kbd-padding-y:                     .2rem !default;\n$kbd-padding-x:                     .4rem !default;\n$kbd-font-size:                     $code-font-size !default;\n$kbd-color:                         $white !default;\n$kbd-bg:                            $gray-900 !default;\n\n$pre-color:                         $gray-900 !default;\n$pre-scrollable-max-height:         340px !default;\n\n\n// Printing\n$print-page-size:                   a3 !default;\n$print-body-min-width:              map-get($grid-breakpoints, \"lg\") !default;\n", "// Hover mixin and `$enable-hover-media-query` are deprecated.\n//\n// Originally added during our alphas and maintained during betas, this mixin was\n// designed to prevent `:hover` stickiness on iOS-an issue where hover styles\n// would persist after initial touch.\n//\n// For backward compatibility, we've kept these mixins and updated them to\n// always return their regular pseudo-classes instead of a shimmed media query.\n//\n// Issue: https://github.com/twbs/bootstrap/issues/25195\n\n@mixin hover() {\n  &:hover { @content; }\n}\n\n@mixin hover-focus() {\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin plain-hover-focus() {\n  &,\n  &:hover,\n  &:focus {\n    @content;\n  }\n}\n\n@mixin hover-focus-active() {\n  &:hover,\n  &:focus,\n  &:active {\n    @content;\n  }\n}\n", "//\n// Mixins: Animation\n//\n\n\n@keyframes flipInX {\n  0% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);\n    transition-timing-function: ease-in;\n    opacity: 0;\n  }\n\n  40% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);\n    transition-timing-function: ease-in;\n  }\n\n  60% {\n    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);\n    opacity: 1;\n  }\n\n  80% {\n    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);\n  }\n\n  100% {\n    transform: perspective(400px);\n  }\n}\n\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n  }\n\n  to {\n    opacity: 0;\n  }\n}\n\n@keyframes shake {\n  0% {\n    transform: translate(2px, 1px) rotate(0deg);\n  }\n  10% {\n    transform: translate(-1px, -2px) rotate(-2deg);\n  }\n  20% {\n    transform: translate(-3px, 0) rotate(3deg);\n  }\n  30% {\n    transform: translate(0, 2px) rotate(0deg);\n  }\n  40% {\n    transform: translate(1px, -1px) rotate(1deg);\n  }\n  50% {\n    transform: translate(-1px, 2px) rotate(-1deg);\n  }\n  60% {\n    transform: translate(-3px, 1px) rotate(0deg);\n  }\n  70% {\n    transform: translate(2px, 1px) rotate(-2deg);\n  }\n  80% {\n    transform: translate(-1px, -1px) rotate(4deg);\n  }\n  90% {\n    transform: translate(2px, 2px) rotate(0deg);\n  }\n  100% {\n    transform: translate(1px, -2px) rotate(-1deg);\n  }\n}\n\n@keyframes wobble {\n  0% {\n    transform: none;\n  }\n\n  15% {\n    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);\n  }\n\n  30% {\n    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);\n  }\n\n  45% {\n    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);\n  }\n\n  60% {\n    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);\n  }\n\n  75% {\n    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);\n  }\n\n  100% {\n    transform: none;\n  }\n}\n\n//\n", "//\n// Pages: Mailbox\n//\n\n.mailbox-messages {\n  > .table {\n    margin: 0;\n  }\n}\n\n.mailbox-controls {\n  padding: 5px;\n\n  &.with-border {\n    border-bottom: 1px solid $card-border-color;\n  }\n}\n\n.mailbox-read-info {\n  border-bottom: 1px solid $card-border-color;\n  padding: 10px;\n\n  h3 {\n    font-size: 20px;\n    margin: 0;\n  }\n\n  h5 {\n    margin: 0;\n    padding: 5px 0 0;\n  }\n}\n\n.mailbox-read-time {\n  color: #999;\n  font-size: 13px;\n}\n\n.mailbox-read-message {\n  padding: 10px;\n}\n\n.mailbox-attachments {\n  @include list-unstyled ();\n  li {\n    border: 1px solid #eee;\n    float: left;\n    margin-bottom: 10px;\n    margin-right: 10px;\n    width: 200px;\n  }\n}\n\n.mailbox-attachment-name {\n  color: #666;\n  font-weight: 700;\n}\n\n.mailbox-attachment-icon,\n.mailbox-attachment-info,\n.mailbox-attachment-size {\n  display: block;\n}\n\n.mailbox-attachment-info {\n  background-color: $gray-100;\n  padding: 10px;\n}\n\n.mailbox-attachment-size {\n  color: #999;\n  font-size: 12px;\n\n  > span {\n    display: inline-block;\n    padding-top: .75rem;\n  }\n}\n\n.mailbox-attachment-icon {\n  color: #666;\n  font-size: 65px;\n  max-height: 132.5px;\n  padding: 20px 10px;\n  text-align: center;\n\n  &.has-img {\n    padding: 0;\n\n    > img {\n      height: auto;\n      max-width: 100%;\n    }\n  }\n}\n\n.mailbox-attachment-close {\n  @extend .close;\n}\n", "// Lists\n\n// Unstyled keeps list items block level, just removes default browser padding and list-style\n@mixin list-unstyled() {\n  padding-left: 0;\n  list-style: none;\n}\n", "//\n// Pages: Lock Screen\n//\n\n// ADD THIS CLASS TO THE <BODY> TAG\n.lockscreen {\n  background-color: $gray-200;\n\n  // User name [optional]\n  .lockscreen-name {\n    font-weight: 600;\n    text-align: center;\n  }\n}\n\n.lockscreen-logo {\n  font-size: 35px;\n  font-weight: 300;\n  margin-bottom: 25px;\n  text-align: center;\n\n  a {\n    color: $gray-700;\n  }\n}\n\n.lockscreen-wrapper {\n  margin: 0 auto;\n  margin-top: 10%;\n  max-width: 400px;\n}\n\n\n// Will contain the image and the sign in form\n.lockscreen-item {\n  @include border-radius(4px);\n  background-color: $white;\n  margin: 10px auto 30px;\n  padding: 0;\n  position: relative;\n  width: 290px;\n}\n\n// User image\n.lockscreen-image {\n  @include border-radius(50%);\n  background-color: $white;\n  left: -10px;\n  padding: 5px;\n  position: absolute;\n  top: -25px;\n  z-index: 10;\n\n  > img {\n    @include border-radius(50%);\n    height: 70px;\n    width: 70px;\n  }\n}\n\n// Contains the password input and the login button\n.lockscreen-credentials {\n  margin-left: 70px;\n\n  .form-control {\n    border: 0;\n  }\n\n  .btn {\n    background-color: $white;\n    border: 0;\n    padding: 0 10px;\n  }\n}\n\n.lockscreen-footer {\n  margin-top: 10px;\n}\n\n@include dark-mode () {\n  .lockscreen-item {\n    background-color: $dark;\n  }\n  .lockscreen-logo a {\n    color: $white;\n  }\n  .lockscreen-credentials .btn {\n    background-color: $dark;\n  }\n  .lockscreen-image {\n    background-color: $gray-600;\n  }\n}\n", "// stylelint-disable property-disallowed-list\n// Single side border-radius\n\n// Helper function to replace negative values with 0\n@function valid-radius($radius) {\n  $return: ();\n  @each $value in $radius {\n    @if type-of($value) == number {\n      $return: append($return, max($value, 0));\n    } @else {\n      $return: append($return, $value);\n    }\n  }\n  @return $return;\n}\n\n@mixin border-radius($radius: $border-radius, $fallback-border-radius: false) {\n  @if $enable-rounded {\n    border-radius: valid-radius($radius);\n  }\n  @else if $fallback-border-radius != false {\n    border-radius: $fallback-border-radius;\n  }\n}\n\n@mixin border-top-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-left-radius($radius) {\n  @if $enable-rounded {\n    border-top-left-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-top-right-radius($radius) {\n  @if $enable-rounded {\n    border-top-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-right-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-right-radius: valid-radius($radius);\n  }\n}\n\n@mixin border-bottom-left-radius($radius) {\n  @if $enable-rounded {\n    border-bottom-left-radius: valid-radius($radius);\n  }\n}\n", "//\n// Mixins: Dark Mode Controll\n//\n\n@mixin dark-mode {\n  @if $enable-dark-mode {\n    .dark-mode {\n      @content;\n    }\n  }\n}\n", "//\n// Pages: Login & Register\n//\n\n.login-logo,\n.register-logo {\n  font-size: 2.1rem;\n  font-weight: 300;\n  margin-bottom: .9rem;\n  text-align: center;\n\n  a {\n    color: $gray-700;\n  }\n}\n\n.login-page,\n.register-page {\n  align-items: center;\n  background-color: $gray-200;\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  justify-content: center;\n}\n\n.login-box,\n.register-box {\n  width: 360px;\n\n  @media (max-width: map-get($grid-breakpoints, sm)) {\n    margin-top: .5rem;\n    width: 90%;\n  }\n\n  .card {\n    margin-bottom: 0;\n  }\n}\n\n.login-card-body,\n.register-card-body {\n  background-color: $white;\n  border-top: 0;\n  color: #666;\n  padding: 20px;\n\n  .input-group {\n    .form-control {\n      border-right: 0;\n\n      &:focus {\n        box-shadow: none;\n\n        ~ .input-group-prepend .input-group-text,\n        ~ .input-group-append .input-group-text {\n          border-color: $input-focus-border-color;\n        }\n      }\n\n      &.is-valid {\n        &:focus {\n          box-shadow: none;\n        }\n\n        ~ .input-group-prepend .input-group-text,\n        ~ .input-group-append .input-group-text {\n          border-color: $success;\n        }\n      }\n\n      &.is-invalid {\n        &:focus {\n          box-shadow: none;\n        }\n\n        ~ .input-group-append .input-group-text {\n          border-color: $danger;\n        }\n      }\n    }\n\n    .input-group-text {\n      background-color: transparent;\n      border-bottom-right-radius: $border-radius;\n      border-left: 0;\n      border-top-right-radius: $border-radius;\n      color: #777;\n      transition: $input-transition;\n    }\n  }\n}\n\n.login-box-msg,\n.register-box-msg {\n  margin: 0;\n  padding: 0 20px 20px;\n  text-align: center;\n}\n\n.social-auth-links {\n  margin: 10px 0;\n}\n\n@include dark-mode () {\n  .login-card-body,\n  .register-card-body {\n    background-color: $dark;\n    border-color: $gray-600;\n    color: $white;\n  }\n  .login-logo,\n  .register-logo {\n    a {\n      color: $white;\n    }\n  }\n}\n", "//\n// Pages: 400 and 500 error pages\n//\n\n.error-page {\n  margin: 20px auto 0;\n  width: 600px;\n\n  @include media-breakpoint-down(sm) {\n    width: 100%;\n  }\n\n  //For the error number e.g: 404\n  > .headline {\n    float: left;\n    font-size: 100px;\n    font-weight: 300;\n\n    @include media-breakpoint-down(sm) {\n      float: none;\n      text-align: center;\n    }\n  }\n\n  //For the message\n  > .error-content {\n    display: block;\n    margin-left: 190px;\n\n    @include media-breakpoint-down(sm) {\n      margin-left: 0;\n    }\n\n    > h3 {\n      font-size: 25px;\n      font-weight: 300;\n\n      @include media-breakpoint-down(sm) {\n        text-align: center;\n      }\n    }\n  }\n}\n", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @return if($n != null and $n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width. Null for the largest (last) breakpoint.\n// The maximum value is calculated as the minimum of the next one less 0.02px\n// to work around the limitations of `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $next: breakpoint-next($name, $breakpoints);\n  @return if($next, breakpoint-min($next, $breakpoints) - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  $max: breakpoint-max($name, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($name, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\n// Pages: Invoice\n//\n\n.invoice {\n  background-color: $white;\n  border: 1px solid $card-border-color;\n  position: relative;\n}\n\n.invoice-title {\n  margin-top: 0;\n}\n\n@include dark-mode () {\n  .invoice {\n    background-color: $dark;\n  }\n}\n", "//\n// Pages: Profile\n//\n\n.profile-user-img {\n  border: 3px solid $gray-500;\n  margin: 0 auto;\n  padding: 3px;\n  width: 100px;\n}\n\n.profile-username {\n  font-size: 21px;\n  margin-top: 5px;\n}\n\n.post {\n  border-bottom: 1px solid $gray-500;\n  color: #666;\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n\n  &:last-of-type {\n    border-bottom: 0;\n    margin-bottom: 0;\n    padding-bottom: 0;\n  }\n\n  .user-block {\n    margin-bottom: 15px;\n    width: 100%;\n  }\n\n  .row {\n    width: 100%;\n  }\n}\n\n@include dark-mode () {\n  .post {\n    color: $white;\n    border-color: $gray-600;\n  }\n}\n", "//\n// Pages: E-commerce\n//\n\n// product image\n.product-image {\n  @include img-fluid ();\n  width: 100%;\n}\n\n// product image thumbnails list\n.product-image-thumbs {\n  align-items: stretch;\n  display: flex;\n  margin-top: 2rem;\n}\n\n// product image thumbnail\n.product-image-thumb {\n  @include box-shadow($thumbnail-box-shadow);\n  @include border-radius($thumbnail-border-radius);\n\n  background-color: $thumbnail-bg;\n  border: $thumbnail-border-width solid $thumbnail-border-color;\n  display: flex;\n  margin-right: 1rem;\n  max-width: 6.5rem + ($thumbnail-padding * 2);\n  padding: $thumbnail-padding * 2;\n\n  img {\n    @include img-fluid ();\n    align-self: center;\n  }\n\n  &:hover {\n    opacity: .5;\n  }\n}\n\n// product share\n.product-share {\n  a {\n    margin-right: .5rem;\n  }\n}\n", "// Image Mixins\n// - Responsive image\n// - Retina image\n\n\n// Responsive image\n//\n// Keep images from scaling beyond the width of their parents.\n\n@mixin img-fluid() {\n  // Part 1: Set a maximum relative to the parent\n  max-width: 100%;\n  // Part 2: Override the height to auto, otherwise images will be stretched\n  // when setting a width and height attribute on the img element.\n  height: auto;\n}\n\n\n// Retina image\n//\n// Short retina mixin for setting background-image and -size.\n\n@mixin img-retina($file-1x, $file-2x, $width-1x, $height-1x) {\n  background-image: url($file-1x);\n\n  // Autoprefixer takes care of adding -webkit-min-device-pixel-ratio and -o-min-device-pixel-ratio,\n  // but doesn't convert dppx=>dpi.\n  // There's no such thing as unprefixed min-device-pixel-ratio since it's nonstandard.\n  // Compatibility info: https://caniuse.com/css-media-resolution\n  @media only screen and (min-resolution: 192dpi), // IE9-11 don't support dppx\n    only screen and (min-resolution: 2dppx) { // Standardized\n    background-image: url($file-2x);\n    background-size: $width-1x $height-1x;\n  }\n  @include deprecate(\"`img-retina()`\", \"v4.3.0\", \"v5\");\n}\n", "@mixin box-shadow($shadow...) {\n  @if $enable-shadows {\n    $result: ();\n\n    @if (length($shadow) == 1) {\n      // We can pass `@include box-shadow(none);`\n      $result: $shadow;\n    } @else {\n      // Filter to avoid invalid properties for example `box-shadow: none, 1px 1px black;`\n      @for $i from 1 through length($shadow) {\n        @if nth($shadow, $i) != \"none\" {\n          $result: append($result, nth($shadow, $i), \"comma\");\n        }\n      }\n    }\n    @if (length($result) > 0) {\n      box-shadow: $result;\n    }\n  }\n}\n", "//\n// Pages: Projects\n//\n\n.projects {\n  td {\n    vertical-align: middle;\n  }\n\n  .list-inline {\n    margin-bottom: 0;\n  }\n\n  // table avatar\n  img.table-avatar,\n  .table-avatar img {\n    border-radius: 50%;\n    display: inline;\n    width: 2.5rem;\n  }\n\n  // project state\n  .project-state {\n    text-align: center;\n  }\n}\n", "body.iframe-mode {\n  .main-sidebar {\n    display: none;\n  }\n  .content-wrapper {\n    margin-left: 0 !important;\n    margin-top: 0 !important;\n    padding-bottom: 0 !important;\n  }\n  .main-header,\n  .main-footer {\n    display: none;\n  }\n}\n\nbody.iframe-mode-fullscreen {\n  overflow: hidden;\n\n  &.layout-navbar-fixed .wrapper .content-wrapper {\n    margin-top: 0 !important;\n  }\n}\n\n.content-wrapper {\n  height: 100%;\n\n  &.iframe-mode {\n    .btn-iframe-close {\n      color: $danger;\n      position: absolute;\n      line-height: 1;\n      right: .125rem;\n      top: .125rem;\n      z-index: 10;\n      visibility: hidden;\n\n      &:hover,\n      &:focus {\n        animation-name: fadeIn;\n        animation-duration: $transition-speed;\n        animation-fill-mode: both;\n        visibility: visible;\n      }\n\n      @include on-touch-device() {\n        visibility: visible;\n      }\n    }\n    .navbar-nav {\n      overflow-y: auto;\n      width: 100%;\n\n      .nav-link {\n        white-space: nowrap;\n      }\n      .nav-item {\n        position: relative;\n\n        &:hover,\n        &:focus {\n          .btn-iframe-close {\n            animation-name: fadeIn;\n            animation-duration: $transition-speed;\n            animation-fill-mode: both;\n            visibility: visible;\n\n            @include on-touch-device() {\n              visibility: visible;\n            }\n          }\n        }\n      }\n    }\n    .tab-content {\n      position: relative;\n    }\n    .tab-pane + .tab-empty {\n      display: none;\n    }\n    .tab-empty {\n      width: 100%;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n    }\n    .tab-loading {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      display: none;\n      background-color: $main-bg;\n\n      > div {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 100%;\n        height: 100%;\n      }\n    }\n\n    iframe {\n      border: 0;\n      width: 100%;\n      height: 100%;\n      margin-bottom: -8px;\n\n      .content-wrapper {\n        padding-bottom: 0 !important;\n      }\n    }\n\n    body.iframe-mode-fullscreen & {\n      position: absolute;\n      left: 0;\n      top: 0;\n      right: 0;\n      bottom: 0;\n      margin-left: 0 !important;\n      height: 100%;\n      min-height: 100%;\n      z-index: $zindex-main-sidebar + 10;\n    }\n  }\n}\n\n.permanent-btn-iframe-close {\n  .btn-iframe-close {\n    animation: none !important;\n    visibility: visible !important;\n    opacity: 1;\n  }\n}\n\n@include dark-mode () {\n  .content-wrapper.iframe-mode {\n    .tab-loading {\n      background-color: $dark;\n    }\n  }\n}\n", "//\n// Core: Variables\n//\n\n// COLORS\n// --------------------------------------------------------\n$blue: #0073b7 !default;\n$lightblue: #3c8dbc !default;\n$navy: #001f3f !default;\n$teal: #39cccc !default;\n$olive: #3d9970 !default;\n$lime: #01ff70 !default;\n$orange: #ff851b !default;\n$fuchsia: #f012be !default;\n$purple: #605ca8 !default;\n$maroon: #d81b60 !default;\n$black: #111 !default;\n$gray-x-light: #d2d6de !default;\n\n$colors: map-merge(\n  (\n    \"lightblue\": $lightblue,\n    \"navy\": $navy,\n    \"olive\": $olive,\n    \"lime\": $lime,\n    \"fuchsia\": $fuchsia,\n    \"maroon\": $maroon,\n  ),\n  $colors\n);\n\n// LAYOUT\n// --------------------------------------------------------\n\n$font-size-root: 1rem !default;\n\n// Sidebar\n$sidebar-width: 250px !default;\n$sidebar-padding-x: .5rem !default;\n$sidebar-padding-y: 0 !default;\n$sidebar-custom-height: 4rem !default;\n$sidebar-custom-height-lg: 6rem !default;\n$sidebar-custom-height-xl: 8rem !default;\n$sidebar-custom-padding-x: .85rem !default;\n$sidebar-custom-padding-y: .5rem !default;\n\n// Boxed layout maximum width\n$boxed-layout-max-width: 1250px !default;\n\n// Body background (Affects main content background only)\n$main-bg: #f4f6f9 !default;\n\n$dark-main-bg: lighten($dark, 7.5%) !important;\n\n// Content padding\n$content-padding-y: 0 !default;\n$content-padding-x: $navbar-padding-x !default;\n\n// IMAGE SIZES\n// --------------------------------------------------------\n$img-size-sm: 1.875rem !default;\n$img-size-md: 3.75rem !default;\n$img-size-lg: 6.25rem !default;\n$img-size-push: .625rem !default;\n\n// MAIN HEADER\n// --------------------------------------------------------\n$main-header-bottom-border-width: $border-width !default;\n$main-header-bottom-border-color: $gray-300 !default;\n$main-header-bottom-border: $main-header-bottom-border-width solid $main-header-bottom-border-color !default;\n$main-header-link-padding-y: $navbar-padding-y !default;\n$main-header-height-inner: ($nav-link-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height: calc(#{$main-header-height-inner} + #{$main-header-bottom-border-width}) !default;\n$nav-link-sm-padding-y: .35rem !default;\n$nav-link-sm-height: ($font-size-sm * $line-height-sm + $nav-link-sm-padding-y * 1.785) !default;\n$main-header-height-sm-inner: ($nav-link-sm-height + ($main-header-link-padding-y * 2)) !default;\n$main-header-height-sm: calc(#{$main-header-height-sm-inner} + #{$main-header-bottom-border-width}) !default;\n\n\n// Main header skins\n$main-header-dark-form-control-bg: $gray-800 !default;\n$main-header-dark-form-control-focused-bg: $gray-700 !default;\n$main-header-dark-form-control-focused-color: $gray-400 !default;\n$main-header-dark-form-control-border-color: $gray-600 !default;\n$main-header-dark-form-control-focused-border-color: $gray-600 !default;\n$main-header-dark-placeholder-color: rgba($white, .6) !default;\n\n$main-header-light-form-control-bg: darken($gray-200, 5%) !default;\n$main-header-light-form-control-focused-bg: darken($gray-200, 7.5%) !default;\n$main-header-light-form-control-focused-color: $gray-400 !default;\n$main-header-light-form-control-border-color: $gray-400 !default;\n$main-header-light-form-control-focused-border-color: darken($gray-400, 2.5%) !default;\n$main-header-light-placeholder-color: rgba(0, 0, 0, .6) !default;\n\n// MAIN FOOTER\n// --------------------------------------------------------\n$main-footer-padding: 1rem !default;\n$main-footer-padding-sm: $main-footer-padding * .812 !default;\n$main-footer-border-top-width: 1px !default;\n$main-footer-border-top-color: $gray-300 !default;\n$main-footer-border-top: $main-footer-border-top-width solid $main-footer-border-top-color !default;\n$main-footer-height-inner: (($font-size-root * $line-height-base) + ($main-footer-padding * 2)) !default;\n$main-footer-height: calc(#{$main-footer-height-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-height-sm-inner: (($font-size-sm * $line-height-base) + ($main-footer-padding-sm * 2)) !default;\n$main-footer-height-sm: calc(#{$main-footer-height-sm-inner} + #{$main-footer-border-top-width}) !default;\n$main-footer-bg: $white !default;\n\n// SIDEBAR SKINS\n// --------------------------------------------------------\n\n// Dark sidebar\n$sidebar-dark-bg: $dark !default;\n$sidebar-dark-hover-bg: rgba(255, 255, 255, .1) !default;\n$sidebar-dark-color: #c2c7d0 !default;\n$sidebar-dark-hover-color: $white !default;\n$sidebar-dark-active-color: $white !default;\n$sidebar-dark-submenu-bg: transparent !default;\n$sidebar-dark-submenu-color: #c2c7d0 !default;\n$sidebar-dark-submenu-hover-color: $white !default;\n$sidebar-dark-submenu-hover-bg: $sidebar-dark-hover-bg !default;\n$sidebar-dark-submenu-active-color: $sidebar-dark-bg !default;\n$sidebar-dark-submenu-active-bg: rgba(255, 255, 255, .9) !default;\n\n// Light sidebar\n$sidebar-light-bg: $white !default;\n$sidebar-light-hover-bg: rgba($black, .1) !default;\n$sidebar-light-color: $gray-800 !default;\n$sidebar-light-hover-color: $gray-900 !default;\n$sidebar-light-active-color: $black !default;\n$sidebar-light-submenu-bg: transparent !default;\n$sidebar-light-submenu-color: #777 !default;\n$sidebar-light-submenu-hover-color: $black !default;\n$sidebar-light-submenu-hover-bg: $sidebar-light-hover-bg !default;\n$sidebar-light-submenu-active-color: $sidebar-light-hover-color !default;\n$sidebar-light-submenu-active-bg: $sidebar-light-submenu-hover-bg !default;\n\n// SIDEBAR MINI\n// --------------------------------------------------------\n$sidebar-mini-width: ($nav-link-padding-x + $sidebar-padding-x + .8rem) * 2 !default;\n$sidebar-nav-icon-width: $sidebar-mini-width - (($sidebar-padding-x + $nav-link-padding-x) * 2) !default;\n$sidebar-user-image-width: $sidebar-nav-icon-width + ($nav-link-padding-x * .5) !default;\n\n// CONTROL SIDEBAR\n// --------------------------------------------------------\n$control-sidebar-width: $sidebar-width !default;\n\n// Cards\n// --------------------------------------------------------\n$card-border-color: $gray-100 !default;\n$card-dark-border-color: lighten($gray-900, 10%) !default;\n$card-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2) !default;\n$card-title-font-size: 1.1rem !default;\n$card-title-font-size-sm: 1rem !default;\n$card-title-font-weight: $font-weight-normal !default;\n$card-nav-link-padding-sm-y: .4rem !default;\n$card-nav-link-padding-sm-x: .8rem !default;\n$card-img-size: $img-size-sm !default;\n\n// PROGRESS BARS\n// --------------------------------------------------------\n$progress-bar-border-radius: 1px !default;\n\n// DIRECT CHAT\n// --------------------------------------------------------\n$direct-chat-default-msg-bg: $gray-x-light !default;\n$direct-chat-default-font-color: #444 !default;\n$direct-chat-default-msg-border-color: $gray-x-light !default;\n\n// Z-INDEX\n// --------------------------------------------------------\n$zindex-main-header: $zindex-fixed + 4 !default;\n$zindex-main-sidebar: $zindex-fixed + 8 !default;\n$zindex-main-footer: $zindex-fixed + 2 !default;\n$zindex-control-sidebar: $zindex-fixed + 1 !default;\n$zindex-toasts: $zindex-main-sidebar + 2 !default;\n$zindex-preloader: 9999 !default;\n\n// TRANSITIONS SETTINGS\n// --------------------------------------------------------\n\n// Transition global options\n$transition-speed: .3s !default;\n$transition-fn: ease-in-out !default;\n\n// TEXT\n// --------------------------------------------------------\n$font-size-xs: ($font-size-base * .75) !default;\n$font-size-xl: ($font-size-base * 2) !default;\n\n\n// BUTTON\n// --------------------------------------------------------\n$button-default-background-color: $gray-100 !default;\n$button-default-color: #444 !default;\n$button-default-border-color: #ddd !default;\n\n$button-padding-y-xs: .125rem !default;\n$button-padding-x-xs: .25rem !default;\n$button-line-height-xs: $line-height-sm !default;\n$button-font-size-xs: ($font-size-base * .75) !default;\n$button-border-radius-xs: .15rem !default;\n\n\n// ELEVATION\n// --------------------------------------------------------\n$elevations: ();\n$elevations: map-merge(\n  (\n    1: unquote(\"0 1px 3px \" + rgba($black, .12) + \", 0 1px 2px \" + rgba($black, .24)),\n    2: unquote(\"0 3px 6px \" + rgba($black, .16) + \", 0 3px 6px \" + rgba($black, .23)),\n    3: unquote(\"0 10px 20px \" + rgba($black, .19) + \", 0 6px 6px \" + rgba($black, .23)),\n    4: unquote(\"0 14px 28px \" + rgba($black, .25) + \", 0 10px 10px \" + rgba($black, .22)),\n    5: unquote(\"0 19px 38px \" + rgba($black, .3) + \", 0 15px 12px \" + rgba($black, .22)),\n  ),\n  $elevations\n);\n\n// RIBBON\n// --------------------------------------------------------\n$ribbon-border-size: 3px !default;\n$ribbon-line-height: 100% !default;\n$ribbon-padding: .375rem 0 !default;\n$ribbon-font-size: .8rem !default;\n$ribbon-width: 90px !default;\n$ribbon-wrapper-size: 70px !default;\n$ribbon-top: 10px !default;\n$ribbon-right: -2px !default;\n$ribbon-lg-wrapper-size: 120px !default;\n$ribbon-lg-width: 160px !default;\n$ribbon-lg-top: 26px !default;\n$ribbon-lg-right: 0 !default;\n$ribbon-xl-wrapper-size: 180px !default;\n$ribbon-xl-width: 240px !default;\n$ribbon-xl-top: 47px !default;\n$ribbon-xl-right: 4px !default;\n\n// CUSTOM FORM SELECT\n// --------------------------------------------------------\n\n$custom-select-dark-indicator-color:     $white !default;\n$custom-select-dark-indicator:           url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'><path fill='#{$custom-select-dark-indicator-color}' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>\") !default;\n$custom-select-dark-background:          escape-svg($custom-select-dark-indicator) right $custom-select-padding-x center / $custom-select-bg-size no-repeat !default; // Used so we can have multiple background elements (e.g., arrow and feedback icon)\n\n// ENABLE DARK MODE\n// --------------------------------------------------------\n$enable-dark-mode: true !default;  // requires `@import \"variables-alt\";`\n\n//\n", "//\n// Mixins: Touch Support\n//\n\n@mixin on-touch-device {\n  @media (hover: none) and (pointer: coarse) {\n    @content;\n  }\n}\n\n//\n", ".content-wrapper.kanban {\n  height: 1px;\n\n  .content {\n    height: 100%;\n    overflow-x: auto;\n    overflow-y: hidden;\n\n    .container,\n    .container-fluid {\n      width: max-content;\n      display: flex;\n      align-items: stretch;\n    }\n  }\n  .content-header + .content {\n    height: calc(100% - ((2 * 15px) + (1.8rem * #{$headings-line-height})));\n  }\n\n  .card {\n    .card-body {\n      padding: .5rem;\n    }\n\n    &.card-row {\n      width: 340px;\n      display: inline-block;\n      margin: 0 .5rem;\n\n      &:first-child {\n        margin-left: 0;\n      }\n\n      .card-body {\n        height: calc(100% - (12px + (1.8rem * #{$headings-line-height}) + .5rem));\n        overflow-y: auto;\n      }\n\n      .card {\n        &:last-child {\n          margin-bottom: 0;\n          border-bottom-width: 1px;\n        }\n        .card-header {\n          padding: .5rem .75rem;\n        }\n        .card-body {\n          padding: .75rem;\n        }\n      }\n    }\n  }\n  .btn-tool {\n    &.btn-link {\n      text-decoration: underline;\n      padding-left: 0;\n      padding-right: 0;\n    }\n  }\n}\n"]}
<template>
    <div
      class="modal fade"
      id="joiningPaymentModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="joiningPaymentModalTitle"
      aria-hidden="true"
    >
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 v-if="pollUrl == null" class="modal-title" id="joiningPaymentModalTitle">New Member Subscription Payment</h5>
            <h5 v-if="pollUrl != null" class="modal-title" id="joiningPaymentModalTitle">Checking Payment In  {{ timerCount }}s </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>

          <div v-if="pollUrl == null" class="modal-body">
            <div class="col-12 mb-2">
              <label for="_amount">Deposit Amount</label>
               <p>Please note payment will include additional transaction charges that are not part of the deposit.</p>
            </div>

            <h5 class="h6">Payment method:</h5>

            <div v-if="paymentMethod=='clicknpay'" class="col-12 mb-2">
              <label for="_phone">Enter ecocash number</label>
              <div class="input-group mb-3">
                <input
                  id="_phone"
                  value="3"
                  v-model.number="phone"
                  type="phone"
                  class="form-control"
                  placeholder="Phone Number"
                  aria-label="Phone Number"
                  aria-describedby="Phone Number"
                />
              </div>
            </div>


            <button v-if="paymentMethod=='clicknpay'" class="btn btn-success w-100" @click="pay()" >
              Make Payment
            </button>
            <!-- <button v-if="paymentMethod!='clicknpay'" class="btn btn-success w-100" @click="paymentMethod='clicknpay' " >
              Ecocash (ClicknPay)
            </button> -->
            <br><br><a v-if="paymentMethod!='clicknpay'" class="btn btn-success w-100" :href="paynowUrl">
              Paynow
            </a>


          </div>
          <div v-if="pollUrl != null" class="modal-body">
            <div class="col-12 mb-2">
              <label for="_amount">Waiting for Payment</label>
            </div>

            <h5 v-if="open" class="h6">Checking in: {{timerCount}}</h5>
            <button class="btn btn-success w-100" @click="checkPaymment()">
              Check Now
            </button>
          </div>

        </div>
      </div>
    </div>
  </template>

  <script>
  import axios from 'axios';
  export default {
    props: ['route', 'user', 'client_id'],
    mounted() {
      console.log(this.user, this.client_id);
      this.paynowUrl = "/joining-payment/submit/paynow/"+this.user.id;
      this.clicknpayUrl = "/api/clicknpay/deposit/"+this.user.id;
      console.log(this.paynowUrl, this.user.id);

      $("#joiningPaymentModal").on("show.bs.modal", e => {
        if(!this.open){
          const scr = document.createElement("script");
          scr.src =
            `https://www.paypal.com/sdk/js?client-id=${this.client_id}&currency=GBP`;
          scr.addEventListener("load", this.loadPayPal);
          scr.addEventListener("error", this.loadError);
          document.body.append(scr);
          this.script = scr;
        }
      });
      $('#joiningPaymentModal').on('hidden.bs.modal', (e) => {
          if(this.error){
            this.error = false;
            $(this.script).remove();
            this.load = false;
          }
      });
    },
    data() {
      return {
        open: false,
        error: false,
        pollUrl: null,
        paymentMethod: null,
        amount: 100,
        phone: null,
        phoneClone : null,
        timerCount: 60,
        timerEnabled: false,
        payment_ref: null,
        paynowUrl: null,
        clicknpayUrl: null,
        script: null
      };
    },

    methods: {
      finalise(payment_ref){
        console.log(payment_ref);
        $.notify(
          {
            icon: "nc-icon nc-check-2",
            message: "Paid Joining Fee Successfully. Please wait to be redirected to reset password."
          },
          {
            type: "primary",
            timer: 5000,
            placement: {
              from: "top",
              align: "right"
            }
          }
        );
        this.payment_ref = payment_ref;
        setTimeout(() => this.$refs.form.submit(), 80);
      },
      loadError(){
        this.error = true;
      },
      loadPayPal(e){
        this.open = true;
        console.log(e, "Loaded Successfully");
        if (typeof paypal === "undefined") {
          this.error = true;
          return;
        }
        paypal
          .Buttons({
            createOrder: (data, action) => {

              this.amount = parseFloat(100);
              this.amount = (this.amount);
              this.amount = this.amount.toFixed(2);

              this.amount1 = (this.amount/0.971)+0.31;
              this.amount1 = this.amount1.toFixed(2);

              console.log("Paypal payment of: ", this.amount);
              return action.order.create({
                application_context: {
                  brand_name: "Funeral Cover Association",
                  user_action: "PAY_NOW",
                  shipping_preference: "NO_SHIPPING"
                },
                purchase_units: [
                  {
                      description: `Joining fee for applicant: ${this.user.id} (${this.user.first_name})`,
                      amount: {
                          currency_code: "GBP",
                          value: this.amount1
                    }
                  }
                ]
              });
            },
            onApprove: (data, actions) => {
              return actions.order.capture().then(details => {
                if (details.status == "COMPLETED") {
                  this.orderID = data.orderID;
                  this.finalise(data.orderID);
                } else {
                  $.notify({
                      icon: "nc-icon nc-simple-remove",
                      message: `Error saving your payment, contact support with ID: ${data.orderID}`,
                    },
                    {
                      type: "danger",
                      timer: 8000,
                      placement: {
                        from: "top",
                        align: "right"
                      }
                    }
                  );
                }
              });
            }
          })
          .render(this.$refs.paypal);
      },
      pay() {
        let url = '/api/clicknpay/pay-joining/'+this.user.id+'/'+this.phone;
          if(this.phone==null || this.phone?.toString().length<8){
              return $.notify(
                  {
                  icon: "nc-icon nc-check-2",
                  message: "Enter Valid Ecocash Number."
                  },
                  {
                  type: "warning",
                  timer: 5000,
                  placement: {
                      from: "top",
                      align: "right"
                  }
                  }
              );
          }else{
              let loader = this.$loading.show({
                  backgroundColor: "#000",
                  color: "#ae2227",
                  canCancel: false
              });

              this.phoneClone = this.phone;
              axios.get(url).then(response => {
                  console.log(response.data);
                      loader.hide();
                      this.pollUrl =  response.data.pollUrl;
                      this.timerEnabled = true;
                  })
                  .catch(error => {
                      console.error('Error fetching data:', error);
                      loader.hide();
                  }
              );
          }
      },
      sleep(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
      },

      checkPaymment() {
          this.timerCount = 60;
          this.timerEnabled = false

          let loader = this.$loading.show({
              backgroundColor: "#000",
              color: "#ae2227",
              canCancel: false
          });



          let url = '/api/clicknpay/pay-joining/check/'+this.phoneClone +'/'+this.pollUrl;

          axios.get(url)
              .then(response => {
                  console.log(response.data);
                  loader.hide();
                  this.timerCount = 60;

                  if(response.data.status=="COMPLETED"){
                      $.notify(
                          {
                          icon: "nc-icon nc-check-2",
                          message: response.data.message
                          },
                          {
                          type: "primary",
                          timer: 5000,
                          placement: {
                              from: "top",
                              align: "right"
                          }
                          }
                      );

                      this.sleep(4000).then(() => {
                          window.location.href = "/login";
                      });


                  }else{


                      $.notify(
                          {
                          icon: "nc-icon nc-check-2",
                          message: "Payment pending."
                          },
                          {
                          type: "warning",
                          timer: 5000,
                          placement: {
                              from: "top",
                              align: "right"
                          }
                          }
                      );



                      this.timerEnabled = true;
                  }

              })
              .catch(error => {
                  console.error('Error fetching data:', error);
                  loader.hide();
                  this.timerEnabled = true;
              });
      },
    },
    watch: {
      timerEnabled(value) {
          if (value) {
              setTimeout(() => {
                  this.timerCount--;
              }, 1000);
          }
      },

      timerCount: {
          handler(value) {

              if (value > 0 && this.timerEnabled) {
                  setTimeout(() => {
                      this.timerCount--;
                  }, 1000);
              }else if(this.timerEnabled){
                  this.timerCount = 60;
                  this.timerEnabled = false;
                  this.checkPaymment();
              }
          },
          immediate: true // This ensures the watcher is triggered upon creation
      }

  }
  };
  </script>

  <style>
  </style>

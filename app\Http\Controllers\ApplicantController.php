<?php

namespace App\Http\Controllers;

use App\Events\ApplicantRejected;
// use App\Events\NewApplicant;
use App\Http\Requests\StoreApplicant;
use App\Http\Requests\StoreCompleteRegistration;
use App\Models\Ambassador;
use App\Models\Applicant;
use App\Models\Branch;
use App\Models\Category;
use App\Models\Member;
use App\Models\Nominee;
use App\Notifications\NewApplicant;
use App\Notifications\PaymentCompleted;
use App\Notifications\PaymentRequest;
use App\Service;
use App\User;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Str;

class ApplicantController extends Controller
{
    public function form()
    {
        $categories = Category::all();
        $branches = Branch::all();
        $ambassadors = Ambassador::all();
        foreach ($categories as $c) {
            $c->services;
        }

        return view('auth.apply', ["categories" => $categories, "branches"=>$branches,"ambassadors"=>$ambassadors ]);
    }
    public function checkEmail(Request $request)
    {
        $user = User::whereEmail($request->email)->first();
        return ["unique" => !isset($user)];
    }
    public function store(StoreApplicant $request)
    {
        $validated = $request->validated();
        $applicant = Applicant::create($validated);
        $redirect_code = encrypt("{$applicant->id}:{$applicant->email}");

        try{
            $applicant->notify(new PaymentRequest(""));
        }catch(Exception $ex){
            logger($ex->getMessage());
        }

        $route = route("submitted", $redirect_code);
        $route = route('joinpay', $applicant->id);

        if($request->json==true) return response()->json([
            'message' => $applicant
        ]);

        return compact('route');
    }

    public function submitted($code)
    {
        $applicant_id = null;

        try {
            $identy = decrypt($code);
            $applicant_id = substr($identy, 0, strpos($identy, ":"));
        } catch (\Throwable $th) {
            logger("Someone tried to access success page", [$th->getMessage()]);
            return abort(404);
        }

        $applicant = Applicant::findOrFail($applicant_id);

        logger("Applicant completed registration");

        return view("auth.submitted", compact('applicant'));
    }


    public function joiningForm(Applicant $applicant){
        $member = Member::whereEmail($applicant->email)->first();
        if($member != null && $applicant->email!=null){
            return view('pages.joinpaymentexists', ["applicant" => $applicant]);
        }

        $services = [];
        foreach($applicant->services as $s){
           $service =  Service::find($s);
           array_push($services, $service);
        }

        return view('pages.joinpayment', ["applicant" => $applicant, "services"=>$services, "total"=>$applicant->getServiceTotal()]);
    }

    public function subscriptionpay($member){
        $memberF = Member::find($member);
        if($memberF != null){
            $memberF->services();
            return view('pages.subscriptionpaymentexists', ["applicant"=>$memberF]);
        }
        return back()->with([
            'message'    => "Member not found",
            'alert-type' => 'error',
        ]);
    }


    public function recharge(){
        return view('pages.subscriptionpayment');
    }

    public function accept(Request $request)
    {
        if (!isset($request->applicant_id)) return abort(404);
        $applicant = Applicant::findOrFail($request->applicant_id);
        $request = requestPayment($applicant);

        if($request->statusCode !== 201){
            return back()->with([
                'message'    => "Failed to request payment. Please try again",
                'alert-type' => 'error',
            ]);
        }
        $res = (object) $request->result;
        $approve = null;
        foreach ($res->links as $link) {
            if($link->rel === "approve"){
                $approve = $link->href;
            }
        }

        logger("New payment request: ID >> {$res->id} Created @ {$res->create_time}");
        $applicant->notify(new PaymentRequest($approve));
        $applicant->status = "accepted";
        $applicant->token = $res->id;
        $applicant->save();
        return back()->with([
            'message'    => "{$applicant->full_name} was accepted",
            'alert-type' => 'success',
        ]);
    }


    // Cash payment
    public function acceptCashPaid(Request $request){
        $applicant =  Applicant::find($request->applicant_id);
        if(!isset($applicant))  return abort(404);
        $member = Member::whereEmail($applicant->email)->first();
        if($member != null && $applicant->email!=null) return back()->withErrors(["message" => "A member with that email already exists"]);

        $invoice = $applicant->invoiceJoining("Cash joining fee approved by ".Auth::user()->email);
        $memCon = new MemberController();
        $memCon->createPaidMember($invoice,$applicant->getServiceTotal(),$invoice->poll_url, 'Cash');
        return redirect(route('members-area.home'));
    }

    public function deleteusers(){
            Member::whereNotNull('id')->delete();
    }


    public function completeRegForm(Applicant $applicant, Request $request)
    {
        $token = $request->token;
        if(!isset($token)) return abort(404);
        $capture = captureOrder($applicant->token);
        abort_if($capture->statusCode !== 201 && $capture->statusCode !== 422, 500);
        try {
            $res = (object) $capture->result;
            if (!$applicant->welcome_send) {
                $url = route('forgot.password');
                $applicant->notify(new PaymentCompleted($url, $res->id ?? "0000"));
                $applicant->update(["welcome_send" => true]);
            }
        } catch (\Throwable $th) {}

        return view("auth.complete", ["token" => $applicant->token, "email" => $applicant->email]);
    }






    public function reject(Request $request)
    {
        $applicant = Applicant::find($request->applicant_id);
        if (!isset($applicant)) {
            return ["error" => true, "message" => "Oops something went wrong. Refresh your browser and try again"];
        }

        event(new ApplicantRejected($applicant, $request->rejection_reason));
        $applicant->status = "rejected";
        $applicant->save();
        return [
            "success" => true,
            "message" => "User was rejected successfully",
            "route" => route("voyager.applicants.index")
        ];
    }
}

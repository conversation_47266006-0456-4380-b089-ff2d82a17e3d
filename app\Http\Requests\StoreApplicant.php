<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreApplicant extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //Member details
            'email' => 'max:255',
            'first_name' => 'sometimes|max:255',
            'middle_names' => 'sometimes|max:255',
            'last_name' => 'sometimes|max:255',
            'dob'  =>  'sometimes',
            'city' => 'sometimes',
            'source' => 'sometimes',
            'country' => 'sometimes',
            'gov_id' => 'sometimes',
            'branch' => 'sometimes',
            'apartment' => 'sometimes|max:255',
            'street' => 'sometimes|max:255',
            'zip' => 'sometimes',
            'json' => 'sometimes',
            "gender" => "sometimes|in:f,m",
            "phone" => 'sometimes',
            //Next of kin details
            "nok_city" => 'sometimes',
            "nok_country" => 'sometimes',
            "nok_dob" => 'sometimes',
            "nok_email" => 'sometimes',
            "nok_full_name" => 'sometimes',
            "nok_phone" => 'sometimes',
            "nok_street" => 'sometimes',
            "nok_apartment" => 'sometimes',
            "nok_zip" => 'sometimes',
            //nominees
            "nominees" => 'sometimes',
            //Terms
            "services" => 'sometimes',
            // "certify_details" => 'sometimes',
            // "uk_resident" => 'sometimes',
            //payment
            // "orderID" => 'sometimes'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'form_name.unique' => 'The template name should be unique',
            "nok_city.sometimes" => 'Nex of Kin\'s City is sometimes',
            "nok_country.sometimes" => 'Nex of Kin\'s country is sometimes',
            "nok_dob.sometimes" => 'Nex of Kin\'s Date of Birth is sometimes',
            "nok_email.sometimes" => 'Nex of Kin\'s Email is sometimes',
            "nok_full_name.sometimes" => 'Nex of Kin\'s Full Name is sometimes',
            "nok_phone.sometimes" => 'Nex of Kin\'s Phone is sometimes',
            "nok_street.sometimes" => 'Nex of Kin\'s Street is sometimes',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    // protected function prepareForValidation()
    // {
    //     $this->merge([
    //         'dob' => strtotime($this->dob)
    //     ]);
    // }
}

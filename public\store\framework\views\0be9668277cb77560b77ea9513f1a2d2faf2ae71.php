<?php $__env->startSection('page'); ?>
Dependants
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="col-12">
    <?php if($errors->any()): ?>
    <div class="alert alert-danger" role="alert">
        <ul>
            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li><?php echo e($error); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
    <?php endif; ?>
    <nominees-form :maximum_nominees="<?php echo e(setting('member.max_nominees', 4)); ?>" :can_update="'<?php echo e($can_update); ?>'" :route="'<?php echo e(route('members-area.nominees')); ?>'" :nominees="<?php echo e($nominees); ?>">
        <?php echo csrf_field(); ?>
    </nominees-form>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('member.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/nominees.blade.php ENDPATH**/ ?>
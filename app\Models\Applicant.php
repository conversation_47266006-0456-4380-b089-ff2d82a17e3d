<?php

namespace App\Models;
use App\Service;
use DateTime;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Applicant extends Model
{
    use Notifiable;

    protected $fillable = [
        "email",
        "first_name",
        "middle_names",
        "last_name",
        "dob",
        "gov_id",
        "city",
        "branch",
        "country",
        "street",
        "apartment",
        "zip",
        "gender",
        "phone",
        "status",
        "token",
        "nok_city",
        "nok_country",
        "nok_dob",
        "nok_email",
        "nok_full_name",
        "nok_phone",
        "nok_street",
        "nok_zip",
        "ambassador_id",
        "nok_apartment",
        "nominees",
        "services",
        "read_constitution",
        "certify_details",
        "uk_resident",
        "welcome_send",
    ];

    protected $hidden = [
        'created_at', 'updated_at', 'deleted_at'
    ];

    protected $casts = [
        'nominees' => 'array',
        'services' => 'array',
        'dob' => 'date',
        'nok_dob' => 'date',
    ];


    public function ambassador(){
        return $this->belongsTo(related: Ambassador::class);
    }


    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->middle_names} {$this->last_name}";
    }

    public function getReadConstitutionReadAttribute()
    {
        return $this->read_constitution ? "Yes": "No";
    }

    public function getCertifyDetailsReadAttribute()
    {
        return $this->certify_details ? "Yes" : "No";
    }

    public function getUkResidentReadAttribute()
    {
        return $this->uk_resident ? "Yes" : "No";
    }


    public function getNomineesBrowseAttribute(){
        return json_encode($this->nominees);
    }

    public function getServiceNames(){
        $services = Service::findMany($this->services);
        $serviceNames = " ";
        for($i=0; $i<count($services); $i++){
            $serviceNames =$serviceNames.($services[$i]->title." ,");
        }

        return $serviceNames;
    }


    public function getServiceTotal(){
        $services = [];
        $total = 0;
        foreach($this->services as $s){
           $service =  Service::find($s);
           array_push($services, $service);
           $total+=$service->price;
        }

        if(setting('site.customise_nominee_package')) foreach($this->nominees??[] as $n){
            // $total*=(count($this->nominees)+1);
            $servicen =  Service::find($n["service_id"]);
            array_push($services, $servicen );
            $total+=$servicen->price;
        }

        $total+=setting('member.join_fee');


        return $total;
    }


    public function invoiceJoining(String $poll_url){
        $invoice = Invoice::create([
            "invoice_date" =>  new DateTime(),
            "type" => "Payment",
            "description" => "New member initial payment for".strval($this->first_name)." ".strval($this->last_name)." for".$this->getServiceNames(),
            "subtotal" => $this->getServiceTotal(),
            "obituary_id" => 0,
            "total" => $this->getServiceTotal(),
            "applicant_id" => $this->id,
            "status" => "unpaid",
            "due_date" => new DateTime(),
        ]);

        InvoiceItem::create([
            "title" => "New member initial payment for".strval($this->first_name)." ".strval($this->last_name)." for".$this->getServiceNames(),
            "amount" => $this->getServiceTotal(),
            "invoice_id" => $invoice->id
        ]);



        $invoice->poll_url = $poll_url;
        $invoice->save();
        return $invoice;
    }

    public function getNomineesReadAttribute()
    {
        return json_encode($this->nominees);
    }

    public function getServicessBrowseAttribute(){
        return json_encode($this->services);
    }

    public function getServicesReadAttribute(){
        return json_encode($this->services);
    }

    protected function gender()
    {
        return $this->gender === "m" ? "Male" : "Female";
    }

    public function getGenderBrowseAttribute()
    {
        return $this->gender();
    }

    public function getGenderReadAttribute()
    {
        return $this->gender();
    }
}

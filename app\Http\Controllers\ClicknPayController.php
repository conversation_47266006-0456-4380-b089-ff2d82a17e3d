<?php
//Also refer to deposits controller for deposits code

namespace App\Http\Controllers;

use App\User;
use DateTime;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Nominee;
use Illuminate\Support\Facades\Http;
use App\Models\Member;
use App\Models\Applicant;
use App\Models\Deposit;
use Paynow\Payments\Paynow;
use Illuminate\Support\Collection;
use App\Service;
use Illuminate\Http\Request;
use App\Notifications\PaymentCompleted;
use Illuminate\Support\Facades\DB;
use App\Notifications\DepositNotification;
use Exception;
use Illuminate\Support\Facades\Notification;
use Ramsey\Uuid\Type\Decimal;

class ClicknPayController extends Controller
{

    //New member payment request
    public function newMemberPayment(int $applicantId, String $phone){
        $applicant =  Applicant::find($applicantId);
        if(!isset($applicant))  return abort(404);
        $member = Member::whereEmail($applicant->email)->first();
        if($member != null) return response()->json(['message' => 'Member with that email already exists']);

        $servicesTotal= $applicant->getServiceTotal();
        try{
            $response = $this->processPayment($phone, $servicesTotal);
        }catch(Exception $e){
            return response()->json(['message' => 'An error occured while processing your request.'], 500);
        }
        $applicant->invoiceJoining($response['clientCorrelator']);
        return response()->json(['pollUrl' => $response['clientCorrelator']]);
    }


        /**
    * @return Member
    */
    public function checkChatBotNewMemberPayment(Request $request){
        try{
            $paymentResult = $this->getConfirmation($request['endUserId'],$request['clientCorrelator'] );
            if($paymentResult['status']!='COMPLETED')
              return response()->json(['message' => 'Payment not complete'], 500);
        }catch(Exception $e){
            return response()->json(['message' => 'ClicknPay transaction not found'], 500);
        }

        $applicant = Applicant::find($request['applicantId']);

        if (!isset($applicant))
           return response()->json([ 'message' => 'Applicant was not found',], 500);

        $member = Member::whereEmail($applicant->email)->first();

        if($member != null)
            return response()->json(['status' => 'COMPLETED','message' => 'Member with that email already exists. Contact admin.',],500);

        $invoice = $applicant->invoiceJoining($request['clientCorrelator']);

        $memCon = new MemberController();
        $memCon->createPaidMember($invoice, $paymentResult['amount'], $request['clientCorrelator'], 'ClicknPay');
        return response()->json([ 'status' => $paymentResult['status'],'message' => 'Payment received and new member created.',]);
    }


    public function checkNewMemberPayment($phone, $clientCorrelator){
        try{
            $paymentResult = $this->getConfirmation($phone,$clientCorrelator);
            if($paymentResult['status']!='COMPLETED')
                return response()->json(['message' => 'Payment not complete'], 500);
        }catch(Exception $e){
            return response()->json(['message' => 'ClicknPay transaction not found'], 500);
        }

        $invoice = Invoice::wherePollUrl($clientCorrelator)->first();
        $memCon = new MemberController();
        $memCon->createPaidMember($invoice, $paymentResult['amount'], $paymentResult['clientCorrelator'], 'ClicknPay');
        return response()->json([ 'status' => 'COMPLETED','message' => 'New member payment processed.',]);
    }




    public function memberDeposit(Member $member, float $total, $phone){
        try{
            $response = $this->processPayment($phone, $total);
        }catch(Exception $e){
            return response()->json(['message' => 'An error occured while processing your request.'], 500);
        }
        $this->invoiceMemberForDeposit($member, $total, $response['clientCorrelator']);
        return response()->json(['pollUrl' => $response['clientCorrelator']]);
    }

    public function chatbotMemberDeposit(Request $request){
        $member = Member::find($request['memberId']);
        if(!isset($member))
            return response()->json(['message' => 'Member does not exist'], 500);

        $user = User::find($member->user_id);
        if (!isset($user))
            return response()->json(['message' => 'User for deposit was not found'], 500);

        $deposit = Deposit::wherePaymentRef($request['clientCorrelator'])->first();
        if(isset($deposit)){
            return response()->json([
               'message' => 'Deposit already processed',
               'amount'=> $deposit->amount,
               'memberId'=> $member->id,
               'funded'=> true,
               'responseCode'=> 200
            ]);
        }
        try{
            $paymentResult =    $this->getConfirmation($request['endUserId'], $request['clientCorrelator'] );
            if($paymentResult['status']!='COMPLETED')
              return response()->json(['message' => 'Payment not complete'], 500);
        }catch(Exception $e){
            return response()->json(['message' => 'ClicknPay transaction not found'], 500);
        }

        $member->memberDeposit($paymentResult['amount'], $request['clientCorrelator'], "ClicknPayBot", "Clicknpay chatbot deposit for member" );

        try{
            Notification::route('mail', $member->email)->notify(new DepositNotification($paymentResult['amount']));

        }catch(Exception $e){
            logger("An error occured while attempting to record a member deposit via ClicknPay. The deposit was however successfull: ".$e->getMessage());
            return response()->json(['message' => 'Payment not complete. Contact admin.'], 500);
        }

        return response()->json([
            'message' => 'Deposit Successsful',
            'amount'=> $paymentResult['amount'],
            'memberId'=> $member->id,
            'funded'=> true,
            'responseCode'=> 200
        ]);


    }


    public function ssdMemberDeposit(Request $request){
        logger("SSD deposit request to deposit funds. Details :");
        logger($request);

        $token = $request->bearerToken();

        if($token!="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************.qihL1gh5aPrrCal4jfkLNa1x98USkyGLj-pYZJ9f2A4")
            return response()->json(['message' => 'You are not authorized to access this resource'], 401);

        if($request["apiKey"]!="6952UI853759")
            return response()->json(['message' => 'You are not authorized to access this resource'], 401);


        $member = Member::find($request['memberId']);
        if(!isset($member))
            return response()->json(['message' => 'Member does not exist'], 500);

        $user = User::find($member->user_id);
        if (!isset($user))
            return response()->json(['message' => 'User for deposit was not found'], 500);

        $deposit = Deposit::wherePaymentRef($request['transactionId'])->first();
        if(isset($deposit)){
            return response()->json([
               'message' => 'Deposit already processed',
               'amount'=> $deposit->amount,
               'memberId'=> $member->id,
               'funded'=> false,
               'responseCode'=> 500
            ]);
        }


        $member->memberDeposit($request['amount'], $request['transactionId'], "ClicknPaySSD", "Clicknpay ssd deposit for member" );


        try{
            Notification::route('mail', $member->email)->notify(new DepositNotification($request['amount']));

        }catch(Exception $e){
            logger("An error occured while attempting to record a member ssd deposit via ClicknPay. The deposit was however successfull: ".$e->getMessage());
            return response()->json(['message' => 'Payment not complete. Contact admin.'], 500);
        }

        return response()->json([
            'message' => 'Deposit Successsful',
            'amount'=> $request['amount'],
            'memberId'=> $member->id,
            'funded'=> true,
            'responseCode'=> 200
        ]);
    }



    public function checkMemberDeposit($phone, $clientCorrelator){

        try{
            $paymentResult = $this->getConfirmation($phone,$clientCorrelator);
            if($paymentResult['status']!='COMPLETED')
              return response()->json(['message' => 'Payment not complete'], 500);
        }catch(Exception $e){
            return response()->json(['message' => 'ClicknPay transaction not found'], 500);
        }

        $invoice = Invoice::wherePollUrl($clientCorrelator)->first();
        if( $invoice->status == "paid")
            return response()->json(['status' => 'COMPLETED','message' => 'Payment already processed',]);

        $member = Member::find($invoice->member_id);
        if(!isset($member))
            return response()->json(['message' => 'Member does not exist'], 500);

        $user = User::find($member->user_id);
        if (!isset($user))
            return response()->json(['message' => 'User for deposit was not found'], 500);

        $deposit = Deposit::wherePaymentRef($paymentResult['clientCorrelator'])->first();
        if(isset($deposit)){
            return response()->json([
               'message' => 'Deposit already processed',
               'amount'=> $deposit->amount,
               'memberId'=> $member->id,
               'funded'=> true,
               'responseCode'=> 200
            ]);
        }

        $member->memberDeposit($paymentResult['amount'], $paymentResult['clientCorrelator'], "ClicknPay", "Clicknpay deposit for member" );

        try{
            Notification::route('mail', $member->email)->notify(new DepositNotification($paymentResult['amount']));

        }catch(Exception $e){
            logger("An error occured while attempting to record a member deposit via ClicknPay. The deposit was however successfull: ".$e->getMessage());
            return response()->json(['message' => 'Payment not complete. Contact admin.'], 500);
        }

        return response()->json(['message' => 'Deposit Successsful']);






    }







    /**
     * @return Invoice
     */
    public function invoiceMemberForDeposit(Member $member, float $total, $clientCorrelator){
        $invoice = Invoice::create([
            "invoice_date" =>  new DateTime(),
            "type" => "Deposit",
            "description" => "Member deposit paynow",
            "subtotal" => $total,
            "obituary_id" => 0,
            "total" => $total,
            "member_id" => $member->id,
            "status" => "unpaid",
            "due_date" => new DateTime(),
        ]);

        InvoiceItem::create([
            "title" => "Member deposit" ,
            "amount" => $total,
            "invoice_id" => $invoice->id
        ]);
        $invoice->poll_url = $clientCorrelator;
        $invoice->save();
        return $invoice;
    }




    // Private Functions
    private function processPayment(String $phone, float $amount ){
        $data = [
            "amount"=>$amount,
            "currency"=>"usd",
            "endUserId"=>$phone,
            "uniqueId"=>"uazDKjxszBQfBLero",
            "description"=>"Shekinah Life Club",
            "notifyUrl"=> env('APP_URL')."/clicknpay/deposit/payment-complete/1"

            ];

        $coockie = [
            'Content-Type' => 'application/json',
        ];

        $response =  Http::withBody(json_encode($data), 'application/json')
        ->withOptions(['headers' => $coockie])
        ->post('https://backendservices.clicknpay.africa:2080/ecocashapi/initiate');


        if($response->status()!=200){
            logger($response);
            throw new Exception("An error occured while trying to make payment");
        }

        if(!$response){
            logger($response);
            throw new Exception("An error occured while trying to make payment");
        }

        return $response;

    }


    private function getConfirmation(String $id, String $ref ){
        $data = [
            "amount"=>1,
            "currency"=>"usd",
            "endUserId"=> $id,
            "uniqueId"=>"uazDKjxszBQfBLero",
            "description"=>"Shekinah Life Club",
            "notifyUrl"=> env('APP_URL')."/clicknpay/deposit/payment-complete/1"

            ];

        $coockie = [ 'Content-Type' => 'application/json', ];
        $response = ['status'=> 'PENDING SUBSCRIBER VALIDATION'];

        $response = Http::withBody(json_encode($data), 'application/json')
        ->withOptions(['headers' => $coockie])
        ->get('https://backendservices.clicknpay.africa:2080/ecocashapi/status/'.$id.'/'.$ref.'');

        if($response->status()!=200){
            logger($response);
            throw new Exception("Payment not found");
        }

        return $response;
    }
}

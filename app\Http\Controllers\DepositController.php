<?php
// Also refer to Paynow controller for more deposits code
namespace App\Http\Controllers;

use App\Models\Deposit;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Member;
use Illuminate\Http\Request;
use App\User;
use App\Notifications\DepositNotification;
use Exception;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Auth;

class DepositController extends SharedBaseController
{
    public function index()
    {
        $deposits = Deposit::whereMemberId(Auth::user()->member_id)->get();
        return view('member.deposits', compact('deposits'));
    }


    public function manual($member)
    {
        $member = Member::find($member);
        return view('vendor.voyager.wallets.read', compact('member'));
    }
    public function manually(Request $request)
    {
        $member = Member::find($request->id);

        $member->memberDeposit($request->amount, $request->payment_ref, "Admin", "Adjustment of member balance by admin" );

        return redirect()->route("voyager.members.index")->with([
            'message'    => __('voyager::generic.successfully_updated')." Deposit.",
            'alert-type' => 'success',
        ]);
    }

}

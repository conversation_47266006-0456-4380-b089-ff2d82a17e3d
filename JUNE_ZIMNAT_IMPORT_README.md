# JUNE Zimnat Payments Import

This document explains how to import the `JUNE_Zimnat_payments.csv` file into the members and nominees tables.

## Overview

The CSV file contains member and nominee data where:
- Rows with "H" in the first column are **members** (heads of household)
- Rows following an "H" row (without "H") are **nominees/dependants** of that member

## CSV Structure

The CSV has the following columns:
1. Member Type (H for member, empty for nominee)
2. First Name
3. Surname
4. Birth Date
5. Gender
6. National ID
7. Phone Number
8. Address
9. Amount Paid
10. Policy Option
11. Tenure

## Import Methods

### Method 1: Laravel Artisan Command (Recommended)

1. **Dry Run (Test without making changes):**
   ```bash
   php artisan import:june-zimnat-payments --dry-run
   ```

2. **Live Import:**
   ```bash
   php artisan import:june-zimnat-payments
   ```

### Method 2: Direct PHP Script

1. **Dry Run (Test without making changes):**
   ```bash
   php import_june_zimnat.php
   ```

2. **Live Import:**
   ```bash
   php import_june_zimnat.php --live
   ```

### Method 3: <PERSON><PERSON>der

1. **Add to DatabaseSeeder.php:**
   ```php
   public function run()
   {
       $this->call(ImportJuneZimnatPayments::class);
   }
   ```

2. **Run the seeder:**
   ```bash
   php artisan db:seed --class=ImportJuneZimnatPayments
   ```

## Data Mapping

### Members Table
- `first_name` ← First Name
- `last_name` ← Surname  
- `dob` ← Birth Date (parsed to Y-m-d format)
- `gender` ← Gender (normalized to Male/Female)
- `gov_id` ← National ID
- `phone` ← Phone Number
- `street` ← Address
- `country` ← "Zimbabwe" (default)
- `id` ← Auto-generated unique ID

### Nominees Table
- `member_id` ← Links to the member's ID
- `full_name` ← First Name + Surname
- `dob` ← Birth Date (parsed to Y-m-d format)
- `gov_id` ← National ID

## Features

### Duplicate Prevention
- **Members**: Checked by National ID or combination of first name, last name, and DOB
- **Nominees**: Checked by member_id, full_name, and DOB

### Date Parsing
Supports multiple date formats:
- d/m/Y (18/06/1962)
- d/m/y (18/06/62)
- d-m-Y (18-06-1962)
- Y-m-d (1962-06-18)
- And more...

### Gender Normalization
- "Male", "M", "man" → "Male"
- "Female", "F", "woman" → "Female"

### Error Handling
- Skips empty rows
- Logs parsing errors
- Uses database transactions (rollback on failure)
- Provides detailed error reporting

## Sample Output

```
Starting import of JUNE_Zimnat_payments.csv...
✓ Member: Anyway L Mupanduki
  ✓ Nominee: Nellie Mupanduki
  ✓ Nominee: Trust Siwela
✓ Member: Roberts T Marenya
  ✓ Nominee: Nobulawu S L Sithole

=== Import Summary ===
Members processed: 45
Nominees processed: 89
✓ Import completed successfully!
```

## Troubleshooting

### Common Issues

1. **CSV file not found**
   - Ensure `database/seeds/JUNE_Zimnat_payments.csv` exists
   - Check file permissions

2. **Database connection errors**
   - Verify `.env` database configuration
   - Ensure database is running

3. **Date parsing errors**
   - Check date formats in CSV
   - Review warning messages for specific dates

4. **Duplicate key errors**
   - The script handles duplicates automatically
   - Check error messages for specific issues

### Validation

After import, verify the data:

```sql
-- Check member count
SELECT COUNT(*) FROM members;

-- Check nominee count  
SELECT COUNT(*) FROM nominees;

-- Check members with nominees
SELECT m.first_name, m.last_name, COUNT(n.id) as nominee_count
FROM members m
LEFT JOIN nominees n ON m.id = n.member_id
GROUP BY m.id, m.first_name, m.last_name
ORDER BY nominee_count DESC;
```

## Files Created

1. `app/Console/Commands/ImportJuneZimnatPayments.php` - Artisan command
2. `database/seeds/ImportJuneZimnatPayments.php` - Laravel seeder
3. `import_june_zimnat.php` - Standalone PHP script
4. `JUNE_ZIMNAT_IMPORT_README.md` - This documentation

## Safety Features

- **Dry run mode**: Test imports without making changes
- **Database transactions**: Automatic rollback on errors
- **Duplicate detection**: Prevents duplicate entries
- **Comprehensive logging**: Detailed success/error reporting

## Recommendations

1. **Always run dry-run first** to identify potential issues
2. **Backup your database** before running the live import
3. **Review the summary** and error messages carefully
4. **Verify data** after import using SQL queries

## Support

If you encounter issues:
1. Check the error messages in the output
2. Verify the CSV file format matches expectations
3. Ensure database connectivity and permissions
4. Review the Laravel logs for additional details

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class ImportJuneZimnatPayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:june-zimnat-payments {--dry-run : Run without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import JUNE_Zimnat_payments.csv into members and nominees tables';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $csvFile = database_path('seeds/JUNE_Zimnat_payments.csv');
        
        if (!file_exists($csvFile)) {
            $this->error("CSV file not found: {$csvFile}");
            return 1;
        }

        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('=== DRY RUN MODE - No changes will be made ===');
        }

        $this->info('Starting import of JUNE_Zimnat_payments.csv...');
        
        $handle = fopen($csvFile, 'r');
        
        // Skip the header row
        fgetcsv($handle);
        
        $currentMember = null;
        $membersImported = 0;
        $nomineesImported = 0;
        $errors = [];
        
        // Start database transaction if not dry run
        if (!$isDryRun) {
            DB::beginTransaction();
        }
        
        try {
            while (($data = fgetcsv($handle)) !== false) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($data))) {
                        continue;
                    }
                    
                    $memberType = trim($data[0]);
                    $firstName = trim($data[1]);
                    $surname = trim($data[2]);
                    $birthDate = trim($data[3]);
                    $gender = trim($data[4]);
                    $nationalId = trim($data[5]);
                    $phoneNumber = trim($data[6]);
                    $address = trim($data[7]);
                    $amountPaid = trim($data[8]);
                    $policyOption = trim($data[9]);
                    $tenure = trim($data[10]);
                    
                    // Skip rows with no name data
                    if (empty($firstName) && empty($surname)) {
                        continue;
                    }
                    
                    if ($memberType === 'H') {
                        // This is a member (head)
                        $currentMember = $this->createMember([
                            'first_name' => $firstName,
                            'last_name' => $surname,
                            'dob' => $this->parseDate($birthDate),
                            'gender' => $this->normalizeGender($gender),
                            'gov_id' => $nationalId,
                            'phone' => $phoneNumber,
                            'street' => $address,
                            'amount_paid' => $this->parseAmount($amountPaid),
                            'policy_option' => $policyOption,
                            'tenure' => $tenure
                        ], $isDryRun);
                        
                        if ($currentMember) {
                            $membersImported++;
                            $this->info("✓ Member: {$firstName} {$surname}" . ($isDryRun ? ' (DRY RUN)' : ''));
                        }
                    } else {
                        // This is a nominee/dependant
                        if ($currentMember) {
                            $nominee = $this->createNominee($currentMember['id'], [
                                'full_name' => trim($firstName . ' ' . $surname),
                                'dob' => $this->parseDate($birthDate),
                                'gov_id' => $nationalId,
                                'gender' => $this->normalizeGender($gender),
                                'phone' => $phoneNumber,
                                'address' => $address,
                                'amount_paid' => $this->parseAmount($amountPaid),
                                'policy_option' => $policyOption,
                                'tenure' => $tenure
                            ], $isDryRun);
                            
                            if ($nominee) {
                                $nomineesImported++;
                                $this->info("  ✓ Nominee: {$firstName} {$surname}" . ($isDryRun ? ' (DRY RUN)' : ''));
                            }
                        } else {
                            $errors[] = "Nominee found without member: {$firstName} {$surname}";
                        }
                    }
                    
                } catch (Exception $e) {
                    $errors[] = "Error processing row: " . implode(',', $data) . " - " . $e->getMessage();
                }
            }
            
            if (!$isDryRun) {
                DB::commit();
            }
            
        } catch (Exception $e) {
            if (!$isDryRun) {
                DB::rollback();
            }
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
        
        fclose($handle);
        
        $this->info("\n=== Import Summary ===");
        $this->info("Members processed: {$membersImported}");
        $this->info("Nominees processed: {$nomineesImported}");
        
        if (!empty($errors)) {
            $this->error("\n=== Errors ===");
            foreach ($errors as $error) {
                $this->error($error);
            }
        }
        
        if ($isDryRun) {
            $this->info("\n=== DRY RUN COMPLETED - No changes were made ===");
            $this->info("Run without --dry-run flag to perform actual import");
        } else {
            $this->info("\n✓ Import completed successfully!");
        }
        
        return 0;
    }
    
    /**
     * Create a member record
     */
    private function createMember($data, $isDryRun = false)
    {
        try {
            // Generate a unique member ID
            $memberId = $this->generateMemberId();
            
            $memberData = [
                'id' => $memberId,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'dob' => $data['dob'],
                'gender' => $data['gender'],
                'gov_id' => $data['gov_id'],
                'phone' => $data['phone'],
                'street' => $data['street'],
                'country' => 'Zimbabwe', // Default country
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if member already exists by gov_id or name combination
            $existingMember = DB::table('members')
                ->where(function($query) use ($data) {
                    if (!empty($data['gov_id'])) {
                        $query->where('gov_id', $data['gov_id']);
                    }
                })
                ->orWhere(function($query) use ($data) {
                    $query->where('first_name', $data['first_name'])
                          ->where('last_name', $data['last_name']);
                    if ($data['dob']) {
                        $query->where('dob', $data['dob']);
                    }
                })
                ->first();
                
            if ($existingMember) {
                $this->warn("  ⚠ Member already exists: {$data['first_name']} {$data['last_name']}");
                return ['id' => $existingMember->id];
            }
            
            if (!$isDryRun) {
                DB::table('members')->insert($memberData);
            }
            
            return ['id' => $memberId];
            
        } catch (Exception $e) {
            $this->error("Error creating member {$data['first_name']} {$data['last_name']}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create a nominee record
     */
    private function createNominee($memberId, $data, $isDryRun = false)
    {
        try {
            $nomineeData = [
                'member_id' => $memberId,
                'full_name' => $data['full_name'],
                'dob' => $data['dob'],
                'gov_id' => $data['gov_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if nominee already exists
            $existingNominee = DB::table('nominees')
                ->where('member_id', $memberId)
                ->where('full_name', $data['full_name'])
                ->first();
                
            if ($existingNominee) {
                $this->warn("    ⚠ Nominee already exists: {$data['full_name']}");
                return ['id' => $existingNominee->id];
            }
            
            if (!$isDryRun) {
                $nomineeId = DB::table('nominees')->insertGetId($nomineeData);
                return ['id' => $nomineeId];
            }
            
            return ['id' => 'dry-run-id'];
            
        } catch (Exception $e) {
            $this->error("Error creating nominee {$data['full_name']}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Generate a unique member ID
     */
    private function generateMemberId()
    {
        do {
            $id = rand(1000, 999999);
        } while (DB::table('members')->where('id', $id)->exists());
        
        return $id;
    }
    
    /**
     * Parse date from various formats
     */
    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }
        
        try {
            // Try different date formats
            $formats = [
                'd/m/Y',
                'd/m/y',
                'd-m-Y',
                'd-m-y',
                'Y-m-d',
                'd M Y',
                'd/m/Y H:i:s'
            ];
            
            foreach ($formats as $format) {
                $date = \DateTime::createFromFormat($format, $dateString);
                if ($date !== false) {
                    return $date->format('Y-m-d');
                }
            }
            
            // Try Carbon parsing as fallback
            return Carbon::parse($dateString)->format('Y-m-d');
            
        } catch (Exception $e) {
            $this->warn("Could not parse date: {$dateString}");
            return null;
        }
    }
    
    /**
     * Normalize gender values
     */
    private function normalizeGender($gender)
    {
        $gender = strtolower(trim($gender));
        
        if (in_array($gender, ['male', 'm', 'man'])) {
            return 'Male';
        } elseif (in_array($gender, ['female', 'f', 'woman'])) {
            return 'Female';
        }
        
        return ucfirst($gender);
    }
    
    /**
     * Parse amount from string
     */
    private function parseAmount($amountString)
    {
        if (empty($amountString)) {
            return null;
        }
        
        // Remove currency symbols and spaces
        $amount = preg_replace('/[^\d.,]/', '', $amountString);
        
        return floatval($amount);
    }
}

<?php

/**
 * Simple script to import JUNE_Zimnat_payments.csv
 * Run this from the Laravel root directory: php import_june_zimnat.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class JuneZimnatImporter
{
    private $dryRun;
    private $membersImported = 0;
    private $nomineesImported = 0;
    private $errors = [];
    
    public function __construct($dryRun = true)
    {
        $this->dryRun = $dryRun;
    }
    
    public function import()
    {
        $csvFile = 'database/seeds/JUNE_Zimnat_payments.csv';
        
        if (!file_exists($csvFile)) {
            echo "ERROR: CSV file not found: {$csvFile}\n";
            return false;
        }

        if ($this->dryRun) {
            echo "=== DRY RUN MODE - No changes will be made ===\n";
        }

        echo "Starting import of JUNE_Zimnat_payments.csv...\n";
        
        $handle = fopen($csvFile, 'r');
        
        // Skip the header row
        fgetcsv($handle);
        
        $currentMember = null;
        
        // Start database transaction if not dry run
        if (!$this->dryRun) {
            DB::beginTransaction();
        }
        
        try {
            while (($data = fgetcsv($handle)) !== false) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($data))) {
                        continue;
                    }
                    
                    $memberType = trim($data[0]);
                    $firstName = trim($data[1]);
                    $surname = trim($data[2]);
                    $birthDate = trim($data[3]);
                    $gender = trim($data[4]);
                    $nationalId = trim($data[5]);
                    $phoneNumber = trim($data[6]);
                    $address = trim($data[7]);
                    $amountPaid = trim($data[8]);
                    $policyOption = trim($data[9]);
                    $tenure = trim($data[10]);
                    
                    // Skip rows with no name data
                    if (empty($firstName) && empty($surname)) {
                        continue;
                    }
                    
                    if ($memberType === 'H') {
                        // This is a member (head)
                        $currentMember = $this->createMember([
                            'first_name' => $firstName,
                            'last_name' => $surname,
                            'dob' => $this->parseDate($birthDate),
                            'gender' => $this->normalizeGender($gender),
                            'gov_id' => $nationalId,
                            'phone' => $phoneNumber,
                            'street' => $address,
                            'amount_paid' => $this->parseAmount($amountPaid),
                            'policy_option' => $policyOption,
                            'tenure' => $tenure
                        ]);
                        
                        if ($currentMember) {
                            $this->membersImported++;
                            echo "✓ Member: {$firstName} {$surname}" . ($this->dryRun ? ' (DRY RUN)' : '') . "\n";
                        }
                    } else {
                        // This is a nominee/dependant
                        if ($currentMember) {
                            $nominee = $this->createNominee($currentMember['id'], [
                                'full_name' => trim($firstName . ' ' . $surname),
                                'dob' => $this->parseDate($birthDate),
                                'gov_id' => $nationalId,
                                'gender' => $this->normalizeGender($gender),
                                'phone' => $phoneNumber,
                                'address' => $address,
                                'amount_paid' => $this->parseAmount($amountPaid),
                                'policy_option' => $policyOption,
                                'tenure' => $tenure
                            ]);
                            
                            if ($nominee) {
                                $this->nomineesImported++;
                                echo "  ✓ Nominee: {$firstName} {$surname}" . ($this->dryRun ? ' (DRY RUN)' : '') . "\n";
                            }
                        } else {
                            $this->errors[] = "Nominee found without member: {$firstName} {$surname}";
                        }
                    }
                    
                } catch (Exception $e) {
                    $this->errors[] = "Error processing row: " . implode(',', $data) . " - " . $e->getMessage();
                }
            }
            
            if (!$this->dryRun) {
                DB::commit();
            }
            
        } catch (Exception $e) {
            if (!$this->dryRun) {
                DB::rollback();
            }
            echo "Import failed: " . $e->getMessage() . "\n";
            return false;
        }
        
        fclose($handle);
        
        $this->printSummary();
        return true;
    }
    
    private function printSummary()
    {
        echo "\n=== Import Summary ===\n";
        echo "Members processed: {$this->membersImported}\n";
        echo "Nominees processed: {$this->nomineesImported}\n";
        
        if (!empty($this->errors)) {
            echo "\n=== Errors ===\n";
            foreach ($this->errors as $error) {
                echo "ERROR: {$error}\n";
            }
        }
        
        if ($this->dryRun) {
            echo "\n=== DRY RUN COMPLETED - No changes were made ===\n";
            echo "Run with --live flag to perform actual import\n";
        } else {
            echo "\n✓ Import completed successfully!\n";
        }
    }
    
    private function createMember($data)
    {
        try {
            // Generate a unique member ID
            $memberId = $this->generateMemberId();
            
            $memberData = [
                'id' => $memberId,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'dob' => $data['dob'],
                'gender' => $data['gender'],
                'gov_id' => $data['gov_id'],
                'phone' => $data['phone'],
                'street' => $data['street'],
                'country' => 'Zimbabwe', // Default country
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if member already exists by gov_id or name combination
            $existingMember = DB::table('members')
                ->where(function($query) use ($data) {
                    if (!empty($data['gov_id'])) {
                        $query->where('gov_id', $data['gov_id']);
                    }
                })
                ->orWhere(function($query) use ($data) {
                    $query->where('first_name', $data['first_name'])
                          ->where('last_name', $data['last_name']);
                    if ($data['dob']) {
                        $query->where('dob', $data['dob']);
                    }
                })
                ->first();
                
            if ($existingMember) {
                echo "  ⚠ Member already exists: {$data['first_name']} {$data['last_name']}\n";
                return ['id' => $existingMember->id];
            }
            
            if (!$this->dryRun) {
                DB::table('members')->insert($memberData);
            }
            
            return ['id' => $memberId];
            
        } catch (Exception $e) {
            echo "ERROR: Error creating member {$data['first_name']} {$data['last_name']}: " . $e->getMessage() . "\n";
            return null;
        }
    }
    
    private function createNominee($memberId, $data)
    {
        try {
            $nomineeData = [
                'member_id' => $memberId,
                'full_name' => $data['full_name'],
                'dob' => $data['dob'],
                'gov_id' => $data['gov_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if nominee already exists
            $existingNominee = DB::table('nominees')
                ->where('member_id', $memberId)
                ->where('full_name', $data['full_name'])
                ->first();
                
            if ($existingNominee) {
                echo "    ⚠ Nominee already exists: {$data['full_name']}\n";
                return ['id' => $existingNominee->id];
            }
            
            if (!$this->dryRun) {
                $nomineeId = DB::table('nominees')->insertGetId($nomineeData);
                return ['id' => $nomineeId];
            }
            
            return ['id' => 'dry-run-id'];
            
        } catch (Exception $e) {
            echo "ERROR: Error creating nominee {$data['full_name']}: " . $e->getMessage() . "\n";
            return null;
        }
    }
    
    private function generateMemberId()
    {
        do {
            $id = rand(1000, 999999);
        } while (DB::table('members')->where('id', $id)->exists());
        
        return $id;
    }
    
    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }
        
        try {
            // Try different date formats
            $formats = [
                'd/m/Y',
                'd/m/y',
                'd-m-Y',
                'd-m-y',
                'Y-m-d',
                'd M Y',
                'd/m/Y H:i:s'
            ];
            
            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateString);
                if ($date !== false) {
                    return $date->format('Y-m-d');
                }
            }
            
            // Try Carbon parsing as fallback
            return Carbon::parse($dateString)->format('Y-m-d');
            
        } catch (Exception $e) {
            echo "WARNING: Could not parse date: {$dateString}\n";
            return null;
        }
    }
    
    private function normalizeGender($gender)
    {
        $gender = strtolower(trim($gender));
        
        if (in_array($gender, ['male', 'm', 'man'])) {
            return 'Male';
        } elseif (in_array($gender, ['female', 'f', 'woman'])) {
            return 'Female';
        }
        
        return ucfirst($gender);
    }
    
    private function parseAmount($amountString)
    {
        if (empty($amountString)) {
            return null;
        }
        
        // Remove currency symbols and spaces
        $amount = preg_replace('/[^\d.,]/', '', $amountString);
        
        return floatval($amount);
    }
}

// Check command line arguments
$dryRun = true;
if (isset($argv[1]) && $argv[1] === '--live') {
    $dryRun = false;
}

// Run the import
$importer = new JuneZimnatImporter($dryRun);
$success = $importer->import();

exit($success ? 0 : 1);

<?php $__env->startSection('page'); ?>
Transactions
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Transactions</h4>
            </div>
            <div class="card-body">
                <deposits-table :deposits="<?php echo e($deposits); ?>"></deposits-table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('member.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/deposits.blade.php ENDPATH**/ ?>
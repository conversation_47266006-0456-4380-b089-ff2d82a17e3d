@extends('member.master')

@section('page')
Member
@endsection

@section('content')
<div class="row">
    @if (Auth::user()->role_id==1)
        <div class="col-md-6 col-sm-6">
            <div class="card card-stats">
                <div class="card-body ">
                    <div class="row">
                        <div class="col-5 col-md-4">
                            <div class="icon-big text-center icon-warning">
                                <i class="nc-icon nc-single-02 text-warning"></i>
                            </div>
                        </div>
                        <div class="col-7 col-md-8">
                            <div class="numbers">
                                <p class="card-category">Members</p>
                                <p class="card-title">{{$members_count}}<p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer ">
                    <hr>
                    <div class="stats">
                        <i class="fa fa-refresh"></i>
                        Update Now
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-body ">
                <div class="row">
                    <div class="col-5 col-md-4">
                        <div class="icon-big text-center icon-warning">
                            <i class="nc-icon nc-money-coins text-success"></i>
                        </div>
                    </div>
                    <div class="col-7 col-md-8">
                        <div class="numbers">
                            <p class="card-category">Transactions</p>
                            <a href="{{route("members-area.deposits")}}" class="card-title">View</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer ">
                <hr>
                <div class="stats">
                    <i class="fa fa-calendar-o"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- column -->
    <div class="col-md-12">
        <div class="card mb-5">
            <div class="card-body">
                <h4 class="card-title"><i class="nc-icon nc-sound-wave"></i>Registered Services</h4>
            </div>
            <div class="comment-widgets">
                @forelse ($services as  $s)
                <div class="d-flex flex-row mb-3 mt-0">
                    <div class="p-2"><img src="{{asset('storage/'.$s?->photo)}}" alt width="50"
                            class="rounded-circle"></div>
                    <div class="comment-text w-100">
                        <span>
                            <h5>{{str_limit($s->title)}}</h5>
                        </span>
                        <span class="m-b-15 d-block">
                            {{str_limit($s->description)}}
                        </span>
                        <div class="comment-footer pr-2">
                            <span class="text-muted float-right">${{$s->price}}/month</span>

                        </div>
                    </div>
                </div>
                @empty
                    <div class="card-body">
                        <h4 >No services found</h4>
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection

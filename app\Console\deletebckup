        // // Update member balance on user balance cron
        // $schedule->call(function () {
        //     // logger("User balance Cron running:");
        //     $this->billBoardMembers();


        //     $users = User::all();
        //     for ($x = 0; $x <= $users->count() - 1; $x++) {
        //         $member = $users[$x]->memberDetails;
        //         if($member){
        //             $bal = number_format($users[$x]->balanceFloat, 2, '.', '');
        //             // logger("Member balance updated with value:", [$bal] );
        //             if($member->balance !=  $bal){
        //                 $member->balance =  $bal;
        //                 $member->save();
        //             }

        //             // logger("Balance updated member:", [$member]  );
        //         }else{
        //             // logger("User balance update failed:", [$users[$x]->id]  );
        //         }
        //     }
        // })->everyMinute();



        // Billing of orbituaries cron
        // $schedule->call(function () {
        //     try{
        //         // logger("Obituary Cron running");
        //         $members = Member::all();
        //         $obituaries = Obituary::all();
        //         for ($y = 0; $y <= $obituaries->count() - 1; $y++) {
        //             for ($x = 0; $x <= $members->count() - 1; $x++) {

        //                 $user = User::find($members[$x]->user_id);

        //                 // logger("Obituary Cron running:", [$members[$x]->id] );
        //                 // logger("Obituary", [$obituaries[$y]->id] );

        //                 if ($user ) {
        //                     $invoiced = Invoice::whereMemberIdAndType($user->member_id, $obituaries[$y]->id)->first();
        //                     // Create invoice for an obituary for this member
        //                     // otherwise if invoice exists and is unpaid
        //                     // check balance and pay if sufficient funds
        //                     if (!$invoiced) {
        //                         if ($user->balanceFloat >= $obituaries[$y]->donated_amount) {
        //                             $paid_status = "paid";
        //                             $donation = $members[$x]->donations()->create([
        //                                 "obituary_id" => $obituaries[$y]->id,
        //                                 "orderID" => "wallet",
        //                                 "amount" => $obituaries[$y]->donated_amount,
        //                                 "on" => now()
        //                             ]);
        //                             // logger("New invoice to be created and Donation added for member:", [$members[$x]->id]);
        //                             // logger("Obituary is:", [$obituaries[$y]->id]);

        //                         } else {
        //                             $paid_status = "unpaid";
        //                             // logger("Insufficient funds for member:", [$members[$x]->id]);
        //                         }

        //                         $user->forceWithdrawFloat($obituaries[$y]->donated_amount, ['description' => 'payment for obituary']);
        //                         // logger("Withdraw for member on obituary:", [$obituaries[$y]->id]);
        //                         $date = strtotime("+3 days");
        //                         $dueDate = date("D M d, Y G:i", $date);

        //                         $invoice = Invoice::create([
        //                             "invoice_date" => date("D M d, Y G:i"),
        //                             "type" => $obituaries[$y]->id,
        //                             "subtotal" => $obituaries[$y]->donated_amount,
        //                             "total" => $obituaries[$y]->donated_amount,
        //                             "member_id" => $members[$x]->id,
        //                             "status" => $paid_status,
        //                             "due_date" => $dueDate,
        //                         ]);
        //                         InvoiceItem::create([
        //                             "title" => $obituaries[$y]->full_name,
        //                             "amount" => $obituaries[$y]->donated_amount,
        //                             "invoice_id" => $invoice->id
        //                         ]);
        //                         // logger("Invoice created for member:", [$members[$x]->id]);
        //                     }
        //                     else{
        //                         if ($invoiced->status != "paid") {
        //                             // logger("Obituary already invoiced:", [$invoiced->id]);
        //                             // if ($user->balanceFloat >= $obituaries[$y]->donated_amount) {
        //                             if ($user->balanceFloat >= 0) {
        //                                 // if (!$obituaries[$y]->hasPaidId($members[$x]->id)) {
        //                                     $paid_status = "paid";
        //                                     $donation = $members[$x]->donations()->create([
        //                                         "obituary_id" => $obituaries[$y]->id,
        //                                         "orderID" => "wallet",
        //                                         "amount" => $obituaries[$y]->donated_amount,
        //                                         "on" => now()
        //                                     ]);
        //                                     $invoiced->status = "paid";
        //                                     $invoiced->save();
        //                                     // logger("Donation added for member:", [$members[$x]->id]);
        //                                 // }
        //                             } else {
        //                                 $hasPaid = Donation::whereMemberIdAndObituaryId($user->member_id, $obituaries[$y]->id)->first();
        //                                 if(isset($hasPaid)){
        //                                     $paid_status = "paid";
        //                                     $donation = $members[$x]->donations()->create([
        //                                         "obituary_id" => $obituaries[$y]->id,
        //                                         "orderID" => "wallet",
        //                                         "amount" => $obituaries[$y]->donated_amount,
        //                                         "on" => now()
        //                                     ]);
        //                                     $invoiced->status = "paid";
        //                                     $invoiced->save();
        //                                 }else{
        //                                     $paid_status = "unpaid";
        //                                 }
        //                                 // logger("Insufficient funds for member:", [$members[$x]->id]);
        //                             }
        //                         }else{
        //                             $hasPaid = Donation::whereMemberIdAndObituaryId($user->member_id, $obituaries[$y]->id)->first();
        //                             if(!isset($hasPaid)) {

        //                                 $paid_status = "paid";
        //                                 $donation = $members[$x]->donations()->create([
        //                                     "obituary_id" => $obituaries[$y]->id,
        //                                     "orderID" => "wallet",
        //                                     "amount" => $obituaries[$y]->donated_amount,
        //                                     "on" => now()
        //                                 ]);
        //                             }
        //                             // logger("Member already paid obituary:", [$obituaries[$y]->id]);
        //                             // logger("Orbituary status",$hasPaid->id );
        //                         }
        //                     }

        //                 } else {
        //                     // logger("Failed to invoice member:", [$members[$x]->user_id]);
        //                 }
        //             }
        //         }
        //     }
        //     catch (Exception $e) {
        //         // Log::alert($e);
        //     }
        // })->everyMinute();

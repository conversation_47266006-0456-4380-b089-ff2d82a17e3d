

<?php $__env->startSection('page'); ?>
My Profile
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-md-4">
        <div class="card card-user">
            <div class="image">
                <img src="<?php echo e(asset('storage/bg/profile-bg.jpg')); ?>" alt>
            </div>
            <div class="card-body">
                <div class="author">
                    <a href="javascript:;">
                        <img class="avatar border-gray" src="<?php echo e(asset('storage/users/default.png')); ?>" alt="...">
                        <h5 class="title"><?php echo e(Auth::user()->name); ?></h5>
                    </a>
                    <p class="description">
                        Membership ID: <strong><?php echo e($profile->id); ?></strong>
                    </p>
                    <p class="description text-center">
                        <?php echo e($profile->bio); ?>

                    </p>
                </div>
            </div>

        </div>

    </div>
    <div class="col-md-8">
        <?php if($errors->any()): ?>
        <div class="alert alert-danger" role="alert">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <?php endif; ?>
        <div class="card card-user mb-5">
            <div class="card-header">
                <h5 class="card-title">Edit Profile</h5>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('members-area.profile')); ?>" method="POST">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Membership ID</label>
                                <input type="text" class="form-control" disabled="" value="<?php echo e($profile->id ?? 0); ?>">
                                <input type="hidden" name="membership_Id" value="<?php echo e($profile->id ?? 0); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Phone</label>
                                <input name="phone" type="text" class="form-control" value="<?php echo e($profile->phone); ?>">
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="form-group">
                                <label for="email">Email address</label>
                                <input name="email" type="email" class="form-control" value="<?php echo e($profile->email); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>First Name</label>
                                <input name="first_name" type="text" class="form-control"
                                    value="<?php echo e($profile->first_name); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Middle Names</label>
                                <input name="middle_names" type="text" class="form-control"
                                    value="<?php echo e($profile->middle_names); ?>" readonly>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Last Name</label>
                                <input name="last_name" type="text" class="form-control"
                                    value="<?php echo e($profile->last_name); ?>" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Date of Birth</label>
                                <input name="street" type="text" class="form-control" value="<?php echo e(explode(" ", $profile->dob)[0]); ?>" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Date of Registration</label>
                                <input name="street" type="text" class="form-control" value="<?php echo e(explode(" ", $profile->created_at)[0]); ?>" readonly>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Street</label>
                                <input name="street" type="text" class="form-control" value="<?php echo e($profile->street); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>City</label>
                                <input name="city" type="text" class="form-control" value="<?php echo e($profile->city); ?>">
                            </div>
                        </div>
                        <country-picker classes="col-md-4" value="<?php echo e($profile->country); ?>"></country-picker>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>POSTCODE</label>
                                <input name="zip" type="text" class="form-control" value="<?php echo e($profile->zip); ?>">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>About Me</label>
                                <textarea name="bio" class="form-control textarea"
                                    placeholder="Your Bio..."><?php echo e($profile->bio); ?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="update ml-auto mr-auto">
                            <button type="submit" class="btn btn-primary ">Update Profile</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('member.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/member/profile.blade.php ENDPATH**/ ?>

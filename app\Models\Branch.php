<?php

namespace App\Models;

use App\Service;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;

class Branch extends Model
{
    use Notifiable;

    protected $fillable = [
        "id",
        "name",
        "addess"
    ];


    public function services(){
        return $this->hasMany(Member::class);
    }

    public function region(){
        return $this->belongsTo(Region::class);
    }

}

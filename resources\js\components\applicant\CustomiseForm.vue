<template>
  <div >
    <div class=" custom-checkbox mt-3 "
    v-for="(item3, index2) in categories" :key="index2">


    <div class="card text-center "  :style="'background-image: url(storage/img/categories/'+'.png);background-repeat: no-repeat;background-color: var(--base); background-size: cover; background-position: center;'" >
        <div class="card-body">
            <h5 class="card-title text-white" :id="'category-'+item3.id">{{item3.name}}</h5>
        </div>
    </div>


    <div class="row m-0">
        <div class=" p-0 custom-checkbox mt-3 col-lg-6 col-xl-4"

        v-for="(item, index) in item3.services" :key="index">
        <div class="m-2 card"
        @mouseenter="showText = item.id" @mouseleave="showText = item.id"
        style="padding: 2.5rem;height: 100%; background-color:#fcfcfc">
          <input
          :checked="form.services?.includes(item.id)??false"
            @change="updateTotal(item, item3)"
            :class="{ 'is-invalid': submitted && !form.services.contains(item.id) }"
            class="custom-control-input" :id="'const_serv'+item.id" type="checkbox" />
          <label class="custom-control-label disable-select" :for="'const_serv'+item.id">
            {{item.title }}
            <br>
              <small v-show="showText!=item.id" class="mt-1 line-clamp-4">{{item.description }}</small>
              <small v-show="showText==item.id" class="mt-1 " style="display: -webkit-box;">{{item.description }}</small>
            <br>
             <b class="mt-1">USD${{item.price}} per month</b>
          </label>
        </div>


        </div>
    </div>

    </div>



    <div class="mt-3 mr-sm-2">
        <br><h5 for="full_name">How many depedants do you wish to add?</h5>
        <input
          @change="addDependants()"
          class="form-control mb-30 bg-gray"
          type="number"
          name="additionalMembers"
          style="width:auto"
          min="0"
          v-model="additionalMembers"
          id="additionalMembers"
          placeholder="0"
        />
    </div>

    <div class="mt-3 mr-sm-2">
        <!-- <h6
      >Monthly Subscription: ${{totalPayable}}</h6> -->
      <div class="col-12 text-right"  v-if="totalPayable<=0&&consultationSelected">
        <button class="btn radix-btn">Select Required Services</button>
      </div>
      <div class="col-12 text-right"  v-if="!consultationSelected">
        <small class="text-danger px-4">Please add one item from consultation or hospitalisation cover to your selection.</small>
        <a class="btn radix-btn" href="#category-1">Add At Least One Consultation or Hospitalisation  Cover Package</a>
      </div>
      <div class="col-12 text-right" @click="submit()" v-if="totalPayable>0&&consultationSelected">
        <button class="btn radix-btn">Next Step</button>
      </div>
    </div>
  </div>
</template>

<script>
import { integer, required } from "vuelidate/lib/validators";
import { submitJoiningDetails } from "../../services/api";

const mustBeTrue = (value) => value;

export default {
  props: ['forms', 'categories'],
  data(){
    return {
      showText: 0,
      submitted: false,
      form: {},
      totalPayable: 0,
      eachPayable: 0,
      additionalMembers:0,
      consultationSelected:true
    }
  },
  mounted(){
    let consultations = this.categories.filter(item => item.id == 1)[0];

    console.log(consultations);
    this.form.services = [];
  },
  validations: {
    form: {
      services: { required,mustBeTrue },
    }
  },
  methods: {
    submit(){
        return this.$emit('done', this.form);
    },
    checkConsultation(){
        // let consultations = this.categories.filter(item => item.id == 1)[0].services;
        // consultations = [...consultations, ...this.categories.filter(item => item.id == 6)[0].services];
        // consultations = [...consultations, ...this.categories.filter(item => item.id == 7)[0].services];

        // let consultationIds = [];
        // consultations.forEach(e => {
        //     consultationIds.push(e.id);
        // });
        // let selectedConsultations = this.form.services.filter(item => consultationIds.includes(item));

        // if(!selectedConsultations.length)
        // {this.consultationSelected = false;}
        // else{ this.consultationSelected = true}

        this.consultationSelected = true
    },
    updateTotal(service, category){

        let serviceId = service.id;
        this.totalPayable = parseFloat(0);
        this.eachPayable = parseFloat(0);
        let cateServices = [];

        if(this.form.services?.includes(service.id)){
            this.form.services = this.form.services.filter(item => item !== serviceId);
            this.form.servicesFull = this.form.servicesFull.filter(item => item !== service);
        }else{
            category.services.forEach(e => {cateServices.push(e.id);});
            const toRemove = new Set(cateServices);
            this.form.services = this.form.services.filter((x) => !toRemove.has(x));


            if(this.form.services==null || this.form.services.length==0){
                this.form.services = [serviceId];
                this.form.servicesFull = [service];
            }else if(this.form.services.includes(serviceId)){
                this.form.services = this.form.services.filter(item => item !== serviceId);
                this.form.servicesFull = this.form.servicesFull.filter(item => item !== service);
            }else{
                this.form.services.push(serviceId);
                this.form.servicesFull.push(service);
            }

            this.form.servicesFull.forEach(e => {
                this.eachPayable+=parseFloat(e.price);
            });


        }
        console.log(this.form.services);
            this.addDependants();
            this.checkConsultation();

    },
    addDependants(){
        if(this.additionalMembers<0)
          this.additionalMembers=0;
          this.form.additionalMembers= this.additionalMembers;
        // console.log(this.additionalMembers);
        this.additionalMembers = parseFloat(this.additionalMembers);
        this.totalPayable=(this.additionalMembers+1)* this.eachPayable;
    }
  }
};
</script>

<style>
.disable-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: pointer;
}
.line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
}
</style>

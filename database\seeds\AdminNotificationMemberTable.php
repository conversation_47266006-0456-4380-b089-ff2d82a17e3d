<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AdminNotificationMemberTable extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

            $users = ['CM000220','CM000318','CM000348','CM002872','CM002873','CM002874','CM001284','CM002875','CM002367','CM002736','CM002737','CM002738','CM002739','CM002740','CM002741','CM002742','CM002743','CM002744','CM002745','CM002746','CM002747','CM002748','CM002749','CM002750','CM002751','CM002752','CM002753','CM002754','CM002755','CM002756','CM002757','CM002758','CM002759','CM002760','CM002761','CM002762','CM002763','CM002764','CM002765','CM002766','CM002767','CM002768','CM002769','CM002770','CM002771','CM002772','CM002773','CM002774','CM002775','CM002776','CM002777','CM002778','CM002779','CM002780','CM002781','CM002782','CM002783','CM002784','CM002785','CM002786','CM002787','CM002788','CM002789','CM002790','CM002791','CM002792','CM002793','CM002794','CM002795','CM002796','CM002797','CM002798','CM002799','CM002800','CM002801','CM002802','CM002803','CM002804','CM002805','CM002806','CM002807','CM002808','CM002809','CM002810','CM002811','CM002812','CM002813','CM002814','CM002815','CM002816','CM002817','CM002818','CM002819','CM002820','CM002821','CM002822','CM002823','CM002824','CM002825','CM002826','CM002827','CM002828','CM002829','CM002830','CM002831','CM002832','CM002833','CM002834','CM002835','CM002836','CM002837','CM002838','CM002839','CM002840','CM002841','CM002842','CM002843','CM002844','CM002845','CM002846','CM002847','CM002848','CM002849','CM002850','CM002851','CM002852','CM002853','CM002854','CM002855','CM002856','CM002857','CM002858','CM002859','CM002860','CM002861','CM002862','CM002863','CM002864','CM002865','CM002866','CM002867','CM002868','CM002869','CM002870','CM002871','CM000005'];

            foreach($users as $user){
                DB::table('members')->where('id', $user)->update([
                    'misc' =>   'send' ,
                ]);
            }

        //

        // dd($exists,$idexists);
    }
}

<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class Reminder1Notification extends Notification implements ShouldQueue
{
    use Queueable;

    public $amount;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($amount)
    {
        //
        $this->amount = $amount;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
        ->from(env("MAIL_USERNAME"), env("MAIL_FROM_NAME"))
        ->subject("Corp24 Payment Reminder")
        ->markdown(
            'mail.member.deposit',
            [
                'action' => "View Account",
                'link' => route("splash"),
                'message' => "

Dear member\n
Reminder of payment due. A payment of $".$this->amount." is still pending after funeral announcement made 72 hours ago.
We wish to highlight the urgency of this issue as per our constitution.
Please settle outstanding amount at your earliest.
We look forward to receiving your payment receipt in order to avoid suspension.
Please do not hesitate <NAME_EMAIL> with any further questions. \n
Best wishes
Admin Team
Medical Aid Association

",

            ]
        );
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}

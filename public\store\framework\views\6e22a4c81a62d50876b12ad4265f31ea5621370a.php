<?php $__env->startSection('page'); ?>
Sign In
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <!-- Register Thumbnail-->
      <div class="d-none d-lg-block col-12 col-lg-6">
        <div class="register-thumbnail mb-50 text-center">
            <div class="mx-auto" style="width: 66%;">
              <img src="<?php echo e(asset('storage/bg/login.png')); ?>" alt="">
            </div>
        </div>
      </div>
      <!-- Register Card-->
      <div class="col-12 col-lg-6">
       <?php if($errors->any()): ?>
        <div class="alert alert-danger" role="alert">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <?php endif; ?>
        <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
          <div class="card-body">
            <h4>Welcome Back!</h4>
            <!-- Register Form-->
            <div class="register-form mt-3 mb-2">
              <form action="<?php echo e(route('login')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="form-group">
                  <input name="email" class="form-control rounded-0" type="email" placeholder="Email Address" required>
                </div>
                <div class="form-group">
                  <label class="label-psswd" for="password">
                    <span class="hide">HIDE</span>
                    <span class="show">SHOW</span>
                  </label>
                  <input name="password" class="input-psswd form-control rounded-0" id="password" type="password" placeholder="Password"
                    psswd-shown="false" required>
                </div>
                <a class="btn radix-btn white-btn w-40" href="/join-now" type="submit">
                  <i class="lni-user mr-2"></i>
                  Register</a>
                  <button class="btn radix-btn white-btn w-40" type="submit">
                  <i class="lni-unlock mr-2"></i>
                  Login</button>
              </form>
              <div class="login-meta-data d-flex align-items-center justify-content-between">
                <div class="custom-control custom-checkbox mt-3 mr-sm-2">
                  <input class="custom-control-input" id="rememberMe" type="checkbox">
                  <label class="custom-control-label" for="rememberMe">Keep me logged in</label>
                </div>
                <a class="forgot-password mt-3" href="<?php echo e(route('forgot.password')); ?>">Forgot Password?</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/auth/login.blade.php ENDPATH**/ ?>
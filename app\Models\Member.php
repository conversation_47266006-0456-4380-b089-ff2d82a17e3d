<?php

namespace App\Models;

use App\Notifications\PaymentCompleted;
use App\Service;
use App\User;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;

class Member extends Model
{
    public $incrementing = false;

    use Notifiable;
    use SoftDeletes;
    protected $dates = ['deleted_at'];
    protected $fillable = [
        "id",
        "user_id",
        "balance",
        "gov_id",
        "email",
        "branch",
        "first_name",
        "middle_names",
        "last_name",
        "ambassador_id",
        "dob",
        "city",
        "country",
        "street",
        "zip",
        "gender",
        "phone",
        "nok_city",
        "nok_country",
        "nok_dob",
        "nok_email",
        "nok_full_name",
        "nok_phone",
        "nok_street",
        "nok_zip",
        "created_at",
        "orderID",
        "bio"
    ];

    protected $casts = [
        'dob' => 'date',
        'nok_dob' => 'date',
    ];


    public function branch(){
        return $this->belongsTo(related: Branch::class);
    }
    public function ambassador(){
        return $this->belongsTo(related: Ambassador::class);
    }

    public function toDeceased(){
        $decea = $this->toArray();
        DeceasedMember::create($decea);
        $this->delete();
    }
    //Creating Membership ID

    public static function boot()
    {
        parent::boot();
        static::deleting(function ($member){
            $term = $member->toArray();
            $is_deceased = DeceasedMember::find($member->id);
            $is_terminated = TerminatedMember::find($member->id);
            if(!isset($is_deceased) && !isset($is_terminated)){
                TerminatedMember::create($term);
                $member->nominees()->delete();
            }
            $member->deleted_at= date("Y-m-d H:i:s");
            // $member->user->delete();
            logger("Member was deleted", [$member]);
        });
        static::creating(function ($model) {
            $f_name = $model->first_name[0] ?? '';
            $l_name = $model->last_name[0] ?? '';

            $prefix = strtoupper($l_name . $f_name);
            $prefix2 = "M";

            $counter = Member::count();
            $padded = 0;
            $member_id = null;
            do {
                $counter++;
                $padded = str_pad($counter, 4, '0', STR_PAD_LEFT);
                $member_id = $prefix2 . $padded;
            } while (Member::withTrashed()->find($member_id) != null);
            logger("Creating Member ID: {$member_id}");
            $model->id = $member_id;
        });
    }

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function nominees(){
        return $this->hasMany(Nominee::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    public function deposits(){
        return $this->hasMany(Deposit::class);
    }

    public function invoices(){
        return $this->hasMany(Invoice::class);
    }

    public function services(){
        return $this->belongsToMany(Service::class);
    }

    public function boardMember(){
        return $this->hasOne(BoardMember::class);
    }


    public function getNextOfKin(){
        return (object) [
            "city" => $this->nok_city,
            "country" => $this->nok_country,
            "dob" => $this->nok_dob,
            "email" => $this->nok_email,
            "full_name" => $this->nok_full_name,
            "phone" => $this->nok_phone,
            "street" => $this->nok_street,
            "zip" => $this->nok_zip
        ];
    }



    public function getDonatedAmountAttribute(){
        $donations = Payment::whereMemberId($this->id)->get();
        return $donations->sum('amount');
    }

    public function getDonatedCountAttribute(){
        return Payment::whereMemberId($this->id)->count();
    }

    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->middle_names} {$this->last_name}";
    }

    protected function _gender(){
        if(isset($this->gender)){
            return $this->gender === "m" ? "Male" : "Female";
        };
    }

    public function getGenderBrowseAttribute()
    {
        return $this->_gender();
    }

    public function getGenderReadAttribute()
    {
        return $this->_gender();
    }

    public function getNomineesBrowseAttribute()
    {
        return json_encode($this->nominees);
    }

    public function getNomineesReadAttribute()
    {
        return json_encode($this->nominees);
    }

    public function getNomineesEditAttribute()
    {
        return json_encode($this->nominees);
    }

    public function memberDeposit($paidAmount,$paymentRef, $paymentMethod, $description){
        if($paidAmount>0){
            $this->user->depositFloat($paidAmount,  ['description' => $description]);
        }else if($paidAmount<0) {
            $this->user->forceWithdrawFloat(-1*$paidAmount, ['description' => $description]);
        }

        try{
            $this->deposits()->create([
                "amount" => $paidAmount,
                "payment_ref" => $paymentRef,
                "date" => now(),
                "balance" => $this->user->balanceFloat,
                "type" => $paymentMethod,
                "description" => $description
            ]);
            $this->payInvoices();
        }catch(Exception $e){
            logger("An error occured while attempting to record a direct deposit by admin. The deposit was however successfull: ".$e->getMessage());
        }

    }
    public function processMemberJoiningPaymentAndNotify(Invoice $invoice,float $amount, String $ref, String $type){
        $this->payments()->create([
            "invoice_id" => $invoice->id,
            "payment_ref" => $ref,
            "type" => $type,
            "amount" => $amount,
            "description" => $invoice->description,
            "on" => now()
        ]);

        $invoice->status = "paid";
        $invoice->save();

        try{
            $this->notify(new PaymentCompleted($invoice->id, "0000"));
            $this->update(["welcome_send" => true]);
        }catch(Exception $e){
            logger('Failed to notify member about new account created. '. $e->getMessage());
        }

        return $invoice;
    }


    public function payInvoices(){
        if ($this->user->balanceFloat >= 0) {
            $invoices = Invoice::whereMemberIdAndStatus($this->member_id, "unpaid")->get();
            for ($x = 0; $x <= $invoices->count() - 1; $x++) {
                $this->user->memberDetails->payments()->create([
                    "invoice_id" => $invoices[$x]->id,
                    "payment_ref" => "Wallet payment",
                    "amount" => $invoices[$x]->total,
                    "description" => $invoices[$x]->description,
                    "on" => now()
                ]);
                $invoices[$x]->status = "paid";
                $invoices[$x]->save();
            }
        }
    }



}

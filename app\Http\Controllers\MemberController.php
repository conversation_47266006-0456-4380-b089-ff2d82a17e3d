<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Member;
use App\Models\Applicant;
use App\Models\Nominee;
use App\Services;
use App\Models\Obituary;
use App\Notifications\PaymentCompleted;
use App\Notifications\TerminationNotification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use App\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;

class MemberController extends SharedBaseController
{

    public function index(){
        $members_count = count(Member::all());
        // $obituaries = Obituary::orderBy('created_at', 'desc')
        //     ->limit(3)
        //     ->get();
        $member = Auth::user()->memberDetails;
        $user = User::find(Auth::user()->id);
        $admin = new Collection([]);
        $payment = new Collection([]);
        $obituary = new Collection([]);
        $services  = new Collection([]);
        if (isset($member)) {
            $services = $member->services;
            $all = $member->notifications;
            $admin = $this->filterType($all, "admin");
            $payment = $this->filterType($all, "payment");
            $obituary = $this->filterType($all, "obituary");
        }

        return view('member.index',
        compact('members_count',
            'admin',
            'payment',
            'obituary',
            'services'));
    }

    private function filterType($notifications, $type)
    {
        return $notifications->filter(function ($notification) use ($type) {
            return notification_type($notification->type) === $type;
        });
    }

    /**
     * Copies the applicant data to a new table {members}
     * and create new user
     */
    public function completeRegistration(Request $request){

    }
    public function store(Request $request)
    {
        return $request->all();
    }

    public function delete(Member $member)
    {

        $member->delete();
        Notification::route('mail', $member->email)->notify(new TerminationNotification($member));

        return back()->with([
            'message'    => 'Successfully deleted member',
            'alert-type' => 'success',
        ]);
    }
    public function findById( $member)
    {
        $mem =  Member::find($member);

        if(!isset($mem))
           return response()->json(['message' => 'Member does not exist'], 404);


        $data = [
            "id"=> $mem->id,
            "email"=> $mem->email,
            "first_name"=> $mem->first_name,
            "last_name"=> $mem->last_name
        ];
        return response()->json($data);
    }


        /**
    * @return Member
    */
    public function createPaidMember(Invoice $invoice, float $paidAmount, string $paymentRef, string $paymentMemthod){
        $applicant = Applicant::find($invoice->applicant_id);
        $user = User::create([
            'name' => $applicant->full_name,
            'email' =>  $applicant->email ? strtolower($applicant->email): null,
            'avatar'    => 'users/default.png',
            'password'  =>  bcrypt("passwordklasx")
        ]);

        $user->markEmailAsVerified();
        $member = $user->memberDetails()->create($applicant->toArray());
        $nominees = [];

        foreach ($applicant->nominees??[] as $key => $nominee) {
            array_push($nominees, new Nominee($nominee));
        }

        try {
            $member->services()->attach($applicant->services);
        } catch (\Throwable $th) {
            logger($th);
        }

        try {
            $member->nominees()->saveMany($nominees);
        } catch (\Throwable $th) {
            logger($th);
        }

        $member->processMemberJoiningPaymentAndNotify($invoice,$paidAmount,  $paymentRef, $paymentMemthod);

        return $member;
    }
}

<?php $__env->startSection('page'); ?>
Sign In
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
      <!-- Register Thumbnail-->
      <div class="d-none d-lg-block col-12 col-lg-6">
        <div class="register-thumbnail mb-50">
        <div class="mx-auto" style="width: 66%;">
                <img src="<?php echo e(asset('storage/bg/login.png')); ?>" alt="">
            </div>
        </div>
      </div>
      <!-- Register Card-->
      <div class="col-12 col-lg-6">
       <?php if($errors->any()): ?>
        <div class="alert alert-danger" role="alert">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
        <?php endif; ?>
        <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
          <div class="card-body">
            <h5>Subscription Payment </h5>

            <form id="redirectForm">
                <div class="  py-3 mr-sm-2">
                    <label class=" " for="const">
                        Enter Membership Number
                    </label>
                    <input class="form-control" type="text" id="urlInput" />
                </div>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button type="submit"
                            class="nav-link btn radix-btn white-btn  w-100" >
                            <i class="nc-icon nc-share-66"></i>
                            Proceed</button>
                    </li>
                </ul>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/pages/subscriptionpayment.blade.php ENDPATH**/ ?>
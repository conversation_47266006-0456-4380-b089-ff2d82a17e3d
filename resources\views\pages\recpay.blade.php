@extends('master')

@section('page')
Sign In
@endsection

@section('content')
<div class="register-area section-padding-120-70">
  <div class="container">
    <div class="row align-items-center justify-content-between">
        <!-- Register Thumbnail-->
        <div class="d-none d-lg-block col-12 col-lg-6">
            <div class="register-thumbnail mb-50">
                <div class="mx-auto" style="width: 66%;">
                    <img src="{{'/'.'storage/' .str_replace("\\", "/", setting('site.home_image'))}}" alt="">
                </div>
            </div>
        </div>
        <!-- Register Card-->
        <div class="col-12 col-lg-6">
        @if ($errors->any())
            <div class="alert alert-danger" role="alert">
                <ul>
                    @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
            <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
            <div class="card-body">
                <h5>Failed</h5>

                <form id="redirectForm">
                <div class="  py-3 mr-sm-2">
                    <label class=" " for="const">
                    Payment Failed
                    </label>
                </div>
                </form>
            </div>
            </div>
        @else
            <div class="card register-card bg-gray p-1 p-sm-4 mb-50">
                <div class="card-body">
                    <h5>Success</h5>

                    <form id="redirectForm">
                    <div class="  py-3 mr-sm-2">
                        <label class=" " for="const">
                        Payment Received Check Your Email
                        </label>
                        <p>Didn't receive confirmation email? <a href="/contact-us">Get in touch</a></p>
                    </div>
                    </form>
                </div>
            </div>
        @endif



        </div>
    </div>
  </div>
</div>
@endsection

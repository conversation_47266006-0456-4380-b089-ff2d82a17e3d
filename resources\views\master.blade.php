<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="Medical Aid is a benevolent based community organisation formed to help Zimbabweans.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>@yield('page') - {{ setting('site.title') }}</title>
    <link rel="shortcut icon" href="{{asset('storage/settings/May2020/oRWDPZmmv riKw5nuB6Bs.png')}}" type="image/png">
    <link rel="stylesheet" href="{{ asset('css/libs/theme.css?v=ogf') }}">
    <link href="{{ asset('css/app.css?v=0.'.time()) }}" rel="stylesheet">
    <style type="text/css">
        :root {
        --base: {{setting('site.main_color')}} !important;
        --primary: {{setting('site.primary_color')}} !important;
        --secondary: #fdd76e;
        }
    </style>
</head>
<body>
    <!-- <div class="preloader" id="preloader">
      <div class="spinner-grow text-light" role="status"><span class="sr-only">Loading...</span></div>
    </div> -->
    <div id="corp24-app">
        @guest
            <guest-nav
                :logo_link="'{{'storage/' .str_replace("\\", "/", setting('site.logo'))}}'"
                :about_us="'{{route('about.us')}}'"
                :contact_us="'{{route('contact.us')}}'"
                :login="'{{route('login')}}'"></guest-nav>
        @endguest

        @yield('content')
        <new-message :level="'{{session('level') ?? 'primary'}}'" :message="'{{session('message') ?? null}}'">
        </new-message>
        <cookie-view></cookie-view>
        <!-- <small-footer

        ></small-footer> -->
        <loaded-view></loaded-view>
    </div>
    <script src="{{ asset('js/corp24.app.js?v=0.'.time()) }}"></script>
    <script>
        document.getElementById("redirectForm")?.addEventListener("submit", function (event) {
            event.preventDefault();
            const url = document.getElementById("urlInput").value;
            console.log("/subscription-payment/"+url);
            window.location.href = "/subscription-payment/"+url;
        });
    </script>
</body>
</html>

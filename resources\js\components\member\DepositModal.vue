<template>
  <div
    class="modal fade"
    id="depositModal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="depositModalTitle"
    aria-hidden="true"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="depositModalTitle">Deposit Funds</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="col-12 mb-2">
            <label for="_amount">Amount</label>
             <p>Please note payment will include additional transaction charges that are not part of the deposit.</p>
            <div class="input-group mb-3">
            </div>
          </div>
          <h5 v-if="open" class="h6">Deposit method:</h5>
          <a class="btn btn-success w-100" :href="paynowUrl">
            Paynow
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['route', 'user', 'client_id'],
  mounted() {
    console.log(this.user, this.client_id);

    $("#depositModal").on("show.bs.modal", e => {
      if(!this.open){
        const scr = document.createElement("script");
        scr.src =
          `https://www.paypal.com/sdk/js?client-id=${this.client_id}&currency=GBP`;
        scr.addEventListener("load", this.loadPayPal);
        scr.addEventListener("error", this.loadError);
        document.body.append(scr);
        this.script = scr;
      }
    });
    $('#depositModal').on('hidden.bs.modal', (e) => {
        if(this.error){
          this.error = false;
          $(this.script).remove();
          this.load = false;
        }
    });
  }
  ,
  data() {
    return {
      open: false,
      error: false,
      amount: null,
      paynowUrl: "#",
      payment_ref: null,
      script: null
    };
  },

  methods: {
    finalise(payment_ref){
      console.log(payment_ref);
      $.notify(
        {
          icon: "nc-icon nc-check-2",
          message: "Deposited Successfully. Please wait.."
        },
        {
          type: "primary",
          timer: 5000,
          placement: {
            from: "top",
            align: "right"
          }
        }
      );
      this.payment_ref = payment_ref;
      setTimeout(() => this.$refs.form.submit(), 80);
    },
    loadError(){
      this.error = true;
    },
    loadPayPal(e){
      this.open = true;
      console.log(e, "Loaded Successfully");
      if (typeof paypal === "undefined") {
        this.error = true;
        return;
      }
      paypal
        .Buttons({
          createOrder: (data, action) => {

            console.log(this.amount);

            console.log(this.amount);
            this.amount = parseFloat(this.amount);
            this.amount = (this.amount);
            this.amount = this.amount.toFixed(2);

            this.amount1 = (this.amount/0.971)+0.31;
            this.amount1 = this.amount1.toFixed(2);

            console.log("Paypal payment of: ", this.amount);
            console.log(data);
            console.log(action);

            return action.order.create({
              application_context: {
                brand_name: "Funeral Cover Association",
                user_action: "PAY_NOW",
                shipping_preference: "NO_SHIPPING"
              },
              purchase_units: [
                {
                    description: `Funds Deposit for user: ${this.user.id} (${this.user.name})`,
                    amount: {
                        currency_code: "GBP",
                        value: this.amount1
                  }
                }
              ]
            });
          },
          onApprove: (data, actions) => {
            return actions.order.capture().then(details => {
              if (details.status == "COMPLETED") {
                this.orderID = data.orderID;
                this.finalise(data.orderID);
              } else {
                $.notify({
                    icon: "nc-icon nc-simple-remove",
                    message: `Error saving your payment, contact support with ID: ${data.orderID}`,
                  },
                  {
                    type: "danger",
                    timer: 8000,
                    placement: {
                      from: "top",
                      align: "right"
                    }
                  }
                );
              }
            });
          }
        })
        .render(this.$refs.paypal);
    }
  }
};
</script>

<style>
</style>

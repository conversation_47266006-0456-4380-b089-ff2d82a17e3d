<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <title>Apply - <?php echo e(setting('site.title')); ?></title>
    <link rel="stylesheet" href="<?php echo e(asset('css/libs/theme.css?v=ogf')); ?>">
    <link href="<?php echo e(asset('css/app.css?v=0.'.time())); ?>" rel="stylesheet">
</head>

<body>
    <!-- <div class="preloader" id="preloader">
        <div class="spinner-grow text-light" role="status"><span class="sr-only">Loading...</span></div>
    </div> -->
    <div id="corp24-app">
        <header class="header-area header2">
            <div class="container">
                <div class="classy-nav-container breakpoint-off">
                    <nav class="classy-navbar navbar2 justify-content-between" id="radixNav">
                        <a class="nav-brand mr-5" href="/">
                            <img src="<?php echo e(asset('storage/img/logo.png')); ?>" alt />
                        </a>
                        <div class="login-btn-area">
                            <a href="/subscription-payment" style="    padding-right: 13px;">Payment</a>
                            <a class="btn radix-btn white-btn btn-sm" href="<?php echo e(route('login')); ?>">Login</a>
                            <a class="btn d-none d-sm-inline-block radix-btn white-btn btn-sm" href="<?php echo e(route('apply')); ?>">Register</a>
                        </div>
                    </nav>
                </div>
            </div>
        </header>
        <div class=" white-bg-breadcrumb" style="height: 50px">
            <div class="container h-100">
                <!-- <div class="row h-100 align-items-center">
                    <div class="col-12">
                        <div class="breadcrumb-content pb-4 mb-4">
                            <h2 class="breadcrumb-title">Join Now</h2>
                        <small class="text-center">

                        By continuing you are agreeing that, you have read and understood the
                        <a href="#">Funeral Cover Association's Terms and Conditions</a>
                        and you are ready to become a member.

</small>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>
        <application-form :max_nominees="<?php echo e(setting('member.max_nominees', 4)); ?>" :categories="<?php echo e($categories); ?>"></application-form>
        <small-footer
            :about_us="'<?php echo e(route('about.us')); ?>'"
            :contact_us="'<?php echo e(route('contact.us')); ?>'"
            :login="'<?php echo e(route('login')); ?>'"
        ></small-footer>
        <loaded-view></loaded-view>
    </div>
    <script src="<?php echo e(asset('js/corp24.app.js?v=0.'.time())); ?>"></script>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/auth/apply.blade.php ENDPATH**/ ?>

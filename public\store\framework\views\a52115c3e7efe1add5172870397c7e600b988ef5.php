<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="Funeral Cover is a benevolent based community organisation formed to help Zimbabweans.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><?php echo $__env->yieldContent('page'); ?> - <?php echo e(setting('site.title')); ?></title>
    <link rel="shortcut icon" href="<?php echo e(asset('storage/settings/May2020/oRWDPZmmvriKw5nuB6Bs.png')); ?>" type="image/png">
    <link rel="stylesheet" href="<?php echo e(asset('css/libs/theme.css?v=ogf')); ?>">
    <link href="<?php echo e(asset('css/app.css?v=0.'.time())); ?>" rel="stylesheet">
</head>
<body>
    <!-- <div class="preloader" id="preloader">
      <div class="spinner-grow text-light" role="status"><span class="sr-only">Loading...</span></div>
    </div> -->
    <div id="corp24-app">
        <?php if(auth()->guard()->guest()): ?>
            <guest-nav
            :logo_link="'{{setting('site.logo')}}'"
                :about_us="'<?php echo e(route('about.us')); ?>'"
                :contact_us="'<?php echo e(route('contact.us')); ?>'"
                :login="'<?php echo e(route('login')); ?>'"></guest-nav>
        <?php endif; ?>

        <?php echo $__env->yieldContent('content'); ?>
        <new-message :level="'<?php echo e(session('level') ?? 'primary'); ?>'" :message="'<?php echo e(session('message') ?? null); ?>'">
        </new-message>
        <cookie-view></cookie-view>
        <small-footer

        ></small-footer>
        <loaded-view></loaded-view>
    </div>
    <script src="<?php echo e(asset('js/corp24.app.js?v=0.'.time())); ?>"></script>
    <script>
        document.getElementById("redirectForm")?.addEventListener("submit", function (event) {
            event.preventDefault();
            const url = document.getElementById("urlInput").value;
            console.log("/subscription-payment/"+url);
            window.location.href = "/subscription-payment/"+url;
        });
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\corp24medicalaid\resources\views/master.blade.php ENDPATH**/ ?>

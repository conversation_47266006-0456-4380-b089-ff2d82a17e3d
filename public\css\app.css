:root {
  --base: var(--base);
  --primary: #ae2227;
  --secondary: #fdd76e;
}

/* Preloader CSS */

#preloader {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 9999999;
  top: 0;
  left: 0;
  background-color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.account-active {
  border-left: 3px solid var(--primary);
}

.account-active a {
  color: var(--primary);
}

.vdp-datepicker .form-control[readonly]:not(.nom) {
  background-color: #f5f5ff;
  opacity: 1;
}

.breadcrumb--area {
  height: 250px;
}

.nav-brand img {
  height: 40px !important;
}

.complete-reg-form {
  max-width: 400px;
}

.sidebar[data-color=primary]:after,
.off-canvas-sidebar[data-color=primary]:after {
  background: var(--base);
}

.sidebar[data-active-color=primary] .nav li.active > a,
.sidebar[data-active-color=primary] .nav li.active > a i,
.sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse],
.sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] i,
.sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] ~ div > ul > li.active .sidebar-mini-icon,
.sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] ~ div > ul > li.active > a,
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a,
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a i,
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse],
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] i,
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] ~ div > ul > li.active .sidebar-mini-icon,
.off-canvas-sidebar[data-active-color=primary] .nav li.active > a[data-toggle=collapse] ~ div > ul > li.active > a {
  color: var(--secondary);
  opacity: 1;
}

.sidebar .nav li > a,
.off-canvas-sidebar .nav li > a {
  text-transform: unset;
  font-size: 14px;
}

.counter {
  position: absolute;
  color: var(--light);
  background: var(--danger);
  border-radius: 100%;
  bottom: 18px;
  left: 20px;
  height: 25px;
}

.radix-btn.btn-sm {
  color: var(--light);
  background-color: var(--primary);
}

.card-title {
  margin-top: 0;
}

.obituary-img {
  height: 250px;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-size: cover !important;
  border-radius: 10px 10px 0 0;
}

.apply-form-nav ul {
  border-left: 3px solid rgba(11, 7, 87, 0.3);
}

.apply-form-nav ul li {
  position: relative;
}

.apply-form-nav ul li a::before {
  content: "";
  width: 3px;
  height: 31px;
  position: absolute;
  left: -3px;
  top: 0;
}

.apply-form-nav ul li a.active-step {
  margin-left: 10px;
  color: var(--primary);
  font-weight: bold;
}

.apply-form-nav ul li a.active-step::before {
  background: var(--primary);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.close:active {
  outline: none;
}


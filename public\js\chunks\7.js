(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[7],{

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Loaded.vue?vue&type=script&lang=js&":
/*!*****************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/Loaded.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/**
 * Load all theme scripts
 * after all elements are loaded
 */
window.WOW = __webpack_require__(/*! ../libs/wow.min */ "./resources/js/libs/wow.min.js").WOW;
__webpack_require__(/*! ../libs/bootstrap.min */ "./resources/js/libs/bootstrap.min.js");
__webpack_require__(/*! ../libs/default/classy-nav.min */ "./resources/js/libs/default/classy-nav.min.js");
__webpack_require__(/*! ../libs/waypoints.min */ "./resources/js/libs/waypoints.min.js");
__webpack_require__(/*! ../libs/default/jquery.scrollup.min */ "./resources/js/libs/default/jquery.scrollup.min.js");
__webpack_require__(/*! ../libs/owl.carousel.min.js */ "./resources/js/libs/owl.carousel.min.js");
__webpack_require__(/*! ../libs/imagesloaded.pkgd.min */ "./resources/js/libs/imagesloaded.pkgd.min.js");
__webpack_require__(/*! ../libs/default/isotope.pkgd.min */ "./resources/js/libs/default/isotope.pkgd.min.js");
__webpack_require__(/*! ../libs/jquery.magnific-popup.min */ "./resources/js/libs/jquery.magnific-popup.min.js");
__webpack_require__(/*! ../libs/jquery.animatedheadline.min */ "./resources/js/libs/jquery.animatedheadline.min.js");
__webpack_require__(/*! ../libs/jquery.counterup.min */ "./resources/js/libs/jquery.counterup.min.js");
__webpack_require__(/*! ../libs/jarallax.min */ "./resources/js/libs/jarallax.min.js");
__webpack_require__(/*! ../libs/jarallax-video.min */ "./resources/js/libs/jarallax-video.min.js");
__webpack_require__(/*! ../libs/default/cookiealert */ "./resources/js/libs/default/cookiealert.js");
__webpack_require__(/*! ../libs/default/jquery.passwordstrength */ "./resources/js/libs/default/jquery.passwordstrength.js");
__webpack_require__(/*! ../libs/default/mail */ "./resources/js/libs/default/mail.js");
__webpack_require__(/*! ../libs/default/active */ "./resources/js/libs/default/active.js");
/* harmony default export */ __webpack_exports__["default"] = ({});

/***/ }),

/***/ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0&":
/*!***************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib??ref--4-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!./node_modules/vue-loader/lib??vue-loader-options!./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0& ***!
  \***************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div");
};
var staticRenderFns = [];
render._withStripped = true;


/***/ }),

/***/ "./resources/js/components/Loaded.vue":
/*!********************************************!*\
  !*** ./resources/js/components/Loaded.vue ***!
  \********************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Loaded.vue?vue&type=template&id=e92958a0& */ "./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0&");
/* harmony import */ var _Loaded_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Loaded.vue?vue&type=script&lang=js& */ "./resources/js/components/Loaded.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport *//* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js */ "./node_modules/vue-loader/lib/runtime/componentNormalizer.js");





/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_2__["default"])(
  _Loaded_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__["render"],
  _Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null
  
)

/* hot reload */
if (false) { var api; }
component.options.__file = "resources/js/components/Loaded.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ "./resources/js/components/Loaded.vue?vue&type=script&lang=js&":
/*!*********************************************************************!*\
  !*** ./resources/js/components/Loaded.vue?vue&type=script&lang=js& ***!
  \*********************************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Loaded_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib??ref--4-0!../../../node_modules/vue-loader/lib??vue-loader-options!./Loaded.vue?vue&type=script&lang=js& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Loaded.vue?vue&type=script&lang=js&");
/* empty/unused harmony star reexport */ /* harmony default export */ __webpack_exports__["default"] = (_node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_index_js_vue_loader_options_Loaded_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__["default"]); 

/***/ }),

/***/ "./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0&":
/*!***************************************************************************!*\
  !*** ./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0& ***!
  \***************************************************************************/
/*! exports provided: render, staticRenderFns */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../node_modules/babel-loader/lib??ref--4-0!../../../node_modules/vue-loader/lib/loaders/templateLoader.js??ref--6!../../../node_modules/vue-loader/lib??vue-loader-options!./Loaded.vue?vue&type=template&id=e92958a0& */ "./node_modules/babel-loader/lib/index.js?!./node_modules/vue-loader/lib/loaders/templateLoader.js?!./node_modules/vue-loader/lib/index.js?!./resources/js/components/Loaded.vue?vue&type=template&id=e92958a0&");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _node_modules_babel_loader_lib_index_js_ref_4_0_node_modules_vue_loader_lib_loaders_templateLoader_js_ref_6_node_modules_vue_loader_lib_index_js_vue_loader_options_Loaded_vue_vue_type_template_id_e92958a0___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });



/***/ }),

/***/ "./resources/js/libs/default/active.js":
/*!*********************************************!*\
  !*** ./resources/js/libs/default/active.js ***!
  \*********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

(function ($) {
  'use strict';

  var radixWindow = $(window);

  // :: Preloader Active Code
  radixWindow.on('load', function () {
    $('#preloader').fadeOut('1000', function () {
      $(this).remove();
    });
  });

  // :: Classy Nav Active Code
  if ($.fn.classyNav) {
    $('#radixNav').classyNav();
  }

  // :: Sticky Active Code
  radixWindow.on('scroll', function () {
    if (radixWindow.scrollTop() > 0) {
      $('.header-area').addClass('sticky');
    } else {
      $('.header-area').removeClass('sticky');
    }
  });

  // :: Welcome Slides Active Code
  if ($.fn.owlCarousel) {
    var welcomeSlider = $('.hero-slides');
    welcomeSlider.owlCarousel({
      items: 1,
      loop: true,
      autoplay: true,
      dots: false,
      nav: true,
      navText: ['<i class="lni-chevron-left"></i>', '<i class="lni-chevron-right"></i>']
    });
    welcomeSlider.on('translate.owl.carousel', function () {
      var layer = $("[data-animation]");
      layer.each(function () {
        var anim_name = $(this).data('animation');
        $(this).removeClass('animated ' + anim_name).css('opacity', '0');
      });
    });
    $("[data-delay]").each(function () {
      var anim_del = $(this).data('delay');
      $(this).css('animation-delay', anim_del);
    });
    $("[data-duration]").each(function () {
      var anim_dur = $(this).data('duration');
      $(this).css('animation-duration', anim_dur);
    });
    welcomeSlider.on('translated.owl.carousel', function () {
      var layer = welcomeSlider.find('.owl-item.active').find("[data-animation]");
      layer.each(function () {
        var anim_name = $(this).data('animation');
        $(this).addClass('animated ' + anim_name).css('opacity', '1');
      });
    });
  }

  // :: Testimonial Slides Active Code
  if ($.fn.owlCarousel) {
    var testimonialSlider = $('.client-feedback-slides');
    testimonialSlider.owlCarousel({
      items: 3,
      margin: 40,
      loop: true,
      autoplay: true,
      smartSpeed: 800,
      autoplayTimeout: 5000,
      dots: true,
      nav: false,
      responsive: {
        0: {
          items: 1,
          margin: 0
        },
        576: {
          items: 2,
          margin: 15
        },
        768: {
          items: 1
        },
        992: {
          items: 2
        },
        1700: {
          items: 3
        }
      }
    });
  }

  // :: Partners Active Code
  if ($.fn.owlCarousel) {
    var partnerSlider = $('.our-partner-slides');
    partnerSlider.owlCarousel({
      items: 6,
      margin: 50,
      loop: true,
      autoplay: true,
      smartSpeed: 800,
      autoplayTimeout: 5000,
      dots: false,
      nav: false,
      responsive: {
        0: {
          items: 2
        },
        480: {
          items: 3
        },
        768: {
          items: 4
        },
        992: {
          items: 5
        },
        1200: {
          items: 6
        }
      }
    });
  }

  // :: Portfolio Active Code
  if ($.fn.owlCarousel) {
    var portSlides = $('.portfolio-slides');
    portSlides.owlCarousel({
      items: 4,
      margin: 10,
      loop: true,
      autoplay: true,
      smartSpeed: 800,
      autoplayTimeout: 5000,
      dots: false,
      nav: false,
      responsive: {
        0: {
          items: 1
        },
        576: {
          items: 2
        },
        992: {
          items: 3
        },
        1200: {
          items: 4
        }
      }
    });
  }

  // :: Portfolio Active Code
  if ($.fn.owlCarousel) {
    var portSlides2 = $('.portfolio-slides-2');
    portSlides2.owlCarousel({
      items: 3,
      margin: 10,
      loop: true,
      autoplay: true,
      smartSpeed: 800,
      autoplayTimeout: 5000,
      dots: false,
      nav: true,
      navText: ['<i class="lni-chevron-left"></i>', '<i class="lni-chevron-right"></i>'],
      responsive: {
        0: {
          items: 1
        },
        576: {
          items: 2
        },
        992: {
          items: 3
        }
      }
    });
  }

  // :: Related Slides Active Code
  if ($.fn.owlCarousel) {
    var relatedProdSlides = $('.related-image-carousel');
    relatedProdSlides.owlCarousel({
      items: 4,
      margin: 15,
      loop: true,
      autoplay: true,
      smartSpeed: 500,
      autoplayTimeout: 5000,
      dots: false,
      nav: true,
      navText: ['<i class="lni-chevron-left"></i>', '<i class="lni-chevron-right"></i>']
    });
  }

  // :: Related Slides Active Code
  if ($.fn.owlCarousel) {
    var videoCardSlides = $('.video-card-slides');
    videoCardSlides.owlCarousel({
      items: 1,
      loop: true,
      autoplay: true,
      smartSpeed: 500,
      autoplayTimeout: 5000,
      dots: false,
      nav: false
    });
  }

  // :: Related Slides Active Code
  if ($.fn.owlCarousel) {
    var relatedProjectSlides = $('.related-project-slide');
    relatedProjectSlides.owlCarousel({
      items: 3,
      margin: 30,
      loop: true,
      autoplay: true,
      smartSpeed: 500,
      autoplayTimeout: 5000,
      dots: false,
      nav: true,
      navText: ['<i class="lni-chevron-left"></i>', '<i class="lni-chevron-right"></i>'],
      responsive: {
        0: {
          items: 1
        },
        768: {
          items: 2
        },
        992: {
          items: 3
        }
      }
    });
  }

  // :: Project Details Slide Active Code
  if ($.fn.owlCarousel) {
    var projectDetailsShotSlide = $('.project-details-shots-slide');
    projectDetailsShotSlide.owlCarousel({
      items: 1,
      margin: 0,
      loop: true,
      autoplay: true,
      smartSpeed: 500,
      autoplayTimeout: 5000,
      dots: false,
      nav: true,
      navText: ['<i class="lni-chevron-left"></i>', '<i class="lni-chevron-right"></i>']
    });
  }

  // :: Masonary Gallery Active Code
  if ($.fn.imagesLoaded) {
    $('.radix-portfolio-filter').imagesLoaded(function () {
      // filter items on button click
      $('.portfolio-menu').on('click', 'button', function () {
        var filterValue = $(this).attr('data-filter');
        $grid.isotope({
          filter: filterValue
        });
      });

      // init Isotope
      var $grid = $('.radix-portfolio-filter').isotope({
        itemSelector: '.single-portfolio-item',
        percentPosition: true,
        masonry: {
          columnWidth: '.single-portfolio-item'
        }
      });
    });
  }

  // :: Gallery Menu Style Code
  $('.portfolio-menu button.btn').on('click', function () {
    $('.portfolio-menu button.btn').removeClass('active');
    $(this).addClass('active');
  });

  // :: Magnific Popup Active Code
  if ($.fn.magnificPopup) {
    $('.video-play-btn').magnificPopup({
      type: 'iframe'
    });
  }
  if ($.fn.magnificPopup) {
    $('.image-popup').magnificPopup({
      type: 'image',
      gallery: {
        enabled: true
      },
      removalDelay: 500,
      mainClass: 'mfp-fade',
      preloader: true,
      callbacks: {
        beforeOpen: function beforeOpen() {
          this.st.image.markup = this.st.image.markup.replace('mfp-figure', 'mfp-figure mfp-with-anim');
          this.st.mainClass = this.st.el.attr('data-effect');
        }
      },
      closeOnContentClick: true,
      midClick: true
    });
  }

  // :: Tooltip Active Code
  if ($.fn.tooltip) {
    $('[data-toggle="tooltip"]').tooltip();
  }

  // :: WOW Active Code
  if (radixWindow.width() > 480) {
    new WOW({
      live: false
    }).init();
  }

  // :: Jarallax Active Code
  if ($.fn.jarallax) {
    $('.jarallax').jarallax({
      speed: 0.5
    });
  }

  // :: Scrollup Active Code
  // if ($.fn.scrollUp) {
  //     radixWindow.scrollUp({
  //         scrollSpeed: 1100,
  //         scrollText: '<i class="lni lni-chevron-up"></i>'
  //     });
  // }

  // :: Counter Up Active Code
  if ($.fn.counterUp) {
    $('.rs-counter').counterUp({
      delay: 15,
      time: 1500
    });
  }

  // :: Prevent Default 'a' Click
  $('a[href="#"]').on('click', function ($) {
    $.preventDefault();
  });

  // :: Service Active Code
  $('.service-card').on('mouseenter', function () {
    $('.service-card').removeClass('active');
    $(this).addClass('active');
  });

  // :: Animated Headline Active Code
  if ($.fn.animatedHeadline) {
    $('.animated--headline').animatedHeadline({
      animationType: 'clip'
    });
  }

  // :: Password Strength Active Code
  if ($.fn.passwordStrength) {
    $('#registerPassword').passwordStrength({
      minimumChars: 8
    });
  }

  // :: Password Hide Show Active Code
  var inputPassword = $('.input-psswd');
  $('.label-psswd').on('click', function () {
    if (inputPassword.attr('psswd-shown') == 'false') {
      inputPassword.removeAttr('type');
      inputPassword.attr('type', 'text');
      inputPassword.removeAttr('psswd-shown');
      inputPassword.attr('psswd-shown', 'true');
    } else {
      inputPassword.removeAttr('type');
      inputPassword.attr('type', 'password');
      inputPassword.removeAttr('psswd-shown');
      inputPassword.attr('psswd-shown', 'false');
    }
    $(this).toggleClass("active");
  });
})(jQuery);

/***/ }),

/***/ "./resources/js/libs/default/classy-nav.min.js":
/*!*****************************************************!*\
  !*** ./resources/js/libs/default/classy-nav.min.js ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

// **********************************************
// ** Classy Nav - 1.1.0
// ** Responsive Megamenu Plugins
// ** Copyright (c) 2019 Designing World
// **********************************************

!function (e) {
  e.fn.classyNav = function (n) {
    var a = e(".classy-nav-container"),
      s = e(".classynav ul"),
      o = e(".classynav > ul > li"),
      i = e(".classy-navbar-toggler"),
      l = e(".classycloseIcon"),
      t = e(".navbarToggler"),
      d = e(".classy-menu"),
      r = e(window),
      c = e.extend({
        breakpoint: 991,
        openCloseSpeed: 500,
        megaopenCloseSpeed: 800
      }, n);
    return this.each(function () {
      function n() {
        window.innerWidth <= c.breakpoint ? a.removeClass("breakpoint-off").addClass("breakpoint-on") : a.removeClass("breakpoint-on").addClass("breakpoint-off");
      }
      i.on("click", function () {
        t.toggleClass("active"), d.toggleClass("menu-on");
      }), l.on("click", function () {
        d.removeClass("menu-on"), t.removeClass("active");
      }), o.has(".dropdown").addClass("cn-dropdown-item"), o.has(".megamenu").addClass("megamenu-item"), s.find("li a").each(function () {
        e(this).next().length > 0 && e(this).parent("li").addClass("has-down").append('<span class="dd-trigger"></span>');
      }), s.find("li .dd-trigger").on("click", function (n) {
        n.preventDefault(), e(this).parent("li").children("ul").stop(!0, !0).slideToggle(c.openCloseSpeed), e(this).parent("li").toggleClass("active");
      }), e(".megamenu-item").removeClass("has-down"), s.find("li .dd-trigger").on("click", function (n) {
        n.preventDefault(), e(this).parent("li").children(".megamenu").slideToggle(c.megaopenCloseSpeed);
      }), n(), r.on("resize", function () {
        n();
      }), !0 === c.sideMenu && a.addClass("sidebar-menu-on").removeClass("breakpoint-off");
    });
  };
}(jQuery);

/***/ }),

/***/ "./resources/js/libs/default/cookiealert.js":
/*!**************************************************!*\
  !*** ./resources/js/libs/default/cookiealert.js ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*
 * Bootstrap Cookie Alert by Wruczek
 * https://github.com/Wruczek/Bootstrap-Cookie-Alert
 * Released under MIT license
 */
(function () {
  "use strict";

  var cookieAlert = document.querySelector(".cookiealert");
  var acceptCookies = document.querySelector(".acceptcookies");
  if (!cookieAlert) {
    return;
  }
  cookieAlert.offsetHeight; // Force browser to trigger reflow (https://stackoverflow.com/a/39451131)

  // Show the alert if we cant find the "acceptCookies" cookie
  if (!getCookie("acceptCookies")) {
    cookieAlert.classList.add("show");
  }

  // When clicking on the agree button, create a 1 year
  // cookie to remember user's choice and close the banner
  acceptCookies.addEventListener("click", function () {
    setCookie("acceptCookies", true, 365);
    cookieAlert.classList.remove("show");
  });

  // Cookie functions from w3schools
  function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
  }
  function getCookie(cname) {
    var name = cname + "=";
    var decodedCookie = decodeURIComponent(document.cookie);
    var ca = decodedCookie.split(';');
    for (var i = 0; i < ca.length; i++) {
      var c = ca[i];
      while (c.charAt(0) === ' ') {
        c = c.substring(1);
      }
      if (c.indexOf(name) === 0) {
        return c.substring(name.length, c.length);
      }
    }
    return "";
  }
})();

/***/ }),

/***/ "./resources/js/libs/default/isotope.pkgd.min.js":
/*!*******************************************************!*\
  !*** ./resources/js/libs/default/isotope.pkgd.min.js ***!
  \*******************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;var __WEBPACK_LOCAL_MODULE_1__, __WEBPACK_LOCAL_MODULE_1__factory, __WEBPACK_LOCAL_MODULE_1__module;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_2__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_LOCAL_MODULE_3__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_LOCAL_MODULE_4__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_5__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_LOCAL_MODULE_6__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_7__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_8__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_9__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_10__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_11__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_12__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_13__;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_LOCAL_MODULE_14__;var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/*!
 * Isotope PACKAGED v2.1.0
 * Filter & sort magical layouts
 * http://isotope.metafizzy.co
 */

/**
 * Bridget makes jQuery widgets
 * v1.1.0
 * MIT license
 */

(function (window) {
  // -------------------------- utils -------------------------- //

  var slice = Array.prototype.slice;
  function noop() {}

  // -------------------------- definition -------------------------- //

  function defineBridget($) {
    // bail if no jQuery
    if (!$) {
      return;
    }

    // -------------------------- addOptionMethod -------------------------- //

    /**
     * adds option method -> $().plugin('option', {...})
     * @param {Function} PluginClass - constructor class
     */
    function addOptionMethod(PluginClass) {
      // don't overwrite original option method
      if (PluginClass.prototype.option) {
        return;
      }

      // option setter
      PluginClass.prototype.option = function (opts) {
        // bail out if not an object
        if (!$.isPlainObject(opts)) {
          return;
        }
        this.options = $.extend(true, this.options, opts);
      };
    }

    // -------------------------- plugin bridge -------------------------- //

    // helper function for logging errors
    // $.error breaks jQuery chaining
    var logError = typeof console === 'undefined' ? noop : function (message) {
      console.error(message);
    };

    /**
     * jQuery plugin bridge, access methods like $elem.plugin('method')
     * @param {String} namespace - plugin name
     * @param {Function} PluginClass - constructor class
     */
    function bridge(namespace, PluginClass) {
      // add to jQuery fn namespace
      $.fn[namespace] = function (options) {
        if (typeof options === 'string') {
          // call plugin method when first argument is a string
          // get arguments for method
          var args = slice.call(arguments, 1);
          for (var i = 0, len = this.length; i < len; i++) {
            var elem = this[i];
            var instance = $.data(elem, namespace);
            if (!instance) {
              logError("cannot call methods on " + namespace + " prior to initialization; " + "attempted to call '" + options + "'");
              continue;
            }
            if (!$.isFunction(instance[options]) || options.charAt(0) === '_') {
              logError("no such method '" + options + "' for " + namespace + " instance");
              continue;
            }

            // trigger method with arguments
            var returnValue = instance[options].apply(instance, args);

            // break look and return first value if provided
            if (returnValue !== undefined) {
              return returnValue;
            }
          }
          // return this if no return value
          return this;
        } else {
          return this.each(function () {
            var instance = $.data(this, namespace);
            if (instance) {
              // apply options & init
              instance.option(options);
              instance._init();
            } else {
              // initialize new instance
              instance = new PluginClass(this, options);
              $.data(this, namespace, instance);
            }
          });
        }
      };
    }

    // -------------------------- bridget -------------------------- //

    /**
     * converts a Prototypical class into a proper jQuery plugin
     *   the class must have a ._init method
     * @param {String} namespace - plugin name, used in $().pluginName
     * @param {Function} PluginClass - constructor class
     */
    $.bridget = function (namespace, PluginClass) {
      addOptionMethod(PluginClass);
      bridge(namespace, PluginClass);
    };
    return $.bridget;
  }

  // transport
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! jquery */ "./node_modules/jquery/dist/jquery.js")], __WEBPACK_AMD_DEFINE_FACTORY__ = (defineBridget),
				__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
  } else {}
})(window);

/*!
 * eventie v1.0.5
 * event binding helper
 *   eventie.bind( elem, 'click', myFn )
 *   eventie.unbind( elem, 'click', myFn )
 * MIT license
 */

/*jshint browser: true, undef: true, unused: true */
/*global define: false, module: false */

(function (window) {
  var docElem = document.documentElement;
  var bind = function bind() {};
  function getIEEvent(obj) {
    var event = window.event;
    // add event.target
    event.target = event.target || event.srcElement || obj;
    return event;
  }
  if (docElem.addEventListener) {
    bind = function bind(obj, type, fn) {
      obj.addEventListener(type, fn, false);
    };
  } else if (docElem.attachEvent) {
    bind = function bind(obj, type, fn) {
      obj[type + fn] = fn.handleEvent ? function () {
        var event = getIEEvent(obj);
        fn.handleEvent.call(fn, event);
      } : function () {
        var event = getIEEvent(obj);
        fn.call(obj, event);
      };
      obj.attachEvent("on" + type, obj[type + fn]);
    };
  }
  var unbind = function unbind() {};
  if (docElem.removeEventListener) {
    unbind = function unbind(obj, type, fn) {
      obj.removeEventListener(type, fn, false);
    };
  } else if (docElem.detachEvent) {
    unbind = function unbind(obj, type, fn) {
      obj.detachEvent("on" + type, obj[type + fn]);
      try {
        delete obj[type + fn];
      } catch (err) {
        // can't delete window object properties
        obj[type + fn] = undefined;
      }
    };
  }
  var eventie = {
    bind: bind,
    unbind: unbind
  };

  // ----- module definition ----- //

  if (true) {
    // AMD
    !(__WEBPACK_LOCAL_MODULE_1__factory = (eventie), (__WEBPACK_LOCAL_MODULE_1__module = { id: "eventie/eventie", exports: {}, loaded: false }), __WEBPACK_LOCAL_MODULE_1__ = (typeof __WEBPACK_LOCAL_MODULE_1__factory === 'function' ? (__WEBPACK_LOCAL_MODULE_1__factory.call(__WEBPACK_LOCAL_MODULE_1__module.exports, __webpack_require__, __WEBPACK_LOCAL_MODULE_1__module.exports, __WEBPACK_LOCAL_MODULE_1__module)) : __WEBPACK_LOCAL_MODULE_1__factory), (__WEBPACK_LOCAL_MODULE_1__module.loaded = true), __WEBPACK_LOCAL_MODULE_1__ === undefined && (__WEBPACK_LOCAL_MODULE_1__ = __WEBPACK_LOCAL_MODULE_1__module.exports));
  } else {}
})(this);

/*!
 * docReady v1.0.4
 * Cross browser DOMContentLoaded event emitter
 * MIT license
 */

/*jshint browser: true, strict: true, undef: true, unused: true*/
/*global define: false, require: false, module: false */

(function (window) {
  var document = window.document;
  // collection of functions to be triggered on ready
  var queue = [];
  function docReady(fn) {
    // throw out non-functions
    if (typeof fn !== 'function') {
      return;
    }
    if (docReady.isReady) {
      // ready now, hit it
      fn();
    } else {
      // queue function when ready
      queue.push(fn);
    }
  }
  docReady.isReady = false;

  // triggered on various doc ready events
  function onReady(event) {
    // bail if already triggered or IE8 document is not ready just yet
    var isIE8NotReady = event.type === 'readystatechange' && document.readyState !== 'complete';
    if (docReady.isReady || isIE8NotReady) {
      return;
    }
    trigger();
  }
  function trigger() {
    docReady.isReady = true;
    // process queue
    for (var i = 0, len = queue.length; i < len; i++) {
      var fn = queue[i];
      fn();
    }
  }
  function defineDocReady(eventie) {
    // trigger ready if page is ready
    if (document.readyState === 'complete') {
      trigger();
    } else {
      // listen for events
      eventie.bind(document, 'DOMContentLoaded', onReady);
      eventie.bind(document, 'readystatechange', onReady);
      eventie.bind(window, 'load', onReady);
    }
    return docReady;
  }

  // transport
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_1__], __WEBPACK_AMD_DEFINE_FACTORY__ = (defineDocReady),
				__WEBPACK_LOCAL_MODULE_2__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/*!
 * EventEmitter v4.2.9 - git.io/ee
 * Oliver Caldwell
 * MIT license
 * @preserve
 */

(function () {
  /**
   * Class for managing events.
   * Can be extended to provide event functionality in other classes.
   *
   * @class EventEmitter Manages event registering and emitting.
   */
  function EventEmitter() {}

  // Shortcuts to improve speed and size
  var proto = EventEmitter.prototype;
  var exports = this;
  var originalGlobalValue = exports.EventEmitter;

  /**
   * Finds the index of the listener for the event in its storage array.
   *
   * @param {Function[]} listeners Array of listeners to search through.
   * @param {Function} listener Method to look for.
   * @return {Number} Index of the specified listener, -1 if not found
   * @api private
   */
  function indexOfListener(listeners, listener) {
    var i = listeners.length;
    while (i--) {
      if (listeners[i].listener === listener) {
        return i;
      }
    }
    return -1;
  }

  /**
   * Alias a method while keeping the context correct, to allow for overwriting of target method.
   *
   * @param {String} name The name of the target method.
   * @return {Function} The aliased method
   * @api private
   */
  function alias(name) {
    return function aliasClosure() {
      return this[name].apply(this, arguments);
    };
  }

  /**
   * Returns the listener array for the specified event.
   * Will initialise the event object and listener arrays if required.
   * Will return an object if you use a regex search. The object contains keys for each matched event. So /ba[rz]/ might return an object containing bar and baz. But only if you have either defined them with defineEvent or added some listeners to them.
   * Each property in the object response is an array of listener functions.
   *
   * @param {String|RegExp} evt Name of the event to return the listeners from.
   * @return {Function[]|Object} All listener functions for the event.
   */
  proto.getListeners = function getListeners(evt) {
    var events = this._getEvents();
    var response;
    var key;

    // Return a concatenated array of all matching events if
    // the selector is a regular expression.
    if (evt instanceof RegExp) {
      response = {};
      for (key in events) {
        if (events.hasOwnProperty(key) && evt.test(key)) {
          response[key] = events[key];
        }
      }
    } else {
      response = events[evt] || (events[evt] = []);
    }
    return response;
  };

  /**
   * Takes a list of listener objects and flattens it into a list of listener functions.
   *
   * @param {Object[]} listeners Raw listener objects.
   * @return {Function[]} Just the listener functions.
   */
  proto.flattenListeners = function flattenListeners(listeners) {
    var flatListeners = [];
    var i;
    for (i = 0; i < listeners.length; i += 1) {
      flatListeners.push(listeners[i].listener);
    }
    return flatListeners;
  };

  /**
   * Fetches the requested listeners via getListeners but will always return the results inside an object. This is mainly for internal use but others may find it useful.
   *
   * @param {String|RegExp} evt Name of the event to return the listeners from.
   * @return {Object} All listener functions for an event in an object.
   */
  proto.getListenersAsObject = function getListenersAsObject(evt) {
    var listeners = this.getListeners(evt);
    var response;
    if (listeners instanceof Array) {
      response = {};
      response[evt] = listeners;
    }
    return response || listeners;
  };

  /**
   * Adds a listener function to the specified event.
   * The listener will not be added if it is a duplicate.
   * If the listener returns true then it will be removed after it is called.
   * If you pass a regular expression as the event name then the listener will be added to all events that match it.
   *
   * @param {String|RegExp} evt Name of the event to attach the listener to.
   * @param {Function} listener Method to be called when the event is emitted. If the function returns true then it will be removed after calling.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.addListener = function addListener(evt, listener) {
    var listeners = this.getListenersAsObject(evt);
    var listenerIsWrapped = _typeof(listener) === 'object';
    var key;
    for (key in listeners) {
      if (listeners.hasOwnProperty(key) && indexOfListener(listeners[key], listener) === -1) {
        listeners[key].push(listenerIsWrapped ? listener : {
          listener: listener,
          once: false
        });
      }
    }
    return this;
  };

  /**
   * Alias of addListener
   */
  proto.on = alias('addListener');

  /**
   * Semi-alias of addListener. It will add a listener that will be
   * automatically removed after its first execution.
   *
   * @param {String|RegExp} evt Name of the event to attach the listener to.
   * @param {Function} listener Method to be called when the event is emitted. If the function returns true then it will be removed after calling.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.addOnceListener = function addOnceListener(evt, listener) {
    return this.addListener(evt, {
      listener: listener,
      once: true
    });
  };

  /**
   * Alias of addOnceListener.
   */
  proto.once = alias('addOnceListener');

  /**
   * Defines an event name. This is required if you want to use a regex to add a listener to multiple events at once. If you don't do this then how do you expect it to know what event to add to? Should it just add to every possible match for a regex? No. That is scary and bad.
   * You need to tell it what event names should be matched by a regex.
   *
   * @param {String} evt Name of the event to create.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.defineEvent = function defineEvent(evt) {
    this.getListeners(evt);
    return this;
  };

  /**
   * Uses defineEvent to define multiple events.
   *
   * @param {String[]} evts An array of event names to define.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.defineEvents = function defineEvents(evts) {
    for (var i = 0; i < evts.length; i += 1) {
      this.defineEvent(evts[i]);
    }
    return this;
  };

  /**
   * Removes a listener function from the specified event.
   * When passed a regular expression as the event name, it will remove the listener from all events that match it.
   *
   * @param {String|RegExp} evt Name of the event to remove the listener from.
   * @param {Function} listener Method to remove from the event.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.removeListener = function removeListener(evt, listener) {
    var listeners = this.getListenersAsObject(evt);
    var index;
    var key;
    for (key in listeners) {
      if (listeners.hasOwnProperty(key)) {
        index = indexOfListener(listeners[key], listener);
        if (index !== -1) {
          listeners[key].splice(index, 1);
        }
      }
    }
    return this;
  };

  /**
   * Alias of removeListener
   */
  proto.off = alias('removeListener');

  /**
   * Adds listeners in bulk using the manipulateListeners method.
   * If you pass an object as the second argument you can add to multiple events at once. The object should contain key value pairs of events and listeners or listener arrays. You can also pass it an event name and an array of listeners to be added.
   * You can also pass it a regular expression to add the array of listeners to all events that match it.
   * Yeah, this function does quite a bit. That's probably a bad thing.
   *
   * @param {String|Object|RegExp} evt An event name if you will pass an array of listeners next. An object if you wish to add to multiple events at once.
   * @param {Function[]} [listeners] An optional array of listener functions to add.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.addListeners = function addListeners(evt, listeners) {
    // Pass through to manipulateListeners
    return this.manipulateListeners(false, evt, listeners);
  };

  /**
   * Removes listeners in bulk using the manipulateListeners method.
   * If you pass an object as the second argument you can remove from multiple events at once. The object should contain key value pairs of events and listeners or listener arrays.
   * You can also pass it an event name and an array of listeners to be removed.
   * You can also pass it a regular expression to remove the listeners from all events that match it.
   *
   * @param {String|Object|RegExp} evt An event name if you will pass an array of listeners next. An object if you wish to remove from multiple events at once.
   * @param {Function[]} [listeners] An optional array of listener functions to remove.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.removeListeners = function removeListeners(evt, listeners) {
    // Pass through to manipulateListeners
    return this.manipulateListeners(true, evt, listeners);
  };

  /**
   * Edits listeners in bulk. The addListeners and removeListeners methods both use this to do their job. You should really use those instead, this is a little lower level.
   * The first argument will determine if the listeners are removed (true) or added (false).
   * If you pass an object as the second argument you can add/remove from multiple events at once. The object should contain key value pairs of events and listeners or listener arrays.
   * You can also pass it an event name and an array of listeners to be added/removed.
   * You can also pass it a regular expression to manipulate the listeners of all events that match it.
   *
   * @param {Boolean} remove True if you want to remove listeners, false if you want to add.
   * @param {String|Object|RegExp} evt An event name if you will pass an array of listeners next. An object if you wish to add/remove from multiple events at once.
   * @param {Function[]} [listeners] An optional array of listener functions to add/remove.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.manipulateListeners = function manipulateListeners(remove, evt, listeners) {
    var i;
    var value;
    var single = remove ? this.removeListener : this.addListener;
    var multiple = remove ? this.removeListeners : this.addListeners;

    // If evt is an object then pass each of its properties to this method
    if (_typeof(evt) === 'object' && !(evt instanceof RegExp)) {
      for (i in evt) {
        if (evt.hasOwnProperty(i) && (value = evt[i])) {
          // Pass the single listener straight through to the singular method
          if (typeof value === 'function') {
            single.call(this, i, value);
          } else {
            // Otherwise pass back to the multiple function
            multiple.call(this, i, value);
          }
        }
      }
    } else {
      // So evt must be a string
      // And listeners must be an array of listeners
      // Loop over it and pass each one to the multiple method
      i = listeners.length;
      while (i--) {
        single.call(this, evt, listeners[i]);
      }
    }
    return this;
  };

  /**
   * Removes all listeners from a specified event.
   * If you do not specify an event then all listeners will be removed.
   * That means every event will be emptied.
   * You can also pass a regex to remove all events that match it.
   *
   * @param {String|RegExp} [evt] Optional name of the event to remove all listeners for. Will remove from every event if not passed.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.removeEvent = function removeEvent(evt) {
    var type = _typeof(evt);
    var events = this._getEvents();
    var key;

    // Remove different things depending on the state of evt
    if (type === 'string') {
      // Remove all listeners for the specified event
      delete events[evt];
    } else if (evt instanceof RegExp) {
      // Remove all events matching the regex.
      for (key in events) {
        if (events.hasOwnProperty(key) && evt.test(key)) {
          delete events[key];
        }
      }
    } else {
      // Remove all listeners in all events
      delete this._events;
    }
    return this;
  };

  /**
   * Alias of removeEvent.
   *
   * Added to mirror the node API.
   */
  proto.removeAllListeners = alias('removeEvent');

  /**
   * Emits an event of your choice.
   * When emitted, every listener attached to that event will be executed.
   * If you pass the optional argument array then those arguments will be passed to every listener upon execution.
   * Because it uses `apply`, your array of arguments will be passed as if you wrote them out separately.
   * So they will not arrive within the array on the other side, they will be separate.
   * You can also pass a regular expression to emit to all events that match it.
   *
   * @param {String|RegExp} evt Name of the event to emit and execute listeners for.
   * @param {Array} [args] Optional array of arguments to be passed to each listener.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.emitEvent = function emitEvent(evt, args) {
    var listeners = this.getListenersAsObject(evt);
    var listener;
    var i;
    var key;
    var response;
    for (key in listeners) {
      if (listeners.hasOwnProperty(key)) {
        i = listeners[key].length;
        while (i--) {
          // If the listener returns true then it shall be removed from the event
          // The function is executed either with a basic call or an apply if there is an args array
          listener = listeners[key][i];
          if (listener.once === true) {
            this.removeListener(evt, listener.listener);
          }
          response = listener.listener.apply(this, args || []);
          if (response === this._getOnceReturnValue()) {
            this.removeListener(evt, listener.listener);
          }
        }
      }
    }
    return this;
  };

  /**
   * Alias of emitEvent
   */
  proto.trigger = alias('emitEvent');

  /**
   * Subtly different from emitEvent in that it will pass its arguments on to the listeners, as opposed to taking a single array of arguments to pass on.
   * As with emitEvent, you can pass a regex in place of the event name to emit to all events that match it.
   *
   * @param {String|RegExp} evt Name of the event to emit and execute listeners for.
   * @param {...*} Optional additional arguments to be passed to each listener.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.emit = function emit(evt) {
    var args = Array.prototype.slice.call(arguments, 1);
    return this.emitEvent(evt, args);
  };

  /**
   * Sets the current value to check against when executing listeners. If a
   * listeners return value matches the one set here then it will be removed
   * after execution. This value defaults to true.
   *
   * @param {*} value The new value to check for when executing listeners.
   * @return {Object} Current instance of EventEmitter for chaining.
   */
  proto.setOnceReturnValue = function setOnceReturnValue(value) {
    this._onceReturnValue = value;
    return this;
  };

  /**
   * Fetches the current value to check against when executing listeners. If
   * the listeners return value matches this one then it should be removed
   * automatically. It will return true by default.
   *
   * @return {*|Boolean} The current value to check for or the default, true.
   * @api private
   */
  proto._getOnceReturnValue = function _getOnceReturnValue() {
    if (this.hasOwnProperty('_onceReturnValue')) {
      return this._onceReturnValue;
    } else {
      return true;
    }
  };

  /**
   * Fetches the events object and creates one if required.
   *
   * @return {Object} The events storage object.
   * @api private
   */
  proto._getEvents = function _getEvents() {
    return this._events || (this._events = {});
  };

  /**
   * Reverts the global {@link EventEmitter} to its previous value and returns a reference to this version.
   *
   * @return {Function} Non conflicting EventEmitter class.
   */
  EventEmitter.noConflict = function noConflict() {
    exports.EventEmitter = originalGlobalValue;
    return EventEmitter;
  };

  // Expose the class either via AMD, CommonJS or the global object
  if (true) {
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_LOCAL_MODULE_3__ = ((function () {
      return EventEmitter;
    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)));
  } else {}
}).call(this);

/*!
 * getStyleProperty v1.0.4
 * original by kangax
 * http://perfectionkills.com/feature-testing-css-properties/
 * MIT license
 */

/*jshint browser: true, strict: true, undef: true */
/*global define: false, exports: false, module: false */

(function (window) {
  var prefixes = 'Webkit Moz ms Ms O'.split(' ');
  var docElemStyle = document.documentElement.style;
  function getStyleProperty(propName) {
    if (!propName) {
      return;
    }

    // test standard property first
    if (typeof docElemStyle[propName] === 'string') {
      return propName;
    }

    // capitalize
    propName = propName.charAt(0).toUpperCase() + propName.slice(1);

    // test vendor specific properties
    var prefixed;
    for (var i = 0, len = prefixes.length; i < len; i++) {
      prefixed = prefixes[i] + propName;
      if (typeof docElemStyle[prefixed] === 'string') {
        return prefixed;
      }
    }
  }

  // transport
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_LOCAL_MODULE_4__ = ((function () {
      return getStyleProperty;
    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)));
  } else {}
})(window);

/*!
 * getSize v1.2.0
 * measure size of elements
 * MIT license
 */

/*jshint browser: true, strict: true, undef: true, unused: true */
/*global define: false, exports: false, require: false, module: false */

(function (window, undefined) {
  // -------------------------- helpers -------------------------- //

  // get a number from a string, not a percentage
  function getStyleSize(value) {
    var num = parseFloat(value);
    // not a percent like '100%', and a number
    var isValid = value.indexOf('%') === -1 && !isNaN(num);
    return isValid && num;
  }
  var logError = typeof console === 'undefined' ? noop : function (message) {
    console.error(message);
  };

  // -------------------------- measurements -------------------------- //

  var measurements = ['paddingLeft', 'paddingRight', 'paddingTop', 'paddingBottom', 'marginLeft', 'marginRight', 'marginTop', 'marginBottom', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'borderBottomWidth'];
  function getZeroSize() {
    var size = {
      width: 0,
      height: 0,
      innerWidth: 0,
      innerHeight: 0,
      outerWidth: 0,
      outerHeight: 0
    };
    for (var i = 0, len = measurements.length; i < len; i++) {
      var measurement = measurements[i];
      size[measurement] = 0;
    }
    return size;
  }
  function defineGetSize(getStyleProperty) {
    // -------------------------- setup -------------------------- //

    var isSetup = false;
    var getStyle, boxSizingProp, isBoxSizeOuter;

    /**
     * setup vars and functions
     * do it on initial getSize(), rather than on script load
     * For Firefox bug https://bugzilla.mozilla.org/show_bug.cgi?id=548397
     */
    function setup() {
      // setup once
      if (isSetup) {
        return;
      }
      isSetup = true;
      var getComputedStyle = window.getComputedStyle;
      getStyle = function () {
        var getStyleFn = getComputedStyle ? function (elem) {
          return getComputedStyle(elem, null);
        } : function (elem) {
          return elem.currentStyle;
        };
        return function getStyle(elem) {
          var style = getStyleFn(elem);
          if (!style) {
            logError('Style returned ' + style + '. Are you running this code in a hidden iframe on Firefox? ' + 'See http://bit.ly/getsizeiframe');
          }
          return style;
        };
      }();

      // -------------------------- box sizing -------------------------- //

      boxSizingProp = getStyleProperty('boxSizing');

      /**
       * WebKit measures the outer-width on style.width on border-box elems
       * IE & Firefox measures the inner-width
       */
      if (boxSizingProp) {
        var div = document.createElement('div');
        div.style.width = '200px';
        div.style.padding = '1px 2px 3px 4px';
        div.style.borderStyle = 'solid';
        div.style.borderWidth = '1px 2px 3px 4px';
        div.style[boxSizingProp] = 'border-box';
        var body = document.body || document.documentElement;
        body.appendChild(div);
        var style = getStyle(div);
        isBoxSizeOuter = getStyleSize(style.width) === 200;
        body.removeChild(div);
      }
    }

    // -------------------------- getSize -------------------------- //

    function getSize(elem) {
      setup();

      // use querySeletor if elem is string
      if (typeof elem === 'string') {
        elem = document.querySelector(elem);
      }

      // do not proceed on non-objects
      if (!elem || _typeof(elem) !== 'object' || !elem.nodeType) {
        return;
      }
      var style = getStyle(elem);

      // if hidden, everything is 0
      if (style.display === 'none') {
        return getZeroSize();
      }
      var size = {};
      size.width = elem.offsetWidth;
      size.height = elem.offsetHeight;
      var isBorderBox = size.isBorderBox = !!(boxSizingProp && style[boxSizingProp] && style[boxSizingProp] === 'border-box');

      // get all measurements
      for (var i = 0, len = measurements.length; i < len; i++) {
        var measurement = measurements[i];
        var value = style[measurement];
        value = mungeNonPixel(elem, value);
        var num = parseFloat(value);
        // any 'auto', 'medium' value will be 0
        size[measurement] = !isNaN(num) ? num : 0;
      }
      var paddingWidth = size.paddingLeft + size.paddingRight;
      var paddingHeight = size.paddingTop + size.paddingBottom;
      var marginWidth = size.marginLeft + size.marginRight;
      var marginHeight = size.marginTop + size.marginBottom;
      var borderWidth = size.borderLeftWidth + size.borderRightWidth;
      var borderHeight = size.borderTopWidth + size.borderBottomWidth;
      var isBorderBoxSizeOuter = isBorderBox && isBoxSizeOuter;

      // overwrite width and height if we can get it from style
      var styleWidth = getStyleSize(style.width);
      if (styleWidth !== false) {
        size.width = styleWidth + (
        // add padding and border unless it's already including it
        isBorderBoxSizeOuter ? 0 : paddingWidth + borderWidth);
      }
      var styleHeight = getStyleSize(style.height);
      if (styleHeight !== false) {
        size.height = styleHeight + (
        // add padding and border unless it's already including it
        isBorderBoxSizeOuter ? 0 : paddingHeight + borderHeight);
      }
      size.innerWidth = size.width - (paddingWidth + borderWidth);
      size.innerHeight = size.height - (paddingHeight + borderHeight);
      size.outerWidth = size.width + marginWidth;
      size.outerHeight = size.height + marginHeight;
      return size;
    }

    // IE8 returns percent values, not pixels
    // taken from jQuery's curCSS
    function mungeNonPixel(elem, value) {
      // IE8 and has percent value
      if (getComputedStyle || value.indexOf('%') === -1) {
        return value;
      }
      var style = elem.style;
      // Remember the original values
      var left = style.left;
      var rs = elem.runtimeStyle;
      var rsLeft = rs && rs.left;

      // Put in the new values to get a computed value out
      if (rsLeft) {
        rs.left = elem.currentStyle.left;
      }
      style.left = value;
      value = style.pixelLeft;

      // Revert the changed values
      style.left = left;
      if (rsLeft) {
        rs.left = rsLeft;
      }
      return value;
    }
    return getSize;
  }

  // transport
  if (true) {
    // AMD for RequireJS
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_4__], __WEBPACK_AMD_DEFINE_FACTORY__ = (defineGetSize),
				__WEBPACK_LOCAL_MODULE_5__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/**
 * matchesSelector v1.0.2
 * matchesSelector( element, '.selector' )
 * MIT license
 */

/*jshint browser: true, strict: true, undef: true, unused: true */
/*global define: false, module: false */

(function (ElemProto) {
  var matchesMethod = function () {
    // check un-prefixed
    if (ElemProto.matchesSelector) {
      return 'matchesSelector';
    }
    // check vendor prefixes
    var prefixes = ['webkit', 'moz', 'ms', 'o'];
    for (var i = 0, len = prefixes.length; i < len; i++) {
      var prefix = prefixes[i];
      var method = prefix + 'MatchesSelector';
      if (ElemProto[method]) {
        return method;
      }
    }
  }();

  // ----- match ----- //

  function match(elem, selector) {
    return elem[matchesMethod](selector);
  }

  // ----- appendToFragment ----- //

  function checkParent(elem) {
    // not needed if already has parent
    if (elem.parentNode) {
      return;
    }
    var fragment = document.createDocumentFragment();
    fragment.appendChild(elem);
  }

  // ----- query ----- //

  // fall back to using QSA
  // thx @jonathantneal https://gist.github.com/3062955
  function query(elem, selector) {
    // append to fragment if no parent
    checkParent(elem);

    // match elem with all selected elems of parent
    var elems = elem.parentNode.querySelectorAll(selector);
    for (var i = 0, len = elems.length; i < len; i++) {
      // return true if match
      if (elems[i] === elem) {
        return true;
      }
    }
    // otherwise return false
    return false;
  }

  // ----- matchChild ----- //

  function matchChild(elem, selector) {
    checkParent(elem);
    return match(elem, selector);
  }

  // ----- matchesSelector ----- //

  var matchesSelector;
  if (matchesMethod) {
    // IE9 supports matchesSelector, but doesn't work on orphaned elems
    // check for that
    var div = document.createElement('div');
    var supportsOrphans = match(div, 'div');
    matchesSelector = supportsOrphans ? match : matchChild;
  } else {
    matchesSelector = query;
  }

  // transport
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_LOCAL_MODULE_6__ = ((function () {
      return matchesSelector;
    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)));
  } else {}
})(Element.prototype);

/**
 * Outlayer Item
 */

(function (window) {
  // ----- get style ----- //

  var getComputedStyle = window.getComputedStyle;
  var getStyle = getComputedStyle ? function (elem) {
    return getComputedStyle(elem, null);
  } : function (elem) {
    return elem.currentStyle;
  };

  // extend objects
  function extend(a, b) {
    for (var prop in b) {
      a[prop] = b[prop];
    }
    return a;
  }
  function isEmptyObj(obj) {
    for (var prop in obj) {
      return false;
    }
    prop = null;
    return true;
  }

  // http://jamesroberts.name/blog/2010/02/22/string-functions-for-javascript-trim-to-camel-case-to-dashed-and-to-underscore/
  function toDash(str) {
    return str.replace(/([A-Z])/g, function ($1) {
      return '-' + $1.toLowerCase();
    });
  }

  // -------------------------- Outlayer definition -------------------------- //

  function outlayerItemDefinition(EventEmitter, getSize, getStyleProperty) {
    // -------------------------- CSS3 support -------------------------- //

    var transitionProperty = getStyleProperty('transition');
    var transformProperty = getStyleProperty('transform');
    var supportsCSS3 = transitionProperty && transformProperty;
    var is3d = !!getStyleProperty('perspective');
    var transitionEndEvent = {
      WebkitTransition: 'webkitTransitionEnd',
      MozTransition: 'transitionend',
      OTransition: 'otransitionend',
      transition: 'transitionend'
    }[transitionProperty];

    // properties that could have vendor prefix
    var prefixableProperties = ['transform', 'transition', 'transitionDuration', 'transitionProperty'];

    // cache all vendor properties
    var vendorProperties = function () {
      var cache = {};
      for (var i = 0, len = prefixableProperties.length; i < len; i++) {
        var prop = prefixableProperties[i];
        var supportedProp = getStyleProperty(prop);
        if (supportedProp && supportedProp !== prop) {
          cache[prop] = supportedProp;
        }
      }
      return cache;
    }();

    // -------------------------- Item -------------------------- //

    function Item(element, layout) {
      if (!element) {
        return;
      }
      this.element = element;
      // parent layout class, i.e. Masonry, Isotope, or Packery
      this.layout = layout;
      this.position = {
        x: 0,
        y: 0
      };
      this._create();
    }

    // inherit EventEmitter
    extend(Item.prototype, EventEmitter.prototype);
    Item.prototype._create = function () {
      // transition objects
      this._transn = {
        ingProperties: {},
        clean: {},
        onEnd: {}
      };
      this.css({
        position: 'absolute'
      });
    };

    // trigger specified handler for event type
    Item.prototype.handleEvent = function (event) {
      var method = 'on' + event.type;
      if (this[method]) {
        this[method](event);
      }
    };
    Item.prototype.getSize = function () {
      this.size = getSize(this.element);
    };

    /**
     * apply CSS styles to element
     * @param {Object} style
     */
    Item.prototype.css = function (style) {
      var elemStyle = this.element.style;
      for (var prop in style) {
        // use vendor property if available
        var supportedProp = vendorProperties[prop] || prop;
        elemStyle[supportedProp] = style[prop];
      }
    };

    // measure position, and sets it
    Item.prototype.getPosition = function () {
      var style = getStyle(this.element);
      var layoutOptions = this.layout.options;
      var isOriginLeft = layoutOptions.isOriginLeft;
      var isOriginTop = layoutOptions.isOriginTop;
      var x = parseInt(style[isOriginLeft ? 'left' : 'right'], 10);
      var y = parseInt(style[isOriginTop ? 'top' : 'bottom'], 10);

      // clean up 'auto' or other non-integer values
      x = isNaN(x) ? 0 : x;
      y = isNaN(y) ? 0 : y;
      // remove padding from measurement
      var layoutSize = this.layout.size;
      x -= isOriginLeft ? layoutSize.paddingLeft : layoutSize.paddingRight;
      y -= isOriginTop ? layoutSize.paddingTop : layoutSize.paddingBottom;
      this.position.x = x;
      this.position.y = y;
    };

    // set settled position, apply padding
    Item.prototype.layoutPosition = function () {
      var layoutSize = this.layout.size;
      var layoutOptions = this.layout.options;
      var style = {};
      if (layoutOptions.isOriginLeft) {
        style.left = this.position.x + layoutSize.paddingLeft + 'px';
        // reset other property
        style.right = '';
      } else {
        style.right = this.position.x + layoutSize.paddingRight + 'px';
        style.left = '';
      }
      if (layoutOptions.isOriginTop) {
        style.top = this.position.y + layoutSize.paddingTop + 'px';
        style.bottom = '';
      } else {
        style.bottom = this.position.y + layoutSize.paddingBottom + 'px';
        style.top = '';
      }
      this.css(style);
      this.emitEvent('layout', [this]);
    };

    // transform translate function
    var translate = is3d ? function (x, y) {
      return 'translate3d(' + x + 'px, ' + y + 'px, 0)';
    } : function (x, y) {
      return 'translate(' + x + 'px, ' + y + 'px)';
    };
    Item.prototype._transitionTo = function (x, y) {
      this.getPosition();
      // get current x & y from top/left
      var curX = this.position.x;
      var curY = this.position.y;
      var compareX = parseInt(x, 10);
      var compareY = parseInt(y, 10);
      var didNotMove = compareX === this.position.x && compareY === this.position.y;

      // save end position
      this.setPosition(x, y);

      // if did not move and not transitioning, just go to layout
      if (didNotMove && !this.isTransitioning) {
        this.layoutPosition();
        return;
      }
      var transX = x - curX;
      var transY = y - curY;
      var transitionStyle = {};
      // flip cooridinates if origin on right or bottom
      var layoutOptions = this.layout.options;
      transX = layoutOptions.isOriginLeft ? transX : -transX;
      transY = layoutOptions.isOriginTop ? transY : -transY;
      transitionStyle.transform = translate(transX, transY);
      this.transition({
        to: transitionStyle,
        onTransitionEnd: {
          transform: this.layoutPosition
        },
        isCleaning: true
      });
    };

    // non transition + transform support
    Item.prototype.goTo = function (x, y) {
      this.setPosition(x, y);
      this.layoutPosition();
    };

    // use transition and transforms if supported
    Item.prototype.moveTo = supportsCSS3 ? Item.prototype._transitionTo : Item.prototype.goTo;
    Item.prototype.setPosition = function (x, y) {
      this.position.x = parseInt(x, 10);
      this.position.y = parseInt(y, 10);
    };

    // ----- transition ----- //

    /**
     * @param {Object} style - CSS
     * @param {Function} onTransitionEnd
     */

    // non transition, just trigger callback
    Item.prototype._nonTransition = function (args) {
      this.css(args.to);
      if (args.isCleaning) {
        this._removeStyles(args.to);
      }
      for (var prop in args.onTransitionEnd) {
        args.onTransitionEnd[prop].call(this);
      }
    };

    /**
     * proper transition
     * @param {Object} args - arguments
     *   @param {Object} to - style to transition to
     *   @param {Object} from - style to start transition from
     *   @param {Boolean} isCleaning - removes transition styles after transition
     *   @param {Function} onTransitionEnd - callback
     */
    Item.prototype._transition = function (args) {
      // redirect to nonTransition if no transition duration
      if (!parseFloat(this.layout.options.transitionDuration)) {
        this._nonTransition(args);
        return;
      }
      var _transition = this._transn;
      // keep track of onTransitionEnd callback by css property
      for (var prop in args.onTransitionEnd) {
        _transition.onEnd[prop] = args.onTransitionEnd[prop];
      }
      // keep track of properties that are transitioning
      for (prop in args.to) {
        _transition.ingProperties[prop] = true;
        // keep track of properties to clean up when transition is done
        if (args.isCleaning) {
          _transition.clean[prop] = true;
        }
      }

      // set from styles
      if (args.from) {
        this.css(args.from);
        // force redraw. http://blog.alexmaccaw.com/css-transitions
        var h = this.element.offsetHeight;
        // hack for JSHint to hush about unused var
        h = null;
      }
      // enable transition
      this.enableTransition(args.to);
      // set styles that are transitioning
      this.css(args.to);
      this.isTransitioning = true;
    };
    var itemTransitionProperties = transformProperty && toDash(transformProperty) + ',opacity';
    Item.prototype.enableTransition = function /* style */
    () {
      // only enable if not already transitioning
      // bug in IE10 were re-setting transition style will prevent
      // transitionend event from triggering
      if (this.isTransitioning) {
        return;
      }

      // make transition: foo, bar, baz from style object
      // TODO uncomment this bit when IE10 bug is resolved
      // var transitionValue = [];
      // for ( var prop in style ) {
      //   // dash-ify camelCased properties like WebkitTransition
      //   transitionValue.push( toDash( prop ) );
      // }
      // enable transition styles
      // HACK always enable transform,opacity for IE10
      this.css({
        transitionProperty: itemTransitionProperties,
        transitionDuration: this.layout.options.transitionDuration
      });
      // listen for transition end event
      this.element.addEventListener(transitionEndEvent, this, false);
    };
    Item.prototype.transition = Item.prototype[transitionProperty ? '_transition' : '_nonTransition'];

    // ----- events ----- //

    Item.prototype.onwebkitTransitionEnd = function (event) {
      this.ontransitionend(event);
    };
    Item.prototype.onotransitionend = function (event) {
      this.ontransitionend(event);
    };

    // properties that I munge to make my life easier
    var dashedVendorProperties = {
      '-webkit-transform': 'transform',
      '-moz-transform': 'transform',
      '-o-transform': 'transform'
    };
    Item.prototype.ontransitionend = function (event) {
      // disregard bubbled events from children
      if (event.target !== this.element) {
        return;
      }
      var _transition = this._transn;
      // get property name of transitioned property, convert to prefix-free
      var propertyName = dashedVendorProperties[event.propertyName] || event.propertyName;

      // remove property that has completed transitioning
      delete _transition.ingProperties[propertyName];
      // check if any properties are still transitioning
      if (isEmptyObj(_transition.ingProperties)) {
        // all properties have completed transitioning
        this.disableTransition();
      }
      // clean style
      if (propertyName in _transition.clean) {
        // clean up style
        this.element.style[event.propertyName] = '';
        delete _transition.clean[propertyName];
      }
      // trigger onTransitionEnd callback
      if (propertyName in _transition.onEnd) {
        var onTransitionEnd = _transition.onEnd[propertyName];
        onTransitionEnd.call(this);
        delete _transition.onEnd[propertyName];
      }
      this.emitEvent('transitionEnd', [this]);
    };
    Item.prototype.disableTransition = function () {
      this.removeTransitionStyles();
      this.element.removeEventListener(transitionEndEvent, this, false);
      this.isTransitioning = false;
    };

    /**
     * removes style property from element
     * @param {Object} style
    **/
    Item.prototype._removeStyles = function (style) {
      // clean up transition styles
      var cleanStyle = {};
      for (var prop in style) {
        cleanStyle[prop] = '';
      }
      this.css(cleanStyle);
    };
    var cleanTransitionStyle = {
      transitionProperty: '',
      transitionDuration: ''
    };
    Item.prototype.removeTransitionStyles = function () {
      // remove transition
      this.css(cleanTransitionStyle);
    };

    // ----- show/hide/remove ----- //

    // remove element from DOM
    Item.prototype.removeElem = function () {
      this.element.parentNode.removeChild(this.element);
      this.emitEvent('remove', [this]);
    };
    Item.prototype.remove = function () {
      // just remove element if no transition support or no transition
      if (!transitionProperty || !parseFloat(this.layout.options.transitionDuration)) {
        this.removeElem();
        return;
      }

      // start transition
      var _this = this;
      this.on('transitionEnd', function () {
        _this.removeElem();
        return true; // bind once
      });

      this.hide();
    };
    Item.prototype.reveal = function () {
      delete this.isHidden;
      // remove display: none
      this.css({
        display: ''
      });
      var options = this.layout.options;
      this.transition({
        from: options.hiddenStyle,
        to: options.visibleStyle,
        isCleaning: true
      });
    };
    Item.prototype.hide = function () {
      // set flag
      this.isHidden = true;
      // remove display: none
      this.css({
        display: ''
      });
      var options = this.layout.options;
      this.transition({
        from: options.visibleStyle,
        to: options.hiddenStyle,
        // keep hidden stuff hidden
        isCleaning: true,
        onTransitionEnd: {
          opacity: function opacity() {
            // check if still hidden
            // during transition, item may have been un-hidden
            if (this.isHidden) {
              this.css({
                display: 'none'
              });
            }
          }
        }
      });
    };
    Item.prototype.destroy = function () {
      this.css({
        position: '',
        left: '',
        right: '',
        top: '',
        bottom: '',
        transition: '',
        transform: ''
      });
    };
    return Item;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_3__, __WEBPACK_LOCAL_MODULE_5__, __WEBPACK_LOCAL_MODULE_4__], __WEBPACK_AMD_DEFINE_FACTORY__ = (outlayerItemDefinition),
				__WEBPACK_LOCAL_MODULE_7__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/*!
 * Outlayer v1.3.0
 * the brains and guts of a layout library
 * MIT license
 */

(function (window) {
  // ----- vars ----- //

  var document = window.document;
  var console = window.console;
  var jQuery = window.jQuery;
  var noop = function noop() {};

  // -------------------------- helpers -------------------------- //

  // extend objects
  function extend(a, b) {
    for (var prop in b) {
      a[prop] = b[prop];
    }
    return a;
  }
  var objToString = Object.prototype.toString;
  function isArray(obj) {
    return objToString.call(obj) === '[object Array]';
  }

  // turn element or nodeList into an array
  function makeArray(obj) {
    var ary = [];
    if (isArray(obj)) {
      // use object if already an array
      ary = obj;
    } else if (obj && typeof obj.length === 'number') {
      // convert nodeList to array
      for (var i = 0, len = obj.length; i < len; i++) {
        ary.push(obj[i]);
      }
    } else {
      // array of single index
      ary.push(obj);
    }
    return ary;
  }

  // http://stackoverflow.com/a/384380/182183
  var isElement = typeof HTMLElement === 'function' || (typeof HTMLElement === "undefined" ? "undefined" : _typeof(HTMLElement)) === 'object' ? function isElementDOM2(obj) {
    return obj instanceof HTMLElement;
  } : function isElementQuirky(obj) {
    return obj && _typeof(obj) === 'object' && obj.nodeType === 1 && typeof obj.nodeName === 'string';
  };

  // index of helper cause IE8
  var indexOf = Array.prototype.indexOf ? function (ary, obj) {
    return ary.indexOf(obj);
  } : function (ary, obj) {
    for (var i = 0, len = ary.length; i < len; i++) {
      if (ary[i] === obj) {
        return i;
      }
    }
    return -1;
  };
  function removeFrom(obj, ary) {
    var index = indexOf(ary, obj);
    if (index !== -1) {
      ary.splice(index, 1);
    }
  }

  // http://jamesroberts.name/blog/2010/02/22/string-functions-for-javascript-trim-to-camel-case-to-dashed-and-to-underscore/
  function toDashed(str) {
    return str.replace(/(.)([A-Z])/g, function (match, $1, $2) {
      return $1 + '-' + $2;
    }).toLowerCase();
  }
  function outlayerDefinition(eventie, docReady, EventEmitter, getSize, matchesSelector, Item) {
    // -------------------------- Outlayer -------------------------- //

    // globally unique identifiers
    var GUID = 0;
    // internal store of all Outlayer intances
    var instances = {};

    /**
     * @param {Element, String} element
     * @param {Object} options
     * @constructor
     */
    function Outlayer(element, options) {
      // use element as selector string
      if (typeof element === 'string') {
        element = document.querySelector(element);
      }

      // bail out if not proper element
      if (!element || !isElement(element)) {
        if (console) {
          console.error('Bad ' + this.constructor.namespace + ' element: ' + element);
        }
        return;
      }
      this.element = element;

      // options
      this.options = extend({}, this.constructor.defaults);
      this.option(options);

      // add id for Outlayer.getFromElement
      var id = ++GUID;
      this.element.outlayerGUID = id; // expando
      instances[id] = this; // associate via id

      // kick it off
      this._create();
      if (this.options.isInitLayout) {
        this.layout();
      }
    }

    // settings are for internal use only
    Outlayer.namespace = 'outlayer';
    Outlayer.Item = Item;

    // default options
    Outlayer.defaults = {
      containerStyle: {
        position: 'relative'
      },
      isInitLayout: true,
      isOriginLeft: true,
      isOriginTop: true,
      isResizeBound: true,
      isResizingContainer: true,
      // item options
      transitionDuration: '0.4s',
      hiddenStyle: {
        opacity: 0,
        transform: 'scale(0.001)'
      },
      visibleStyle: {
        opacity: 1,
        transform: 'scale(1)'
      }
    };

    // inherit EventEmitter
    extend(Outlayer.prototype, EventEmitter.prototype);

    /**
     * set options
     * @param {Object} opts
     */
    Outlayer.prototype.option = function (opts) {
      extend(this.options, opts);
    };
    Outlayer.prototype._create = function () {
      // get items from children
      this.reloadItems();
      // elements that affect layout, but are not laid out
      this.stamps = [];
      this.stamp(this.options.stamp);
      // set container style
      extend(this.element.style, this.options.containerStyle);

      // bind resize method
      if (this.options.isResizeBound) {
        this.bindResize();
      }
    };

    // goes through all children again and gets bricks in proper order
    Outlayer.prototype.reloadItems = function () {
      // collection of item elements
      this.items = this._itemize(this.element.children);
    };

    /**
     * turn elements into Outlayer.Items to be used in layout
     * @param {Array or NodeList or HTMLElement} elems
     * @returns {Array} items - collection of new Outlayer Items
     */
    Outlayer.prototype._itemize = function (elems) {
      var itemElems = this._filterFindItemElements(elems);
      var Item = this.constructor.Item;

      // create new Outlayer Items for collection
      var items = [];
      for (var i = 0, len = itemElems.length; i < len; i++) {
        var elem = itemElems[i];
        var item = new Item(elem, this);
        items.push(item);
      }
      return items;
    };

    /**
     * get item elements to be used in layout
     * @param {Array or NodeList or HTMLElement} elems
     * @returns {Array} items - item elements
     */
    Outlayer.prototype._filterFindItemElements = function (elems) {
      // make array of elems
      elems = makeArray(elems);
      var itemSelector = this.options.itemSelector;
      var itemElems = [];
      for (var i = 0, len = elems.length; i < len; i++) {
        var elem = elems[i];
        // check that elem is an actual element
        if (!isElement(elem)) {
          continue;
        }
        // filter & find items if we have an item selector
        if (itemSelector) {
          // filter siblings
          if (matchesSelector(elem, itemSelector)) {
            itemElems.push(elem);
          }
          // find children
          var childElems = elem.querySelectorAll(itemSelector);
          // concat childElems to filterFound array
          for (var j = 0, jLen = childElems.length; j < jLen; j++) {
            itemElems.push(childElems[j]);
          }
        } else {
          itemElems.push(elem);
        }
      }
      return itemElems;
    };

    /**
     * getter method for getting item elements
     * @returns {Array} elems - collection of item elements
     */
    Outlayer.prototype.getItemElements = function () {
      var elems = [];
      for (var i = 0, len = this.items.length; i < len; i++) {
        elems.push(this.items[i].element);
      }
      return elems;
    };

    // ----- init & layout ----- //

    /**
     * lays out all items
     */
    Outlayer.prototype.layout = function () {
      this._resetLayout();
      this._manageStamps();

      // don't animate first layout
      var isInstant = this.options.isLayoutInstant !== undefined ? this.options.isLayoutInstant : !this._isLayoutInited;
      this.layoutItems(this.items, isInstant);

      // flag for initalized
      this._isLayoutInited = true;
    };

    // _init is alias for layout
    Outlayer.prototype._init = Outlayer.prototype.layout;

    /**
     * logic before any new layout
     */
    Outlayer.prototype._resetLayout = function () {
      this.getSize();
    };
    Outlayer.prototype.getSize = function () {
      this.size = getSize(this.element);
    };

    /**
     * get measurement from option, for columnWidth, rowHeight, gutter
     * if option is String -> get element from selector string, & get size of element
     * if option is Element -> get size of element
     * else use option as a number
     *
     * @param {String} measurement
     * @param {String} size - width or height
     * @private
     */
    Outlayer.prototype._getMeasurement = function (measurement, size) {
      var option = this.options[measurement];
      var elem;
      if (!option) {
        // default to 0
        this[measurement] = 0;
      } else {
        // use option as an element
        if (typeof option === 'string') {
          elem = this.element.querySelector(option);
        } else if (isElement(option)) {
          elem = option;
        }
        // use size of element, if element
        this[measurement] = elem ? getSize(elem)[size] : option;
      }
    };

    /**
     * layout a collection of item elements
     * @api public
     */
    Outlayer.prototype.layoutItems = function (items, isInstant) {
      items = this._getItemsForLayout(items);
      this._layoutItems(items, isInstant);
      this._postLayout();
    };

    /**
     * get the items to be laid out
     * you may want to skip over some items
     * @param {Array} items
     * @returns {Array} items
     */
    Outlayer.prototype._getItemsForLayout = function (items) {
      var layoutItems = [];
      for (var i = 0, len = items.length; i < len; i++) {
        var item = items[i];
        if (!item.isIgnored) {
          layoutItems.push(item);
        }
      }
      return layoutItems;
    };

    /**
     * layout items
     * @param {Array} items
     * @param {Boolean} isInstant
     */
    Outlayer.prototype._layoutItems = function (items, isInstant) {
      var _this = this;
      function onItemsLayout() {
        _this.emitEvent('layoutComplete', [_this, items]);
      }
      if (!items || !items.length) {
        // no items, emit event with empty array
        onItemsLayout();
        return;
      }

      // emit layoutComplete when done
      this._itemsOn(items, 'layout', onItemsLayout);
      var queue = [];
      for (var i = 0, len = items.length; i < len; i++) {
        var item = items[i];
        // get x/y object from method
        var position = this._getItemLayoutPosition(item);
        // enqueue
        position.item = item;
        position.isInstant = isInstant || item.isLayoutInstant;
        queue.push(position);
      }
      this._processLayoutQueue(queue);
    };

    /**
     * get item layout position
     * @param {Outlayer.Item} item
     * @returns {Object} x and y position
     */
    Outlayer.prototype._getItemLayoutPosition = function /* item */
    () {
      return {
        x: 0,
        y: 0
      };
    };

    /**
     * iterate over array and position each item
     * Reason being - separating this logic prevents 'layout invalidation'
     * thx @paul_irish
     * @param {Array} queue
     */
    Outlayer.prototype._processLayoutQueue = function (queue) {
      for (var i = 0, len = queue.length; i < len; i++) {
        var obj = queue[i];
        this._positionItem(obj.item, obj.x, obj.y, obj.isInstant);
      }
    };

    /**
     * Sets position of item in DOM
     * @param {Outlayer.Item} item
     * @param {Number} x - horizontal position
     * @param {Number} y - vertical position
     * @param {Boolean} isInstant - disables transitions
     */
    Outlayer.prototype._positionItem = function (item, x, y, isInstant) {
      if (isInstant) {
        // if not transition, just set CSS
        item.goTo(x, y);
      } else {
        item.moveTo(x, y);
      }
    };

    /**
     * Any logic you want to do after each layout,
     * i.e. size the container
     */
    Outlayer.prototype._postLayout = function () {
      this.resizeContainer();
    };
    Outlayer.prototype.resizeContainer = function () {
      if (!this.options.isResizingContainer) {
        return;
      }
      var size = this._getContainerSize();
      if (size) {
        this._setContainerMeasure(size.width, true);
        this._setContainerMeasure(size.height, false);
      }
    };

    /**
     * Sets width or height of container if returned
     * @returns {Object} size
     *   @param {Number} width
     *   @param {Number} height
     */
    Outlayer.prototype._getContainerSize = noop;

    /**
     * @param {Number} measure - size of width or height
     * @param {Boolean} isWidth
     */
    Outlayer.prototype._setContainerMeasure = function (measure, isWidth) {
      if (measure === undefined) {
        return;
      }
      var elemSize = this.size;
      // add padding and border width if border box
      if (elemSize.isBorderBox) {
        measure += isWidth ? elemSize.paddingLeft + elemSize.paddingRight + elemSize.borderLeftWidth + elemSize.borderRightWidth : elemSize.paddingBottom + elemSize.paddingTop + elemSize.borderTopWidth + elemSize.borderBottomWidth;
      }
      measure = Math.max(measure, 0);
      this.element.style[isWidth ? 'width' : 'height'] = measure + 'px';
    };

    /**
     * trigger a callback for a collection of items events
     * @param {Array} items - Outlayer.Items
     * @param {String} eventName
     * @param {Function} callback
     */
    Outlayer.prototype._itemsOn = function (items, eventName, callback) {
      var doneCount = 0;
      var count = items.length;
      // event callback
      var _this = this;
      function tick() {
        doneCount++;
        if (doneCount === count) {
          callback.call(_this);
        }
        return true; // bind once
      }
      // bind callback
      for (var i = 0, len = items.length; i < len; i++) {
        var item = items[i];
        item.on(eventName, tick);
      }
    };

    // -------------------------- ignore & stamps -------------------------- //

    /**
     * keep item in collection, but do not lay it out
     * ignored items do not get skipped in layout
     * @param {Element} elem
     */
    Outlayer.prototype.ignore = function (elem) {
      var item = this.getItem(elem);
      if (item) {
        item.isIgnored = true;
      }
    };

    /**
     * return item to layout collection
     * @param {Element} elem
     */
    Outlayer.prototype.unignore = function (elem) {
      var item = this.getItem(elem);
      if (item) {
        delete item.isIgnored;
      }
    };

    /**
     * adds elements to stamps
     * @param {NodeList, Array, Element, or String} elems
     */
    Outlayer.prototype.stamp = function (elems) {
      elems = this._find(elems);
      if (!elems) {
        return;
      }
      this.stamps = this.stamps.concat(elems);
      // ignore
      for (var i = 0, len = elems.length; i < len; i++) {
        var elem = elems[i];
        this.ignore(elem);
      }
    };

    /**
     * removes elements to stamps
     * @param {NodeList, Array, or Element} elems
     */
    Outlayer.prototype.unstamp = function (elems) {
      elems = this._find(elems);
      if (!elems) {
        return;
      }
      for (var i = 0, len = elems.length; i < len; i++) {
        var elem = elems[i];
        // filter out removed stamp elements
        removeFrom(elem, this.stamps);
        this.unignore(elem);
      }
    };

    /**
     * finds child elements
     * @param {NodeList, Array, Element, or String} elems
     * @returns {Array} elems
     */
    Outlayer.prototype._find = function (elems) {
      if (!elems) {
        return;
      }
      // if string, use argument as selector string
      if (typeof elems === 'string') {
        elems = this.element.querySelectorAll(elems);
      }
      elems = makeArray(elems);
      return elems;
    };
    Outlayer.prototype._manageStamps = function () {
      if (!this.stamps || !this.stamps.length) {
        return;
      }
      this._getBoundingRect();
      for (var i = 0, len = this.stamps.length; i < len; i++) {
        var stamp = this.stamps[i];
        this._manageStamp(stamp);
      }
    };

    // update boundingLeft / Top
    Outlayer.prototype._getBoundingRect = function () {
      // get bounding rect for container element
      var boundingRect = this.element.getBoundingClientRect();
      var size = this.size;
      this._boundingRect = {
        left: boundingRect.left + size.paddingLeft + size.borderLeftWidth,
        top: boundingRect.top + size.paddingTop + size.borderTopWidth,
        right: boundingRect.right - (size.paddingRight + size.borderRightWidth),
        bottom: boundingRect.bottom - (size.paddingBottom + size.borderBottomWidth)
      };
    };

    /**
     * @param {Element} stamp
    **/
    Outlayer.prototype._manageStamp = noop;

    /**
     * get x/y position of element relative to container element
     * @param {Element} elem
     * @returns {Object} offset - has left, top, right, bottom
     */
    Outlayer.prototype._getElementOffset = function (elem) {
      var boundingRect = elem.getBoundingClientRect();
      var thisRect = this._boundingRect;
      var size = getSize(elem);
      var offset = {
        left: boundingRect.left - thisRect.left - size.marginLeft,
        top: boundingRect.top - thisRect.top - size.marginTop,
        right: thisRect.right - boundingRect.right - size.marginRight,
        bottom: thisRect.bottom - boundingRect.bottom - size.marginBottom
      };
      return offset;
    };

    // -------------------------- resize -------------------------- //

    // enable event handlers for listeners
    // i.e. resize -> onresize
    Outlayer.prototype.handleEvent = function (event) {
      var method = 'on' + event.type;
      if (this[method]) {
        this[method](event);
      }
    };

    /**
     * Bind layout to window resizing
     */
    Outlayer.prototype.bindResize = function () {
      // bind just one listener
      if (this.isResizeBound) {
        return;
      }
      eventie.bind(window, 'resize', this);
      this.isResizeBound = true;
    };

    /**
     * Unbind layout to window resizing
     */
    Outlayer.prototype.unbindResize = function () {
      if (this.isResizeBound) {
        eventie.unbind(window, 'resize', this);
      }
      this.isResizeBound = false;
    };

    // original debounce by John Hann
    // http://unscriptable.com/index.php/2009/03/20/debouncing-javascript-methods/

    // this fires every resize
    Outlayer.prototype.onresize = function () {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
      }
      var _this = this;
      function delayed() {
        _this.resize();
        delete _this.resizeTimeout;
      }
      this.resizeTimeout = setTimeout(delayed, 100);
    };

    // debounced, layout on resize
    Outlayer.prototype.resize = function () {
      // don't trigger if size did not change
      // or if resize was unbound. See #9
      if (!this.isResizeBound || !this.needsResizeLayout()) {
        return;
      }
      this.layout();
    };

    /**
     * check if layout is needed post layout
     * @returns Boolean
     */
    Outlayer.prototype.needsResizeLayout = function () {
      var size = getSize(this.element);
      // check that this.size and size are there
      // IE8 triggers resize on body size change, so they might not be
      var hasSizes = this.size && size;
      return hasSizes && size.innerWidth !== this.size.innerWidth;
    };

    // -------------------------- methods -------------------------- //

    /**
     * add items to Outlayer instance
     * @param {Array or NodeList or Element} elems
     * @returns {Array} items - Outlayer.Items
    **/
    Outlayer.prototype.addItems = function (elems) {
      var items = this._itemize(elems);
      // add items to collection
      if (items.length) {
        this.items = this.items.concat(items);
      }
      return items;
    };

    /**
     * Layout newly-appended item elements
     * @param {Array or NodeList or Element} elems
     */
    Outlayer.prototype.appended = function (elems) {
      var items = this.addItems(elems);
      if (!items.length) {
        return;
      }
      // layout and reveal just the new items
      this.layoutItems(items, true);
      this.reveal(items);
    };

    /**
     * Layout prepended elements
     * @param {Array or NodeList or Element} elems
     */
    Outlayer.prototype.prepended = function (elems) {
      var items = this._itemize(elems);
      if (!items.length) {
        return;
      }
      // add items to beginning of collection
      var previousItems = this.items.slice(0);
      this.items = items.concat(previousItems);
      // start new layout
      this._resetLayout();
      this._manageStamps();
      // layout new stuff without transition
      this.layoutItems(items, true);
      this.reveal(items);
      // layout previous items
      this.layoutItems(previousItems);
    };

    /**
     * reveal a collection of items
     * @param {Array of Outlayer.Items} items
     */
    Outlayer.prototype.reveal = function (items) {
      var len = items && items.length;
      if (!len) {
        return;
      }
      for (var i = 0; i < len; i++) {
        var item = items[i];
        item.reveal();
      }
    };

    /**
     * hide a collection of items
     * @param {Array of Outlayer.Items} items
     */
    Outlayer.prototype.hide = function (items) {
      var len = items && items.length;
      if (!len) {
        return;
      }
      for (var i = 0; i < len; i++) {
        var item = items[i];
        item.hide();
      }
    };

    /**
     * get Outlayer.Item, given an Element
     * @param {Element} elem
     * @param {Function} callback
     * @returns {Outlayer.Item} item
     */
    Outlayer.prototype.getItem = function (elem) {
      // loop through items to get the one that matches
      for (var i = 0, len = this.items.length; i < len; i++) {
        var item = this.items[i];
        if (item.element === elem) {
          // return item
          return item;
        }
      }
    };

    /**
     * get collection of Outlayer.Items, given Elements
     * @param {Array} elems
     * @returns {Array} items - Outlayer.Items
     */
    Outlayer.prototype.getItems = function (elems) {
      if (!elems || !elems.length) {
        return;
      }
      var items = [];
      for (var i = 0, len = elems.length; i < len; i++) {
        var elem = elems[i];
        var item = this.getItem(elem);
        if (item) {
          items.push(item);
        }
      }
      return items;
    };

    /**
     * remove element(s) from instance and DOM
     * @param {Array or NodeList or Element} elems
     */
    Outlayer.prototype.remove = function (elems) {
      elems = makeArray(elems);
      var removeItems = this.getItems(elems);
      // bail if no items to remove
      if (!removeItems || !removeItems.length) {
        return;
      }
      this._itemsOn(removeItems, 'remove', function () {
        this.emitEvent('removeComplete', [this, removeItems]);
      });
      for (var i = 0, len = removeItems.length; i < len; i++) {
        var item = removeItems[i];
        item.remove();
        // remove item from collection
        removeFrom(item, this.items);
      }
    };

    // ----- destroy ----- //

    // remove and disable Outlayer instance
    Outlayer.prototype.destroy = function () {
      // clean up dynamic styles
      var style = this.element.style;
      style.height = '';
      style.position = '';
      style.width = '';
      // destroy items
      for (var i = 0, len = this.items.length; i < len; i++) {
        var item = this.items[i];
        item.destroy();
      }
      this.unbindResize();
      var id = this.element.outlayerGUID;
      delete instances[id]; // remove reference to instance by id
      delete this.element.outlayerGUID;
      // remove data for jQuery
      if (jQuery) {
        jQuery.removeData(this.element, this.constructor.namespace);
      }
    };

    // -------------------------- data -------------------------- //

    /**
     * get Outlayer instance from element
     * @param {Element} elem
     * @returns {Outlayer}
     */
    Outlayer.data = function (elem) {
      var id = elem && elem.outlayerGUID;
      return id && instances[id];
    };

    // -------------------------- create Outlayer class -------------------------- //

    /**
     * create a layout class
     * @param {String} namespace
     */
    Outlayer.create = function (namespace, options) {
      // sub-class Outlayer
      function Layout() {
        Outlayer.apply(this, arguments);
      }
      // inherit Outlayer prototype, use Object.create if there
      if (Object.create) {
        Layout.prototype = Object.create(Outlayer.prototype);
      } else {
        extend(Layout.prototype, Outlayer.prototype);
      }
      // set contructor, used for namespace and Item
      Layout.prototype.constructor = Layout;
      Layout.defaults = extend({}, Outlayer.defaults);
      // apply new options
      extend(Layout.defaults, options);
      // keep prototype.settings for backwards compatibility (Packery v1.2.0)
      Layout.prototype.settings = {};
      Layout.namespace = namespace;
      Layout.data = Outlayer.data;

      // sub-class Item
      Layout.Item = function LayoutItem() {
        Item.apply(this, arguments);
      };
      Layout.Item.prototype = new Item();

      // -------------------------- declarative -------------------------- //

      /**
       * allow user to initialize Outlayer via .js-namespace class
       * options are parsed from data-namespace-option attribute
       */
      docReady(function () {
        var dashedNamespace = toDashed(namespace);
        var elems = document.querySelectorAll('.js-' + dashedNamespace);
        var dataAttr = 'data-' + dashedNamespace + '-options';
        for (var i = 0, len = elems.length; i < len; i++) {
          var elem = elems[i];
          var attr = elem.getAttribute(dataAttr);
          var options;
          try {
            options = attr && JSON.parse(attr);
          } catch (error) {
            // log error, do not initialize
            if (console) {
              console.error('Error parsing ' + dataAttr + ' on ' + elem.nodeName.toLowerCase() + (elem.id ? '#' + elem.id : '') + ': ' + error);
            }
            continue;
          }
          // initialize
          var instance = new Layout(elem, options);
          // make available via $().data('layoutname')
          if (jQuery) {
            jQuery.data(elem, namespace, instance);
          }
        }
      });

      // -------------------------- jQuery bridge -------------------------- //

      // make into jQuery plugin
      if (jQuery && jQuery.bridget) {
        jQuery.bridget(namespace, Layout);
      }
      return Layout;
    };

    // ----- fin ----- //

    // back in global
    Outlayer.Item = Item;
    return Outlayer;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_1__, __WEBPACK_LOCAL_MODULE_2__, __WEBPACK_LOCAL_MODULE_3__, __WEBPACK_LOCAL_MODULE_5__, __WEBPACK_LOCAL_MODULE_6__, __WEBPACK_LOCAL_MODULE_7__], __WEBPACK_AMD_DEFINE_FACTORY__ = (outlayerDefinition),
				__WEBPACK_LOCAL_MODULE_8__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/**
 * Isotope Item
**/

(function (window) {
  // -------------------------- Item -------------------------- //

  function itemDefinition(Outlayer) {
    // sub-class Outlayer Item
    function Item() {
      Outlayer.Item.apply(this, arguments);
    }
    Item.prototype = new Outlayer.Item();
    Item.prototype._create = function () {
      // assign id, used for original-order sorting
      this.id = this.layout.itemGUID++;
      Outlayer.Item.prototype._create.call(this);
      this.sortData = {};
    };
    Item.prototype.updateSortData = function () {
      if (this.isIgnored) {
        return;
      }
      // default sorters
      this.sortData.id = this.id;
      // for backward compatibility
      this.sortData['original-order'] = this.id;
      this.sortData.random = Math.random();
      // go thru getSortData obj and apply the sorters
      var getSortData = this.layout.options.getSortData;
      var sorters = this.layout._sorters;
      for (var key in getSortData) {
        var sorter = sorters[key];
        this.sortData[key] = sorter(this.element, this);
      }
    };
    var _destroy = Item.prototype.destroy;
    Item.prototype.destroy = function () {
      // call super
      _destroy.apply(this, arguments);
      // reset display, #741
      this.css({
        display: ''
      });
    };
    return Item;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_8__], __WEBPACK_AMD_DEFINE_FACTORY__ = (itemDefinition),
				__WEBPACK_LOCAL_MODULE_9__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);
(function (window) {
  // --------------------------  -------------------------- //

  function layoutModeDefinition(getSize, Outlayer) {
    // layout mode class
    function LayoutMode(isotope) {
      this.isotope = isotope;
      // link properties
      if (isotope) {
        this.options = isotope.options[this.namespace];
        this.element = isotope.element;
        this.items = isotope.filteredItems;
        this.size = isotope.size;
      }
    }

    /**
     * some methods should just defer to default Outlayer method
     * and reference the Isotope instance as `this`
    **/
    (function () {
      var facadeMethods = ['_resetLayout', '_getItemLayoutPosition', '_manageStamp', '_getContainerSize', '_getElementOffset', 'needsResizeLayout'];
      for (var i = 0, len = facadeMethods.length; i < len; i++) {
        var methodName = facadeMethods[i];
        LayoutMode.prototype[methodName] = getOutlayerMethod(methodName);
      }
      function getOutlayerMethod(methodName) {
        return function () {
          return Outlayer.prototype[methodName].apply(this.isotope, arguments);
        };
      }
    })();

    // -----  ----- //

    // for horizontal layout modes, check vertical size
    LayoutMode.prototype.needsVerticalResizeLayout = function () {
      // don't trigger if size did not change
      var size = getSize(this.isotope.element);
      // check that this.size and size are there
      // IE8 triggers resize on body size change, so they might not be
      var hasSizes = this.isotope.size && size;
      return hasSizes && size.innerHeight !== this.isotope.size.innerHeight;
    };

    // ----- measurements ----- //

    LayoutMode.prototype._getMeasurement = function () {
      this.isotope._getMeasurement.apply(this, arguments);
    };
    LayoutMode.prototype.getColumnWidth = function () {
      this.getSegmentSize('column', 'Width');
    };
    LayoutMode.prototype.getRowHeight = function () {
      this.getSegmentSize('row', 'Height');
    };

    /**
     * get columnWidth or rowHeight
     * segment: 'column' or 'row'
     * size 'Width' or 'Height'
    **/
    LayoutMode.prototype.getSegmentSize = function (segment, size) {
      var segmentName = segment + size;
      var outerSize = 'outer' + size;
      // columnWidth / outerWidth // rowHeight / outerHeight
      this._getMeasurement(segmentName, outerSize);
      // got rowHeight or columnWidth, we can chill
      if (this[segmentName]) {
        return;
      }
      // fall back to item of first element
      var firstItemSize = this.getFirstItemSize();
      this[segmentName] = firstItemSize && firstItemSize[outerSize] ||
      // or size of container
      this.isotope.size['inner' + size];
    };
    LayoutMode.prototype.getFirstItemSize = function () {
      var firstItem = this.isotope.filteredItems[0];
      return firstItem && firstItem.element && getSize(firstItem.element);
    };

    // ----- methods that should reference isotope ----- //

    LayoutMode.prototype.layout = function () {
      this.isotope.layout.apply(this.isotope, arguments);
    };
    LayoutMode.prototype.getSize = function () {
      this.isotope.getSize();
      this.size = this.isotope.size;
    };

    // -------------------------- create -------------------------- //

    LayoutMode.modes = {};
    LayoutMode.create = function (namespace, options) {
      function Mode() {
        LayoutMode.apply(this, arguments);
      }
      Mode.prototype = new LayoutMode();

      // default options
      if (options) {
        Mode.options = options;
      }
      Mode.prototype.namespace = namespace;
      // register in Isotope
      LayoutMode.modes[namespace] = Mode;
      return Mode;
    };
    return LayoutMode;
  }
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_5__, __WEBPACK_LOCAL_MODULE_8__], __WEBPACK_AMD_DEFINE_FACTORY__ = (layoutModeDefinition),
				__WEBPACK_LOCAL_MODULE_10__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/*!
 * Masonry v3.2.1
 * Cascading grid layout library
 * http://masonry.desandro.com
 * MIT License
 * by David DeSandro
 */

(function (window) {
  // -------------------------- helpers -------------------------- //

  var indexOf = Array.prototype.indexOf ? function (items, value) {
    return items.indexOf(value);
  } : function (items, value) {
    for (var i = 0, len = items.length; i < len; i++) {
      var item = items[i];
      if (item === value) {
        return i;
      }
    }
    return -1;
  };

  // -------------------------- masonryDefinition -------------------------- //

  // used for AMD definition and requires
  function masonryDefinition(Outlayer, getSize) {
    // create an Outlayer layout class
    var Masonry = Outlayer.create('masonry');
    Masonry.prototype._resetLayout = function () {
      this.getSize();
      this._getMeasurement('columnWidth', 'outerWidth');
      this._getMeasurement('gutter', 'outerWidth');
      this.measureColumns();

      // reset column Y
      var i = this.cols;
      this.colYs = [];
      while (i--) {
        this.colYs.push(0);
      }
      this.maxY = 0;
    };
    Masonry.prototype.measureColumns = function () {
      this.getContainerWidth();
      // if columnWidth is 0, default to outerWidth of first item
      if (!this.columnWidth) {
        var firstItem = this.items[0];
        var firstItemElem = firstItem && firstItem.element;
        // columnWidth fall back to item of first element
        this.columnWidth = firstItemElem && getSize(firstItemElem).outerWidth ||
        // if first elem has no width, default to size of container
        this.containerWidth;
      }
      this.columnWidth += this.gutter;
      this.cols = Math.floor((this.containerWidth + this.gutter) / this.columnWidth);
      this.cols = Math.max(this.cols, 1);
    };
    Masonry.prototype.getContainerWidth = function () {
      // container is parent if fit width
      var container = this.options.isFitWidth ? this.element.parentNode : this.element;
      // check that this.size and size are there
      // IE8 triggers resize on body size change, so they might not be
      var size = getSize(container);
      this.containerWidth = size && size.innerWidth;
    };
    Masonry.prototype._getItemLayoutPosition = function (item) {
      item.getSize();
      // how many columns does this brick span
      var remainder = item.size.outerWidth % this.columnWidth;
      var mathMethod = remainder && remainder < 1 ? 'round' : 'ceil';
      // round if off by 1 pixel, otherwise use ceil
      var colSpan = Math[mathMethod](item.size.outerWidth / this.columnWidth);
      colSpan = Math.min(colSpan, this.cols);
      var colGroup = this._getColGroup(colSpan);
      // get the minimum Y value from the columns
      var minimumY = Math.min.apply(Math, colGroup);
      var shortColIndex = indexOf(colGroup, minimumY);

      // position the brick
      var position = {
        x: this.columnWidth * shortColIndex,
        y: minimumY
      };

      // apply setHeight to necessary columns
      var setHeight = minimumY + item.size.outerHeight;
      var setSpan = this.cols + 1 - colGroup.length;
      for (var i = 0; i < setSpan; i++) {
        this.colYs[shortColIndex + i] = setHeight;
      }
      return position;
    };

    /**
     * @param {Number} colSpan - number of columns the element spans
     * @returns {Array} colGroup
     */
    Masonry.prototype._getColGroup = function (colSpan) {
      if (colSpan < 2) {
        // if brick spans only one column, use all the column Ys
        return this.colYs;
      }
      var colGroup = [];
      // how many different places could this brick fit horizontally
      var groupCount = this.cols + 1 - colSpan;
      // for each group potential horizontal position
      for (var i = 0; i < groupCount; i++) {
        // make an array of colY values for that one group
        var groupColYs = this.colYs.slice(i, i + colSpan);
        // and get the max value of the array
        colGroup[i] = Math.max.apply(Math, groupColYs);
      }
      return colGroup;
    };
    Masonry.prototype._manageStamp = function (stamp) {
      var stampSize = getSize(stamp);
      var offset = this._getElementOffset(stamp);
      // get the columns that this stamp affects
      var firstX = this.options.isOriginLeft ? offset.left : offset.right;
      var lastX = firstX + stampSize.outerWidth;
      var firstCol = Math.floor(firstX / this.columnWidth);
      firstCol = Math.max(0, firstCol);
      var lastCol = Math.floor(lastX / this.columnWidth);
      // lastCol should not go over if multiple of columnWidth #425
      lastCol -= lastX % this.columnWidth ? 0 : 1;
      lastCol = Math.min(this.cols - 1, lastCol);
      // set colYs to bottom of the stamp
      var stampMaxY = (this.options.isOriginTop ? offset.top : offset.bottom) + stampSize.outerHeight;
      for (var i = firstCol; i <= lastCol; i++) {
        this.colYs[i] = Math.max(stampMaxY, this.colYs[i]);
      }
    };
    Masonry.prototype._getContainerSize = function () {
      this.maxY = Math.max.apply(Math, this.colYs);
      var size = {
        height: this.maxY
      };
      if (this.options.isFitWidth) {
        size.width = this._getContainerFitWidth();
      }
      return size;
    };
    Masonry.prototype._getContainerFitWidth = function () {
      var unusedCols = 0;
      // count unused columns
      var i = this.cols;
      while (--i) {
        if (this.colYs[i] !== 0) {
          break;
        }
        unusedCols++;
      }
      // fit container to columns that have been used
      return (this.cols - unusedCols) * this.columnWidth - this.gutter;
    };
    Masonry.prototype.needsResizeLayout = function () {
      var previousWidth = this.containerWidth;
      this.getContainerWidth();
      return previousWidth !== this.containerWidth;
    };
    return Masonry;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_8__, __WEBPACK_LOCAL_MODULE_5__], __WEBPACK_AMD_DEFINE_FACTORY__ = (masonryDefinition),
				__WEBPACK_LOCAL_MODULE_11__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/*!
 * Masonry layout mode
 * sub-classes Masonry
 * http://masonry.desandro.com
 */

(function (window) {
  // -------------------------- helpers -------------------------- //

  // extend objects
  function extend(a, b) {
    for (var prop in b) {
      a[prop] = b[prop];
    }
    return a;
  }

  // -------------------------- masonryDefinition -------------------------- //

  // used for AMD definition and requires
  function masonryDefinition(LayoutMode, Masonry) {
    // create an Outlayer layout class
    var MasonryMode = LayoutMode.create('masonry');

    // save on to these methods
    var _getElementOffset = MasonryMode.prototype._getElementOffset;
    var layout = MasonryMode.prototype.layout;
    var _getMeasurement = MasonryMode.prototype._getMeasurement;

    // sub-class Masonry
    extend(MasonryMode.prototype, Masonry.prototype);

    // set back, as it was overwritten by Masonry
    MasonryMode.prototype._getElementOffset = _getElementOffset;
    MasonryMode.prototype.layout = layout;
    MasonryMode.prototype._getMeasurement = _getMeasurement;
    var measureColumns = MasonryMode.prototype.measureColumns;
    MasonryMode.prototype.measureColumns = function () {
      // set items, used if measuring first item
      this.items = this.isotope.filteredItems;
      measureColumns.call(this);
    };

    // HACK copy over isOriginLeft/Top options
    var _manageStamp = MasonryMode.prototype._manageStamp;
    MasonryMode.prototype._manageStamp = function () {
      this.options.isOriginLeft = this.isotope.options.isOriginLeft;
      this.options.isOriginTop = this.isotope.options.isOriginTop;
      _manageStamp.apply(this, arguments);
    };
    return MasonryMode;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_10__, __WEBPACK_LOCAL_MODULE_11__], __WEBPACK_AMD_DEFINE_FACTORY__ = (masonryDefinition),
				__WEBPACK_LOCAL_MODULE_12__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);
(function (window) {
  function fitRowsDefinition(LayoutMode) {
    var FitRows = LayoutMode.create('fitRows');
    FitRows.prototype._resetLayout = function () {
      this.x = 0;
      this.y = 0;
      this.maxY = 0;
      this._getMeasurement('gutter', 'outerWidth');
    };
    FitRows.prototype._getItemLayoutPosition = function (item) {
      item.getSize();
      var itemWidth = item.size.outerWidth + this.gutter;
      // if this element cannot fit in the current row
      var containerWidth = this.isotope.size.innerWidth + this.gutter;
      if (this.x !== 0 && itemWidth + this.x > containerWidth) {
        this.x = 0;
        this.y = this.maxY;
      }
      var position = {
        x: this.x,
        y: this.y
      };
      this.maxY = Math.max(this.maxY, this.y + item.size.outerHeight);
      this.x += itemWidth;
      return position;
    };
    FitRows.prototype._getContainerSize = function () {
      return {
        height: this.maxY
      };
    };
    return FitRows;
  }
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_10__], __WEBPACK_AMD_DEFINE_FACTORY__ = (fitRowsDefinition),
				__WEBPACK_LOCAL_MODULE_13__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);
(function (window) {
  function verticalDefinition(LayoutMode) {
    var Vertical = LayoutMode.create('vertical', {
      horizontalAlignment: 0
    });
    Vertical.prototype._resetLayout = function () {
      this.y = 0;
    };
    Vertical.prototype._getItemLayoutPosition = function (item) {
      item.getSize();
      var x = (this.isotope.size.innerWidth - item.size.outerWidth) * this.options.horizontalAlignment;
      var y = this.y;
      this.y += item.size.outerHeight;
      return {
        x: x,
        y: y
      };
    };
    Vertical.prototype._getContainerSize = function () {
      return {
        height: this.y
      };
    };
    return Vertical;
  }
  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_10__], __WEBPACK_AMD_DEFINE_FACTORY__ = (verticalDefinition),
				__WEBPACK_LOCAL_MODULE_14__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__));
  } else {}
})(window);

/*!
 * Isotope v2.1.0
 * Filter & sort magical layouts
 * http://isotope.metafizzy.co
 */

(function (window) {
  // -------------------------- vars -------------------------- //

  var jQuery = window.jQuery;

  // -------------------------- helpers -------------------------- //

  // extend objects
  function extend(a, b) {
    for (var prop in b) {
      a[prop] = b[prop];
    }
    return a;
  }
  var trim = String.prototype.trim ? function (str) {
    return str.trim();
  } : function (str) {
    return str.replace(/^\s+|\s+$/g, '');
  };
  var docElem = document.documentElement;
  var getText = docElem.textContent ? function (elem) {
    return elem.textContent;
  } : function (elem) {
    return elem.innerText;
  };
  var objToString = Object.prototype.toString;
  function isArray(obj) {
    return objToString.call(obj) === '[object Array]';
  }

  // index of helper cause IE8
  var indexOf = Array.prototype.indexOf ? function (ary, obj) {
    return ary.indexOf(obj);
  } : function (ary, obj) {
    for (var i = 0, len = ary.length; i < len; i++) {
      if (ary[i] === obj) {
        return i;
      }
    }
    return -1;
  };

  // turn element or nodeList into an array
  function makeArray(obj) {
    var ary = [];
    if (isArray(obj)) {
      // use object if already an array
      ary = obj;
    } else if (obj && typeof obj.length === 'number') {
      // convert nodeList to array
      for (var i = 0, len = obj.length; i < len; i++) {
        ary.push(obj[i]);
      }
    } else {
      // array of single index
      ary.push(obj);
    }
    return ary;
  }
  function removeFrom(obj, ary) {
    var index = indexOf(ary, obj);
    if (index !== -1) {
      ary.splice(index, 1);
    }
  }

  // -------------------------- isotopeDefinition -------------------------- //

  // used for AMD definition and requires
  function isotopeDefinition(Outlayer, getSize, matchesSelector, Item, LayoutMode) {
    // create an Outlayer layout class
    var Isotope = Outlayer.create('isotope', {
      layoutMode: "masonry",
      isJQueryFiltering: true,
      sortAscending: true
    });
    Isotope.Item = Item;
    Isotope.LayoutMode = LayoutMode;
    Isotope.prototype._create = function () {
      this.itemGUID = 0;
      // functions that sort items
      this._sorters = {};
      this._getSorters();
      // call super
      Outlayer.prototype._create.call(this);

      // create layout modes
      this.modes = {};
      // start filteredItems with all items
      this.filteredItems = this.items;
      // keep of track of sortBys
      this.sortHistory = ['original-order'];
      // create from registered layout modes
      for (var name in LayoutMode.modes) {
        this._initLayoutMode(name);
      }
    };
    Isotope.prototype.reloadItems = function () {
      // reset item ID counter
      this.itemGUID = 0;
      // call super
      Outlayer.prototype.reloadItems.call(this);
    };
    Isotope.prototype._itemize = function () {
      var items = Outlayer.prototype._itemize.apply(this, arguments);
      // assign ID for original-order
      for (var i = 0, len = items.length; i < len; i++) {
        var item = items[i];
        item.id = this.itemGUID++;
      }
      this._updateItemsSortData(items);
      return items;
    };

    // -------------------------- layout -------------------------- //

    Isotope.prototype._initLayoutMode = function (name) {
      var Mode = LayoutMode.modes[name];
      // set mode options
      // HACK extend initial options, back-fill in default options
      var initialOpts = this.options[name] || {};
      this.options[name] = Mode.options ? extend(Mode.options, initialOpts) : initialOpts;
      // init layout mode instance
      this.modes[name] = new Mode(this);
    };
    Isotope.prototype.layout = function () {
      // if first time doing layout, do all magic
      if (!this._isLayoutInited && this.options.isInitLayout) {
        this.arrange();
        return;
      }
      this._layout();
    };

    // private method to be used in layout() & magic()
    Isotope.prototype._layout = function () {
      // don't animate first layout
      var isInstant = this._getIsInstant();
      // layout flow
      this._resetLayout();
      this._manageStamps();
      this.layoutItems(this.filteredItems, isInstant);

      // flag for initalized
      this._isLayoutInited = true;
    };

    // filter + sort + layout
    Isotope.prototype.arrange = function (opts) {
      // set any options pass
      this.option(opts);
      this._getIsInstant();
      // filter, sort, and layout
      this.filteredItems = this._filter(this.items);
      this._sort();
      this._layout();
    };
    // alias to _init for main plugin method
    Isotope.prototype._init = Isotope.prototype.arrange;

    // HACK
    // Don't animate/transition first layout
    // Or don't animate/transition other layouts
    Isotope.prototype._getIsInstant = function () {
      var isInstant = this.options.isLayoutInstant !== undefined ? this.options.isLayoutInstant : !this._isLayoutInited;
      this._isInstant = isInstant;
      return isInstant;
    };

    // -------------------------- filter -------------------------- //

    Isotope.prototype._filter = function (items) {
      var filter = this.options.filter;
      filter = filter || '*';
      var matches = [];
      var hiddenMatched = [];
      var visibleUnmatched = [];
      var test = this._getFilterTest(filter);

      // test each item
      for (var i = 0, len = items.length; i < len; i++) {
        var item = items[i];
        if (item.isIgnored) {
          continue;
        }
        // add item to either matched or unmatched group
        var isMatched = test(item);
        // item.isFilterMatched = isMatched;
        // add to matches if its a match
        if (isMatched) {
          matches.push(item);
        }
        // add to additional group if item needs to be hidden or revealed
        if (isMatched && item.isHidden) {
          hiddenMatched.push(item);
        } else if (!isMatched && !item.isHidden) {
          visibleUnmatched.push(item);
        }
      }
      var _this = this;
      function hideReveal() {
        _this.reveal(hiddenMatched);
        _this.hide(visibleUnmatched);
      }
      if (this._isInstant) {
        this._noTransition(hideReveal);
      } else {
        hideReveal();
      }
      return matches;
    };

    // get a jQuery, function, or a matchesSelector test given the filter
    Isotope.prototype._getFilterTest = function (filter) {
      if (jQuery && this.options.isJQueryFiltering) {
        // use jQuery
        return function (item) {
          return jQuery(item.element).is(filter);
        };
      }
      if (typeof filter === 'function') {
        // use filter as function
        return function (item) {
          return filter(item.element);
        };
      }
      // default, use filter as selector string
      return function (item) {
        return matchesSelector(item.element, filter);
      };
    };

    // -------------------------- sorting -------------------------- //

    /**
     * @params {Array} elems
     * @public
     */
    Isotope.prototype.updateSortData = function (elems) {
      // get items
      var items;
      if (elems) {
        elems = makeArray(elems);
        items = this.getItems(elems);
      } else {
        // update all items if no elems provided
        items = this.items;
      }
      this._getSorters();
      this._updateItemsSortData(items);
    };
    Isotope.prototype._getSorters = function () {
      var getSortData = this.options.getSortData;
      for (var key in getSortData) {
        var sorter = getSortData[key];
        this._sorters[key] = mungeSorter(sorter);
      }
    };

    /**
     * @params {Array} items - of Isotope.Items
     * @private
     */
    Isotope.prototype._updateItemsSortData = function (items) {
      // do not update if no items
      var len = items && items.length;
      for (var i = 0; len && i < len; i++) {
        var item = items[i];
        item.updateSortData();
      }
    };

    // ----- munge sorter ----- //

    // encapsulate this, as we just need mungeSorter
    // other functions in here are just for munging
    var mungeSorter = function () {
      // add a magic layer to sorters for convienent shorthands
      // `.foo-bar` will use the text of .foo-bar querySelector
      // `[foo-bar]` will use attribute
      // you can also add parser
      // `.foo-bar parseInt` will parse that as a number
      function mungeSorter(sorter) {
        // if not a string, return function or whatever it is
        if (typeof sorter !== 'string') {
          return sorter;
        }
        // parse the sorter string
        var args = trim(sorter).split(' ');
        var query = args[0];
        // check if query looks like [an-attribute]
        var attrMatch = query.match(/^\[(.+)\]$/);
        var attr = attrMatch && attrMatch[1];
        var getValue = getValueGetter(attr, query);
        // use second argument as a parser
        var parser = Isotope.sortDataParsers[args[1]];
        // parse the value, if there was a parser
        sorter = parser ? function (elem) {
          return elem && parser(getValue(elem));
        } :
        // otherwise just return value
        function (elem) {
          return elem && getValue(elem);
        };
        return sorter;
      }

      // get an attribute getter, or get text of the querySelector
      function getValueGetter(attr, query) {
        var getValue;
        // if query looks like [foo-bar], get attribute
        if (attr) {
          getValue = function getValue(elem) {
            return elem.getAttribute(attr);
          };
        } else {
          // otherwise, assume its a querySelector, and get its text
          getValue = function getValue(elem) {
            var child = elem.querySelector(query);
            return child && getText(child);
          };
        }
        return getValue;
      }
      return mungeSorter;
    }();

    // parsers used in getSortData shortcut strings
    Isotope.sortDataParsers = {
      'parseInt': function (_parseInt) {
        function parseInt(_x) {
          return _parseInt.apply(this, arguments);
        }
        parseInt.toString = function () {
          return _parseInt.toString();
        };
        return parseInt;
      }(function (val) {
        return parseInt(val, 10);
      }),
      'parseFloat': function (_parseFloat) {
        function parseFloat(_x2) {
          return _parseFloat.apply(this, arguments);
        }
        parseFloat.toString = function () {
          return _parseFloat.toString();
        };
        return parseFloat;
      }(function (val) {
        return parseFloat(val);
      })
    };

    // ----- sort method ----- //

    // sort filteredItem order
    Isotope.prototype._sort = function () {
      var sortByOpt = this.options.sortBy;
      if (!sortByOpt) {
        return;
      }
      // concat all sortBy and sortHistory
      var sortBys = [].concat.apply(sortByOpt, this.sortHistory);
      // sort magic
      var itemSorter = getItemSorter(sortBys, this.options.sortAscending);
      this.filteredItems.sort(itemSorter);
      // keep track of sortBy History
      if (sortByOpt !== this.sortHistory[0]) {
        // add to front, oldest goes in last
        this.sortHistory.unshift(sortByOpt);
      }
    };

    // returns a function used for sorting
    function getItemSorter(sortBys, sortAsc) {
      return function sorter(itemA, itemB) {
        // cycle through all sortKeys
        for (var i = 0, len = sortBys.length; i < len; i++) {
          var sortBy = sortBys[i];
          var a = itemA.sortData[sortBy];
          var b = itemB.sortData[sortBy];
          if (a > b || a < b) {
            // if sortAsc is an object, use the value given the sortBy key
            var isAscending = sortAsc[sortBy] !== undefined ? sortAsc[sortBy] : sortAsc;
            var direction = isAscending ? 1 : -1;
            return (a > b ? 1 : -1) * direction;
          }
        }
        return 0;
      };
    }

    // -------------------------- methods -------------------------- //

    // get layout mode
    Isotope.prototype._mode = function () {
      var layoutMode = this.options.layoutMode;
      var mode = this.modes[layoutMode];
      if (!mode) {
        // TODO console.error
        throw new Error('No layout mode: ' + layoutMode);
      }
      // HACK sync mode's options
      // any options set after init for layout mode need to be synced
      mode.options = this.options[layoutMode];
      return mode;
    };
    Isotope.prototype._resetLayout = function () {
      // trigger original reset layout
      Outlayer.prototype._resetLayout.call(this);
      this._mode()._resetLayout();
    };
    Isotope.prototype._getItemLayoutPosition = function (item) {
      return this._mode()._getItemLayoutPosition(item);
    };
    Isotope.prototype._manageStamp = function (stamp) {
      this._mode()._manageStamp(stamp);
    };
    Isotope.prototype._getContainerSize = function () {
      return this._mode()._getContainerSize();
    };
    Isotope.prototype.needsResizeLayout = function () {
      return this._mode().needsResizeLayout();
    };

    // -------------------------- adding & removing -------------------------- //

    // HEADS UP overwrites default Outlayer appended
    Isotope.prototype.appended = function (elems) {
      var items = this.addItems(elems);
      if (!items.length) {
        return;
      }
      var filteredItems = this._filterRevealAdded(items);
      // add to filteredItems
      this.filteredItems = this.filteredItems.concat(filteredItems);
    };

    // HEADS UP overwrites default Outlayer prepended
    Isotope.prototype.prepended = function (elems) {
      var items = this._itemize(elems);
      if (!items.length) {
        return;
      }
      // add items to beginning of collection
      var previousItems = this.items.slice(0);
      this.items = items.concat(previousItems);
      // start new layout
      this._resetLayout();
      this._manageStamps();
      // layout new stuff without transition
      var filteredItems = this._filterRevealAdded(items);
      // layout previous items
      this.layoutItems(previousItems);
      // add to filteredItems
      this.filteredItems = filteredItems.concat(this.filteredItems);
    };
    Isotope.prototype._filterRevealAdded = function (items) {
      var filteredItems = this._noTransition(function () {
        return this._filter(items);
      });
      // layout and reveal just the new items
      this.layoutItems(filteredItems, true);
      this.reveal(filteredItems);
      return items;
    };

    /**
     * Filter, sort, and layout newly-appended item elements
     * @param {Array or NodeList or Element} elems
     */
    Isotope.prototype.insert = function (elems) {
      var items = this.addItems(elems);
      if (!items.length) {
        return;
      }
      // append item elements
      var i, item;
      var len = items.length;
      for (i = 0; i < len; i++) {
        item = items[i];
        this.element.appendChild(item.element);
      }
      // filter new stuff
      /*
      // this way adds hides new filtered items with NO transition
      // so user can't see if new hidden items have been inserted
      var filteredInsertItems;
      this._noTransition( function() {
        filteredInsertItems = this._filter( items );
        // hide all new items
        this.hide( filteredInsertItems );
      });
      // */
      // this way hides new filtered items with transition
      // so user at least sees that something has been added
      var filteredInsertItems = this._filter(items);
      // hide all newitems
      this._noTransition(function () {
        this.hide(filteredInsertItems);
      });
      // */
      // set flag
      for (i = 0; i < len; i++) {
        items[i].isLayoutInstant = true;
      }
      this.arrange();
      // reset flag
      for (i = 0; i < len; i++) {
        delete items[i].isLayoutInstant;
      }
      this.reveal(filteredInsertItems);
    };
    var _remove = Isotope.prototype.remove;
    Isotope.prototype.remove = function (elems) {
      elems = makeArray(elems);
      var removeItems = this.getItems(elems);
      // do regular thing
      _remove.call(this, elems);
      // bail if no items to remove
      if (!removeItems || !removeItems.length) {
        return;
      }
      // remove elems from filteredItems
      for (var i = 0, len = removeItems.length; i < len; i++) {
        var item = removeItems[i];
        // remove item from collection
        removeFrom(item, this.filteredItems);
      }
    };
    Isotope.prototype.shuffle = function () {
      // update random sortData
      for (var i = 0, len = this.items.length; i < len; i++) {
        var item = this.items[i];
        item.sortData.random = Math.random();
      }
      this.options.sortBy = 'random';
      this._sort();
      this._layout();
    };

    /**
     * trigger fn without transition
     * kind of hacky to have this in the first place
     * @param {Function} fn
     * @returns ret
     * @private
     */
    Isotope.prototype._noTransition = function (fn) {
      // save transitionDuration before disabling
      var transitionDuration = this.options.transitionDuration;
      // disable transition
      this.options.transitionDuration = 0;
      // do it
      var returnValue = fn.call(this);
      // re-enable transition for reveal
      this.options.transitionDuration = transitionDuration;
      return returnValue;
    };

    // ----- helper methods ----- //

    /**
     * getter method for getting filtered item elements
     * @returns {Array} elems - collection of item elements
     */
    Isotope.prototype.getFilteredItemElements = function () {
      var elems = [];
      for (var i = 0, len = this.filteredItems.length; i < len; i++) {
        elems.push(this.filteredItems[i].element);
      }
      return elems;
    };

    // -----  ----- //

    return Isotope;
  }

  // -------------------------- transport -------------------------- //

  if (true) {
    // AMD
    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_8__, __WEBPACK_LOCAL_MODULE_5__, __WEBPACK_LOCAL_MODULE_6__, __WEBPACK_LOCAL_MODULE_9__, __WEBPACK_LOCAL_MODULE_10__,
    // include default layout modes
    __WEBPACK_LOCAL_MODULE_12__, __WEBPACK_LOCAL_MODULE_13__, __WEBPACK_LOCAL_MODULE_14__], __WEBPACK_AMD_DEFINE_FACTORY__ = (isotopeDefinition),
				__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
  } else {}
})(window);

/***/ }),

/***/ "./resources/js/libs/default/jquery.passwordstrength.js":
/*!**************************************************************!*\
  !*** ./resources/js/libs/default/jquery.passwordstrength.js ***!
  \**************************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*
 * jQuery Password Strength Indicator Plugin v0.1.0
 *
 * https://www.humankode.com
 *
 * Copyright (c) 2016 Carlo van Wyk
 * Released under the MIT license
 */

(function ($) {
  $.fn.passwordStrength = function (options) {
    var defaults = $.extend({
      minimumChars: 8
    }, options);
    var parentContainer = this.parent();
    var progressHtml = "<div class='password-meter-wrapper progress'><div id='password-progress' class='progress-bar' role='progressbar' aria-valuenow='1' aria-valuemin='0' aria-valuemax='100' style='width:1%;'></div></div><div id='password-score' class='password-score'></div><div id='password-recommendation' class='password-recommendation'></div><input type='hidden' id='password-strength-score' value='0'>";
    $(progressHtml).insertAfter('input[type=password]:last');
    $('#password-score').text(defaults.defaultMessage);
    $('.password-meter-wrapper').hide();
    $('#password-score').hide();
    $(this).keyup(function (event) {
      $('.password-meter-wrapper').show();
      $('#password-score').show();
      var element = $(event.target);
      var password = element.val();
      if (password.length == 0) {
        $('#password-score').html('');
        $('#password-recommendation').html('');
        $('.progress').hide();
        $('#password-score').hide();
        $('#password-strength-score').val(0);
      } else {
        var score = calculatePasswordScore(password, defaults);
        $('#password-strength-score').val(score);
        $('.progress-bar').css('width', score + '%').attr('aria-valuenow', score);
        $('#password-recommendation').css('margin-top', '10px');
        if (score < 50) {
          $('#password-score').html('Weak password');
          $('#password-recommendation').html('<div id="password-recommendation-heading">Some tips for a strong password:</div><ul><li>Use at least 8 characters</li><li>Use upper and lower case characters</li><li>Use 1 or more numbers</li><li>Optionally use special characters</li></ul>');
          $('#password-progress').removeClass();
          $('#password-progress').addClass('progress-bar bg-danger');
        } else if (score <= 60) {
          $('#password-score').html('Normal password');
          $('#password-recommendation').html('<div id="password-recommendation-heading">For a stronger password:</div><ul><li>Use upper and lower case characters</li><li>Use 1 or more numbers</li><li>Use special characters for an even stronger password</li></ul>');
          $('#password-recommendation-heading').css('text-align', 'left');
          $('#password-progress').removeClass();
          $('#password-progress').addClass('progress-bar bg-warning');
        } else if (score <= 80) {
          $('#password-score').html('Strong password');
          $('#password-recommendation').html('<div id="password-recommendation-heading">For an even stronger password:</div><ul><li>Increase the lenghth of your password to 15-30 characters</li><li>Use 2 or more numbers</li><li>Use 2 or more special characters</li></ul>');
          $('#password-recommendation-heading').css('text-align', 'left');
          $('#password-progress').removeClass();
          $('#password-progress').addClass('progress-bar bg-info');
        } else {
          $('#password-score').html('Very strong password');
          $('#password-recommendation').html('');
          $('#password-progress').removeClass();
          $('#password-progress').addClass('progress-bar bg-success');
        }
      }
    });
  };
  function calculatePasswordScore(password, options) {
    var score = 0;
    var hasNumericChars = false;
    var hasSpecialChars = false;
    var hasMixedCase = false;
    if (password.length < 1) return score;
    if (password.length < options.minimumChars) return score;
    //match numbers
    if (/\d+/.test(password)) {
      hasNumericChars = true;
      score += 20;
      var count = password.match(/\d+?/g).length;
      if (count > 1) {
        //apply extra score if more than 1 numeric character
        score += 10;
      }
    }

    //match special characters including spaces
    if (/[\W]+/.test(password)) {
      hasSpecialChars = true;
      score += 20;
      var count = password.match(/[\W]+?/g).length;
      if (count > 1) {
        //apply extra score if more than 1 special character
        score += 10;
      }
    }
    //mixed case
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) {
      hasMixedCase = true;
      score += 20;
    }
    if (password.length >= options.minimumChars && password.length < 12) {
      score += 10;
    } else if (!hasMixedCase && password.length >= 12) {
      score += 10;
    }
    if (password.length >= 12 && password.length <= 15 && hasMixedCase && (hasSpecialChars || hasNumericChars)) {
      score += 20;
    } else if (password.length >= 12 && password.length <= 15) {
      score += 10;
    }
    if (password.length > 15 && password.length <= 20 && hasMixedCase && (hasSpecialChars || hasNumericChars)) {
      score += 30;
    } else if (password.length > 15 && password.length <= 20) {
      score += 10;
    }
    if (password.length > 20 && hasMixedCase && (hasSpecialChars || hasNumericChars)) {
      score += 40;
    } else if (password.length > 20) {
      score += 20;
    }
    if (score > 100) score = 100;
    return score;
  }
})(jQuery);

/***/ }),

/***/ "./resources/js/libs/default/jquery.scrollup.min.js":
/*!**********************************************************!*\
  !*** ./resources/js/libs/default/jquery.scrollup.min.js ***!
  \**********************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*!
 * scrollup v2.4.1
 * Url: http://markgoodyear.com/labs/scrollup/
 * Copyright (c) Mark Goodyear â€” @markgdyr â€” http://markgoodyear.com
 * License: MIT
 */
!function (l, o, e) {
  "use strict";

  l.fn.scrollUp = function (o) {
    l.data(e.body, "scrollUp") || (l.data(e.body, "scrollUp", !0), l.fn.scrollUp.init(o));
  }, l.fn.scrollUp.init = function (r) {
    var s,
      t,
      c,
      i,
      n,
      a,
      d,
      p = l.fn.scrollUp.settings = l.extend({}, l.fn.scrollUp.defaults, r),
      f = !1;
    switch (d = p.scrollTrigger ? l(p.scrollTrigger) : l("<a/>", {
      id: p.scrollName,
      href: "#top"
    }), p.scrollTitle && d.attr("title", p.scrollTitle), d.appendTo("body"), p.scrollImg || p.scrollTrigger || d.html(p.scrollText), d.css({
      display: "none",
      position: "fixed",
      zIndex: p.zIndex
    }), p.activeOverlay && l("<div/>", {
      id: p.scrollName + "-active"
    }).css({
      position: "absolute",
      top: p.scrollDistance + "px",
      width: "100%",
      borderTop: "1px dotted" + p.activeOverlay,
      zIndex: p.zIndex
    }).appendTo("body"), p.animation) {
      case "fade":
        s = "fadeIn", t = "fadeOut", c = p.animationSpeed;
        break;
      case "slide":
        s = "slideDown", t = "slideUp", c = p.animationSpeed;
        break;
      default:
        s = "show", t = "hide", c = 0;
    }
    i = "top" === p.scrollFrom ? p.scrollDistance : l(e).height() - l(o).height() - p.scrollDistance, n = l(o).scroll(function () {
      l(o).scrollTop() > i ? f || (d[s](c), f = !0) : f && (d[t](c), f = !1);
    }), p.scrollTarget ? "number" == typeof p.scrollTarget ? a = p.scrollTarget : "string" == typeof p.scrollTarget && (a = Math.floor(l(p.scrollTarget).offset().top)) : a = 0, d.click(function (o) {
      o.preventDefault(), l("html, body").animate({
        scrollTop: a
      }, p.scrollSpeed, p.easingType);
    });
  }, l.fn.scrollUp.defaults = {
    scrollName: "scrollUp",
    scrollDistance: 300,
    scrollFrom: "top",
    scrollSpeed: 300,
    easingType: "linear",
    animation: "fade",
    animationSpeed: 200,
    scrollTrigger: !1,
    scrollTarget: !1,
    scrollText: "Scroll to top",
    scrollTitle: !1,
    scrollImg: !1,
    activeOverlay: !1,
    zIndex: 2147483647
  }, l.fn.scrollUp.destroy = function (r) {
    l.removeData(e.body, "scrollUp"), l("#" + l.fn.scrollUp.settings.scrollName).remove(), l("#" + l.fn.scrollUp.settings.scrollName + "-active").remove(), l.fn.jquery.split(".")[1] >= 7 ? l(o).off("scroll", r) : l(o).unbind("scroll", r);
  }, l.scrollUp = l.fn.scrollUp;
}(jQuery, window, document);

/***/ }),

/***/ "./resources/js/libs/default/mail.js":
/*!*******************************************!*\
  !*** ./resources/js/libs/default/mail.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports) {

$(function () {
  // Get the form.
  var form = $('#main_contact_form');
  // Get the messages div.
  var formMessages = $('#success_fail_info');
  // Set up an event listener for the contact form.
  $(form).submit(function (e) {
    // Stop the browser from submitting the form.
    e.preventDefault();

    // Serialize the form data.
    var formData = $(form).serialize();

    // Submit the form using AJAX.
    $.ajax({
      type: 'POST',
      url: $(form).attr('action'),
      data: formData
    }).done(function (response) {
      // Make sure that the formMessages div has the 'success' class.
      $(formMessages).removeClass('text-danger');
      $(formMessages).addClass('text-success');

      // Set the message text.
      $(formMessages).text('Thanks! Message has been sent.');

      // Clear the form.
      $('#name').val('');
      $('#email').val('');
      $('#message').val('');
    }).fail(function (data) {
      // Make sure that the formMessages div has the 'error' class.
      $(formMessages).removeClass('text-success');
      $(formMessages).addClass('text-danger');

      // Set the message text.
      $(formMessages).text("Oops! An error occurred.");
    });
  });
});

/***/ }),

/***/ "./resources/js/libs/imagesloaded.pkgd.min.js":
/*!****************************************************!*\
  !*** ./resources/js/libs/imagesloaded.pkgd.min.js ***!
  \****************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_LOCAL_MODULE_0__, __WEBPACK_LOCAL_MODULE_0__factory, __WEBPACK_LOCAL_MODULE_0__module;var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/*!
 * imagesLoaded PACKAGED v4.1.4
 * JavaScript is all like "You images are done yet or what?"
 * MIT License
 */

!function (e, t) {
   true ? !(__WEBPACK_LOCAL_MODULE_0__factory = (t), (__WEBPACK_LOCAL_MODULE_0__module = { id: "ev-emitter/ev-emitter", exports: {}, loaded: false }), __WEBPACK_LOCAL_MODULE_0__ = (typeof __WEBPACK_LOCAL_MODULE_0__factory === 'function' ? (__WEBPACK_LOCAL_MODULE_0__factory.call(__WEBPACK_LOCAL_MODULE_0__module.exports, __webpack_require__, __WEBPACK_LOCAL_MODULE_0__module.exports, __WEBPACK_LOCAL_MODULE_0__module)) : __WEBPACK_LOCAL_MODULE_0__factory), (__WEBPACK_LOCAL_MODULE_0__module.loaded = true), __WEBPACK_LOCAL_MODULE_0__ === undefined && (__WEBPACK_LOCAL_MODULE_0__ = __WEBPACK_LOCAL_MODULE_0__module.exports)) : undefined;
}("undefined" != typeof window ? window : this, function () {
  function e() {}
  var t = e.prototype;
  return t.on = function (e, t) {
    if (e && t) {
      var i = this._events = this._events || {},
        n = i[e] = i[e] || [];
      return n.indexOf(t) == -1 && n.push(t), this;
    }
  }, t.once = function (e, t) {
    if (e && t) {
      this.on(e, t);
      var i = this._onceEvents = this._onceEvents || {},
        n = i[e] = i[e] || {};
      return n[t] = !0, this;
    }
  }, t.off = function (e, t) {
    var i = this._events && this._events[e];
    if (i && i.length) {
      var n = i.indexOf(t);
      return n != -1 && i.splice(n, 1), this;
    }
  }, t.emitEvent = function (e, t) {
    var i = this._events && this._events[e];
    if (i && i.length) {
      i = i.slice(0), t = t || [];
      for (var n = this._onceEvents && this._onceEvents[e], o = 0; o < i.length; o++) {
        var r = i[o],
          s = n && n[r];
        s && (this.off(e, r), delete n[r]), r.apply(this, t);
      }
      return this;
    }
  }, t.allOff = function () {
    delete this._events, delete this._onceEvents;
  }, e;
}), function (e, t) {
  "use strict";

   true ? !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__WEBPACK_LOCAL_MODULE_0__], __WEBPACK_AMD_DEFINE_RESULT__ = (function (i) {
    return t(e, i);
  }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__)) : undefined;
}("undefined" != typeof window ? window : this, function (e, t) {
  function i(e, t) {
    for (var i in t) e[i] = t[i];
    return e;
  }
  function n(e) {
    if (Array.isArray(e)) return e;
    var t = "object" == _typeof(e) && "number" == typeof e.length;
    return t ? d.call(e) : [e];
  }
  function o(e, t, r) {
    if (!(this instanceof o)) return new o(e, t, r);
    var s = e;
    return "string" == typeof e && (s = document.querySelectorAll(e)), s ? (this.elements = n(s), this.options = i({}, this.options), "function" == typeof t ? r = t : i(this.options, t), r && this.on("always", r), this.getImages(), h && (this.jqDeferred = new h.Deferred()), void setTimeout(this.check.bind(this))) : void a.error("Bad element for imagesLoaded " + (s || e));
  }
  function r(e) {
    this.img = e;
  }
  function s(e, t) {
    this.url = e, this.element = t, this.img = new Image();
  }
  var h = e.jQuery,
    a = e.console,
    d = Array.prototype.slice;
  o.prototype = Object.create(t.prototype), o.prototype.options = {}, o.prototype.getImages = function () {
    this.images = [], this.elements.forEach(this.addElementImages, this);
  }, o.prototype.addElementImages = function (e) {
    "IMG" == e.nodeName && this.addImage(e), this.options.background === !0 && this.addElementBackgroundImages(e);
    var t = e.nodeType;
    if (t && u[t]) {
      for (var i = e.querySelectorAll("img"), n = 0; n < i.length; n++) {
        var o = i[n];
        this.addImage(o);
      }
      if ("string" == typeof this.options.background) {
        var r = e.querySelectorAll(this.options.background);
        for (n = 0; n < r.length; n++) {
          var s = r[n];
          this.addElementBackgroundImages(s);
        }
      }
    }
  };
  var u = {
    1: !0,
    9: !0,
    11: !0
  };
  return o.prototype.addElementBackgroundImages = function (e) {
    var t = getComputedStyle(e);
    if (t) for (var i = /url\((['"])?(.*?)\1\)/gi, n = i.exec(t.backgroundImage); null !== n;) {
      var o = n && n[2];
      o && this.addBackground(o, e), n = i.exec(t.backgroundImage);
    }
  }, o.prototype.addImage = function (e) {
    var t = new r(e);
    this.images.push(t);
  }, o.prototype.addBackground = function (e, t) {
    var i = new s(e, t);
    this.images.push(i);
  }, o.prototype.check = function () {
    function e(e, i, n) {
      setTimeout(function () {
        t.progress(e, i, n);
      });
    }
    var t = this;
    return this.progressedCount = 0, this.hasAnyBroken = !1, this.images.length ? void this.images.forEach(function (t) {
      t.once("progress", e), t.check();
    }) : void this.complete();
  }, o.prototype.progress = function (e, t, i) {
    this.progressedCount++, this.hasAnyBroken = this.hasAnyBroken || !e.isLoaded, this.emitEvent("progress", [this, e, t]), this.jqDeferred && this.jqDeferred.notify && this.jqDeferred.notify(this, e), this.progressedCount == this.images.length && this.complete(), this.options.debug && a && a.log("progress: " + i, e, t);
  }, o.prototype.complete = function () {
    var e = this.hasAnyBroken ? "fail" : "done";
    if (this.isComplete = !0, this.emitEvent(e, [this]), this.emitEvent("always", [this]), this.jqDeferred) {
      var t = this.hasAnyBroken ? "reject" : "resolve";
      this.jqDeferred[t](this);
    }
  }, r.prototype = Object.create(t.prototype), r.prototype.check = function () {
    var e = this.getIsImageComplete();
    return e ? void this.confirm(0 !== this.img.naturalWidth, "naturalWidth") : (this.proxyImage = new Image(), this.proxyImage.addEventListener("load", this), this.proxyImage.addEventListener("error", this), this.img.addEventListener("load", this), this.img.addEventListener("error", this), void (this.proxyImage.src = this.img.src));
  }, r.prototype.getIsImageComplete = function () {
    return this.img.complete && this.img.naturalWidth;
  }, r.prototype.confirm = function (e, t) {
    this.isLoaded = e, this.emitEvent("progress", [this, this.img, t]);
  }, r.prototype.handleEvent = function (e) {
    var t = "on" + e.type;
    this[t] && this[t](e);
  }, r.prototype.onload = function () {
    this.confirm(!0, "onload"), this.unbindEvents();
  }, r.prototype.onerror = function () {
    this.confirm(!1, "onerror"), this.unbindEvents();
  }, r.prototype.unbindEvents = function () {
    this.proxyImage.removeEventListener("load", this), this.proxyImage.removeEventListener("error", this), this.img.removeEventListener("load", this), this.img.removeEventListener("error", this);
  }, s.prototype = Object.create(r.prototype), s.prototype.check = function () {
    this.img.addEventListener("load", this), this.img.addEventListener("error", this), this.img.src = this.url;
    var e = this.getIsImageComplete();
    e && (this.confirm(0 !== this.img.naturalWidth, "naturalWidth"), this.unbindEvents());
  }, s.prototype.unbindEvents = function () {
    this.img.removeEventListener("load", this), this.img.removeEventListener("error", this);
  }, s.prototype.confirm = function (e, t) {
    this.isLoaded = e, this.emitEvent("progress", [this, this.element, t]);
  }, o.makeJQueryPlugin = function (t) {
    t = t || e.jQuery, t && (h = t, h.fn.imagesLoaded = function (e, t) {
      var i = new o(this, e, t);
      return i.jqDeferred.promise(h(this));
    });
  }, o.makeJQueryPlugin(), o;
});

/***/ }),

/***/ "./resources/js/libs/jarallax-video.min.js":
/*!*************************************************!*\
  !*** ./resources/js/libs/jarallax-video.min.js ***!
  \*************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/*!
 * Name    : Video Background Extension for Jarallax
 * Version : 1.0.1
 * Author  : nK <https://nkdev.info>
 * GitHub  : https://github.com/nk-o/jarallax
 */
!function (o) {
  var i = {};
  function n(e) {
    if (i[e]) return i[e].exports;
    var t = i[e] = {
      i: e,
      l: !1,
      exports: {}
    };
    return o[e].call(t.exports, t, t.exports, n), t.l = !0, t.exports;
  }
  n.m = o, n.c = i, n.d = function (e, t, o) {
    n.o(e, t) || Object.defineProperty(e, t, {
      enumerable: !0,
      get: o
    });
  }, n.r = function (e) {
    "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
      value: "Module"
    }), Object.defineProperty(e, "__esModule", {
      value: !0
    });
  }, n.t = function (t, e) {
    if (1 & e && (t = n(t)), 8 & e) return t;
    if (4 & e && "object" == _typeof(t) && t && t.__esModule) return t;
    var o = Object.create(null);
    if (n.r(o), Object.defineProperty(o, "default", {
      enumerable: !0,
      value: t
    }), 2 & e && "string" != typeof t) for (var i in t) n.d(o, i, function (e) {
      return t[e];
    }.bind(null, i));
    return o;
  }, n.n = function (e) {
    var t = e && e.__esModule ? function () {
      return e["default"];
    } : function () {
      return e;
    };
    return n.d(t, "a", t), t;
  }, n.o = function (e, t) {
    return Object.prototype.hasOwnProperty.call(e, t);
  }, n.p = "", n(n.s = 6);
}([,, function (e, t) {
  e.exports = function (e) {
    "complete" === document.readyState || "interactive" === document.readyState ? e.call() : document.attachEvent ? document.attachEvent("onreadystatechange", function () {
      "interactive" === document.readyState && e.call();
    }) : document.addEventListener && document.addEventListener("DOMContentLoaded", e);
  };
},, function (o, e, t) {
  (function (e) {
    var t;
    t = "undefined" != typeof window ? window : void 0 !== e ? e : "undefined" != typeof self ? self : {}, o.exports = t;
  }).call(this, t(5));
}, function (e, t) {
  function o(e) {
    return (o = "function" == typeof Symbol && "symbol" == _typeof(Symbol.iterator) ? function (e) {
      return _typeof(e);
    } : function (e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : _typeof(e);
    })(e);
  }
  var i;
  i = function () {
    return this;
  }();
  try {
    i = i || new Function("return this")();
  } catch (e) {
    "object" === ("undefined" == typeof window ? "undefined" : o(window)) && (i = window);
  }
  e.exports = i;
}, function (e, t, o) {
  e.exports = o(7);
}, function (e, t, o) {
  "use strict";

  o.r(t);
  var i = o(8),
    n = o.n(i),
    a = o(4),
    r = o.n(a),
    l = o(2),
    p = o.n(l),
    u = o(10);
  r.a.VideoWorker = r.a.VideoWorker || n.a, Object(u["default"])(), p()(function () {
    "undefined" != typeof jarallax && jarallax(document.querySelectorAll("[data-jarallax-video]"));
  });
}, function (e, t, o) {
  e.exports = o(9);
}, function (e, t, o) {
  "use strict";

  function n(e) {
    return (n = "function" == typeof Symbol && "symbol" == _typeof(Symbol.iterator) ? function (e) {
      return _typeof(e);
    } : function (e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : _typeof(e);
    })(e);
  }
  function a(e, t) {
    for (var o = 0; o < t.length; o++) {
      var i = t[o];
      i.enumerable = i.enumerable || !1, i.configurable = !0, "value" in i && (i.writable = !0), Object.defineProperty(e, i.key, i);
    }
  }
  function i() {
    this._done = [], this._fail = [];
  }
  o.r(t), o.d(t, "default", function () {
    return c;
  }), i.prototype = {
    execute: function execute(e, t) {
      var o = e.length;
      for (t = Array.prototype.slice.call(t); o--;) e[o].apply(null, t);
    },
    resolve: function resolve() {
      this.execute(this._done, arguments);
    },
    reject: function reject() {
      this.execute(this._fail, arguments);
    },
    done: function done(e) {
      this._done.push(e);
    },
    fail: function fail(e) {
      this._fail.push(e);
    }
  };
  var r = 0,
    l = 0,
    p = 0,
    u = 0,
    s = 0,
    d = new i(),
    y = new i(),
    c = function () {
      function i(e, t) {
        !function (e, t) {
          if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function");
        }(this, i);
        var o = this;
        o.url = e, o.options_default = {
          autoplay: !1,
          loop: !1,
          mute: !1,
          volume: 100,
          showContols: !0,
          startTime: 0,
          endTime: 0
        }, o.options = o.extend({}, o.options_default, t), o.videoID = o.parseURL(e), o.videoID && (o.ID = r++, o.loadAPI(), o.init());
      }
      return function (e, t, o) {
        t && a(e.prototype, t), o && a(e, o);
      }(i, [{
        key: "extend",
        value: function value(o) {
          var i = arguments;
          return o = o || {}, Object.keys(arguments).forEach(function (t) {
            i[t] && Object.keys(i[t]).forEach(function (e) {
              o[e] = i[t][e];
            });
          }), o;
        }
      }, {
        key: "parseURL",
        value: function value(e) {
          var t,
            o,
            i,
            n,
            a,
            r = !(!(t = e.match(/.*(?:youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=)([^#\&\?]*).*/)) || 11 !== t[1].length) && t[1],
            l = !(!(o = e.match(/https?:\/\/(?:www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|album\/(\d+)\/video\/|video\/|)(\d+)(?:$|\/|\?)/)) || !o[3]) && o[3],
            p = (i = e.split(/,(?=mp4\:|webm\:|ogv\:|ogg\:)/), n = {}, a = 0, i.forEach(function (e) {
              var t = e.match(/^(mp4|webm|ogv|ogg)\:(.*)/);
              t && t[1] && t[2] && (n["ogv" === t[1] ? "ogg" : t[1]] = t[2], a = 1);
            }), !!a && n);
          return r ? (this.type = "youtube", r) : l ? (this.type = "vimeo", l) : !!p && (this.type = "local", p);
        }
      }, {
        key: "isValid",
        value: function value() {
          return !!this.videoID;
        }
      }, {
        key: "on",
        value: function value(e, t) {
          this.userEventsList = this.userEventsList || [], (this.userEventsList[e] || (this.userEventsList[e] = [])).push(t);
        }
      }, {
        key: "off",
        value: function value(o, i) {
          var n = this;
          this.userEventsList && this.userEventsList[o] && (i ? this.userEventsList[o].forEach(function (e, t) {
            e === i && (n.userEventsList[o][t] = !1);
          }) : delete this.userEventsList[o]);
        }
      }, {
        key: "fire",
        value: function value(e) {
          var t = this,
            o = [].slice.call(arguments, 1);
          this.userEventsList && void 0 !== this.userEventsList[e] && this.userEventsList[e].forEach(function (e) {
            e && e.apply(t, o);
          });
        }
      }, {
        key: "play",
        value: function value(e) {
          var t = this;
          t.player && ("youtube" === t.type && t.player.playVideo && (void 0 !== e && t.player.seekTo(e || 0), YT.PlayerState.PLAYING !== t.player.getPlayerState() && t.player.playVideo()), "vimeo" === t.type && (void 0 !== e && t.player.setCurrentTime(e), t.player.getPaused().then(function (e) {
            e && t.player.play();
          })), "local" === t.type && (void 0 !== e && (t.player.currentTime = e), t.player.paused && t.player.play()));
        }
      }, {
        key: "pause",
        value: function value() {
          var t = this;
          t.player && ("youtube" === t.type && t.player.pauseVideo && YT.PlayerState.PLAYING === t.player.getPlayerState() && t.player.pauseVideo(), "vimeo" === t.type && t.player.getPaused().then(function (e) {
            e || t.player.pause();
          }), "local" === t.type && (t.player.paused || t.player.pause()));
        }
      }, {
        key: "mute",
        value: function value() {
          var e = this;
          e.player && ("youtube" === e.type && e.player.mute && e.player.mute(), "vimeo" === e.type && e.player.setVolume && e.player.setVolume(0), "local" === e.type && (e.$video.muted = !0));
        }
      }, {
        key: "unmute",
        value: function value() {
          var e = this;
          e.player && ("youtube" === e.type && e.player.mute && e.player.unMute(), "vimeo" === e.type && e.player.setVolume && e.player.setVolume(e.options.volume), "local" === e.type && (e.$video.muted = !1));
        }
      }, {
        key: "setVolume",
        value: function value(e) {
          var t = 0 < arguments.length && void 0 !== e && e,
            o = this;
          o.player && t && ("youtube" === o.type && o.player.setVolume && o.player.setVolume(t), "vimeo" === o.type && o.player.setVolume && o.player.setVolume(t), "local" === o.type && (o.$video.volume = t / 100));
        }
      }, {
        key: "getVolume",
        value: function value(t) {
          var e = this;
          e.player ? ("youtube" === e.type && e.player.getVolume && t(e.player.getVolume()), "vimeo" === e.type && e.player.getVolume && e.player.getVolume().then(function (e) {
            t(e);
          }), "local" === e.type && t(100 * e.$video.volume)) : t(!1);
        }
      }, {
        key: "getMuted",
        value: function value(t) {
          var e = this;
          e.player ? ("youtube" === e.type && e.player.isMuted && t(e.player.isMuted()), "vimeo" === e.type && e.player.getVolume && e.player.getVolume().then(function (e) {
            t(!!e);
          }), "local" === e.type && t(e.$video.muted)) : t(null);
        }
      }, {
        key: "getImageURL",
        value: function value(t) {
          var o = this;
          if (o.videoImage) t(o.videoImage);else {
            if ("youtube" === o.type) {
              var e = ["maxresdefault", "sddefault", "hqdefault", "0"],
                i = 0,
                n = new Image();
              n.onload = function () {
                120 !== (this.naturalWidth || this.width) || i === e.length - 1 ? (o.videoImage = "https://img.youtube.com/vi/".concat(o.videoID, "/").concat(e[i], ".jpg"), t(o.videoImage)) : (i++, this.src = "https://img.youtube.com/vi/".concat(o.videoID, "/").concat(e[i], ".jpg"));
              }, n.src = "https://img.youtube.com/vi/".concat(o.videoID, "/").concat(e[i], ".jpg");
            }
            if ("vimeo" === o.type) {
              var a = new XMLHttpRequest();
              a.open("GET", "https://vimeo.com/api/v2/video/".concat(o.videoID, ".json"), !0), a.onreadystatechange = function () {
                if (4 === this.readyState && 200 <= this.status && this.status < 400) {
                  var e = JSON.parse(this.responseText);
                  o.videoImage = e[0].thumbnail_large, t(o.videoImage);
                }
              }, a.send(), a = null;
            }
          }
        }
      }, {
        key: "getIframe",
        value: function value(e) {
          this.getVideo(e);
        }
      }, {
        key: "getVideo",
        value: function value(p) {
          var u = this;
          u.$video ? p(u.$video) : u.onAPIready(function () {
            var e, t;
            if (u.$video || ((e = document.createElement("div")).style.display = "none"), "youtube" === u.type) {
              var o, i;
              u.playerOptions = {}, u.playerOptions.videoId = u.videoID, u.playerOptions.playerVars = {
                autohide: 1,
                rel: 0,
                autoplay: 0,
                playsinline: 1
              }, u.options.showContols || (u.playerOptions.playerVars.iv_load_policy = 3, u.playerOptions.playerVars.modestbranding = 1, u.playerOptions.playerVars.controls = 0, u.playerOptions.playerVars.showinfo = 0, u.playerOptions.playerVars.disablekb = 1), u.playerOptions.events = {
                onReady: function onReady(t) {
                  if (u.options.mute ? t.target.mute() : u.options.volume && t.target.setVolume(u.options.volume), u.options.autoplay && u.play(u.options.startTime), u.fire("ready", t), u.options.loop && !u.options.endTime) {
                    u.options.endTime = u.player.getDuration() - .1;
                  }
                  setInterval(function () {
                    u.getVolume(function (e) {
                      u.options.volume !== e && (u.options.volume = e, u.fire("volumechange", t));
                    });
                  }, 150);
                },
                onStateChange: function onStateChange(e) {
                  u.options.loop && e.data === YT.PlayerState.ENDED && u.play(u.options.startTime), o || e.data !== YT.PlayerState.PLAYING || (o = 1, u.fire("started", e)), e.data === YT.PlayerState.PLAYING && u.fire("play", e), e.data === YT.PlayerState.PAUSED && u.fire("pause", e), e.data === YT.PlayerState.ENDED && u.fire("ended", e), e.data === YT.PlayerState.PLAYING ? i = setInterval(function () {
                    u.fire("timeupdate", e), u.options.endTime && u.player.getCurrentTime() >= u.options.endTime && (u.options.loop ? u.play(u.options.startTime) : u.pause());
                  }, 150) : clearInterval(i);
                }
              };
              var n = !u.$video;
              if (n) {
                var a = document.createElement("div");
                a.setAttribute("id", u.playerID), e.appendChild(a), document.body.appendChild(e);
              }
              u.player = u.player || new window.YT.Player(u.playerID, u.playerOptions), n && (u.$video = document.getElementById(u.playerID), u.videoWidth = parseInt(u.$video.getAttribute("width"), 10) || 1280, u.videoHeight = parseInt(u.$video.getAttribute("height"), 10) || 720);
            }
            if ("vimeo" === u.type) {
              if (u.playerOptions = {
                id: u.videoID,
                autopause: 0,
                transparent: 0,
                autoplay: u.options.autoplay ? 1 : 0,
                loop: u.options.loop ? 1 : 0,
                muted: u.options.mute ? 1 : 0
              }, u.options.volume && (u.playerOptions.volume = u.options.volume), u.options.showContols || (u.playerOptions.badge = 0, u.playerOptions.byline = 0, u.playerOptions.portrait = 0, u.playerOptions.title = 0), !u.$video) {
                var r = "";
                Object.keys(u.playerOptions).forEach(function (e) {
                  "" !== r && (r += "&"), r += "".concat(e, "=").concat(encodeURIComponent(u.playerOptions[e]));
                }), u.$video = document.createElement("iframe"), u.$video.setAttribute("id", u.playerID), u.$video.setAttribute("src", "https://player.vimeo.com/video/".concat(u.videoID, "?").concat(r)), u.$video.setAttribute("frameborder", "0"), u.$video.setAttribute("mozallowfullscreen", ""), u.$video.setAttribute("allowfullscreen", ""), e.appendChild(u.$video), document.body.appendChild(e);
              }
              var l;
              u.player = u.player || new Vimeo.Player(u.$video, u.playerOptions), u.options.startTime && u.options.autoplay && u.player.setCurrentTime(u.options.startTime), u.player.getVideoWidth().then(function (e) {
                u.videoWidth = e || 1280;
              }), u.player.getVideoHeight().then(function (e) {
                u.videoHeight = e || 720;
              }), u.player.on("timeupdate", function (e) {
                l || (u.fire("started", e), l = 1), u.fire("timeupdate", e), u.options.endTime && u.options.endTime && e.seconds >= u.options.endTime && (u.options.loop ? u.play(u.options.startTime) : u.pause());
              }), u.player.on("play", function (e) {
                u.fire("play", e), u.options.startTime && 0 === e.seconds && u.play(u.options.startTime);
              }), u.player.on("pause", function (e) {
                u.fire("pause", e);
              }), u.player.on("ended", function (e) {
                u.fire("ended", e);
              }), u.player.on("loaded", function (e) {
                u.fire("ready", e);
              }), u.player.on("volumechange", function (e) {
                u.fire("volumechange", e);
              });
            }
            "local" === u.type && (u.$video || (u.$video = document.createElement("video"), u.options.showContols && (u.$video.controls = !0), u.options.mute ? u.$video.muted = !0 : u.$video.volume && (u.$video.volume = u.options.volume / 100), u.options.loop && (u.$video.loop = !0), u.$video.setAttribute("playsinline", ""), u.$video.setAttribute("webkit-playsinline", ""), u.$video.setAttribute("id", u.playerID), e.appendChild(u.$video), document.body.appendChild(e), Object.keys(u.videoID).forEach(function (e) {
              !function (e, t, o) {
                var i = document.createElement("source");
                i.src = t, i.type = o, e.appendChild(i);
              }(u.$video, u.videoID[e], "video/".concat(e));
            })), u.player = u.player || u.$video, u.player.addEventListener("playing", function (e) {
              t || u.fire("started", e), t = 1;
            }), u.player.addEventListener("timeupdate", function (e) {
              u.fire("timeupdate", e), u.options.endTime && u.options.endTime && this.currentTime >= u.options.endTime && (u.options.loop ? u.play(u.options.startTime) : u.pause());
            }), u.player.addEventListener("play", function (e) {
              u.fire("play", e);
            }), u.player.addEventListener("pause", function (e) {
              u.fire("pause", e);
            }), u.player.addEventListener("ended", function (e) {
              u.fire("ended", e);
            }), u.player.addEventListener("loadedmetadata", function () {
              u.videoWidth = this.videoWidth || 1280, u.videoHeight = this.videoHeight || 720, u.fire("ready"), u.options.autoplay && u.play(u.options.startTime);
            }), u.player.addEventListener("volumechange", function (e) {
              u.getVolume(function (e) {
                u.options.volume = e;
              }), u.fire("volumechange", e);
            }));
            p(u.$video);
          });
        }
      }, {
        key: "init",
        value: function value() {
          this.playerID = "VideoWorker-".concat(this.ID);
        }
      }, {
        key: "loadAPI",
        value: function value() {
          if (!l || !p) {
            var e = "";
            if ("youtube" !== this.type || l || (l = 1, e = "https://www.youtube.com/iframe_api"), "vimeo" !== this.type || p || (p = 1, e = "https://player.vimeo.com/api/player.js"), e) {
              var t = document.createElement("script"),
                o = document.getElementsByTagName("head")[0];
              t.src = e, o.appendChild(t), t = o = null;
            }
          }
        }
      }, {
        key: "onAPIready",
        value: function value(e) {
          if ("youtube" === this.type && ("undefined" != typeof YT && 0 !== YT.loaded || u ? "object" === ("undefined" == typeof YT ? "undefined" : n(YT)) && 1 === YT.loaded ? e() : d.done(function () {
            e();
          }) : (u = 1, window.onYouTubeIframeAPIReady = function () {
            window.onYouTubeIframeAPIReady = null, d.resolve("done"), e();
          })), "vimeo" === this.type) if ("undefined" != typeof Vimeo || s) "undefined" != typeof Vimeo ? e() : y.done(function () {
            e();
          });else {
            s = 1;
            var t = setInterval(function () {
              "undefined" != typeof Vimeo && (clearInterval(t), y.resolve("done"), e());
            }, 20);
          }
          "local" === this.type && e();
        }
      }]), i;
    }();
}, function (e, t, o) {
  "use strict";

  o.r(t), o.d(t, "default", function () {
    return a;
  });
  var i = o(8),
    r = o.n(i),
    n = o(4),
    p = o.n(n);
  function a() {
    var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : p.a.jarallax;
    if (void 0 !== e) {
      var t = e.constructor,
        i = t.prototype.onScroll;
      t.prototype.onScroll = function () {
        var o = this;
        i.apply(o), o.isVideoInserted || !o.video || o.options.videoLazyLoading && !o.isElementInViewport || o.options.disableVideo() || (o.isVideoInserted = !0, o.video.getVideo(function (e) {
          var t = e.parentNode;
          o.css(e, {
            position: o.image.position,
            top: "0px",
            left: "0px",
            right: "0px",
            bottom: "0px",
            width: "100%",
            height: "100%",
            maxWidth: "none",
            maxHeight: "none",
            margin: 0,
            zIndex: -1
          }), o.$video = e, o.image.$container.appendChild(e), t.parentNode.removeChild(t);
        }));
      };
      var l = t.prototype.coverImage;
      t.prototype.coverImage = function () {
        var e = this,
          t = l.apply(e),
          o = !!e.image.$item && e.image.$item.nodeName;
        if (t && e.video && o && ("IFRAME" === o || "VIDEO" === o)) {
          var i = t.image.height,
            n = i * e.image.width / e.image.height,
            a = (t.container.width - n) / 2,
            r = t.image.marginTop;
          t.container.width > n && (i = (n = t.container.width) * e.image.height / e.image.width, a = 0, r += (t.image.height - i) / 2), "IFRAME" === o && (i += 400, r -= 200), e.css(e.$video, {
            width: "".concat(n, "px"),
            marginLeft: "".concat(a, "px"),
            height: "".concat(i, "px"),
            marginTop: "".concat(r, "px")
          });
        }
        return t;
      };
      var o = t.prototype.initImg;
      t.prototype.initImg = function () {
        var e = this,
          t = o.apply(e);
        return e.options.videoSrc || (e.options.videoSrc = e.$item.getAttribute("data-jarallax-video") || null), e.options.videoSrc ? (e.defaultInitImgResult = t, !0) : t;
      };
      var n = t.prototype.canInitParallax;
      t.prototype.canInitParallax = function () {
        var o = this,
          e = n.apply(o);
        if (!o.options.videoSrc) return e;
        var t = new r.a(o.options.videoSrc, {
          autoplay: !0,
          loop: o.options.videoLoop,
          showContols: !1,
          startTime: o.options.videoStartTime || 0,
          endTime: o.options.videoEndTime || 0,
          mute: o.options.videoVolume ? 0 : 1,
          volume: o.options.videoVolume || 0
        });
        if (t.isValid()) if (e) {
          if (t.on("ready", function () {
            if (o.options.videoPlayOnlyVisible) {
              var e = o.onScroll;
              o.onScroll = function () {
                e.apply(o), !o.options.videoLoop && (o.options.videoLoop || o.videoEnded) || (o.isVisible() ? t.play() : t.pause());
              };
            } else t.play();
          }), t.on("started", function () {
            o.image.$default_item = o.image.$item, o.image.$item = o.$video, o.image.width = o.video.videoWidth || 1280, o.image.height = o.video.videoHeight || 720, o.coverImage(), o.clipContainer(), o.onScroll(), o.image.$default_item && (o.image.$default_item.style.display = "none");
          }), t.on("ended", function () {
            o.videoEnded = !0, o.options.videoLoop || o.image.$default_item && (o.image.$item = o.image.$default_item, o.image.$item.style.display = "block", o.coverImage(), o.clipContainer(), o.onScroll());
          }), o.video = t, !o.defaultInitImgResult) return o.image.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7", "local" === t.type || (t.getImageURL(function (e) {
            o.image.bgImage = 'url("'.concat(e, '")'), o.init();
          }), !1);
        } else o.defaultInitImgResult || t.getImageURL(function (e) {
          var t = o.$item.getAttribute("style");
          t && o.$item.setAttribute("data-jarallax-original-styles", t), o.css(o.$item, {
            "background-image": 'url("'.concat(e, '")'),
            "background-position": "center",
            "background-size": "cover"
          });
        });
        return e;
      };
      var a = t.prototype.destroy;
      t.prototype.destroy = function () {
        var e = this;
        e.image.$default_item && (e.image.$item = e.image.$default_item, delete e.image.$default_item), a.apply(e);
      };
    }
  }
}]);

/***/ }),

/***/ "./resources/js/libs/jarallax.min.js":
/*!*******************************************!*\
  !*** ./resources/js/libs/jarallax.min.js ***!
  \*******************************************/
/*! no static exports found */
/***/ (function(module, exports) {

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/*!
 * Name    : Just Another Parallax [Jarallax]
 * Version : 1.12.0
 * Author  : nK <https://nkdev.info>
 * GitHub  : https://github.com/nk-o/jarallax
 */
!function (n) {
  var o = {};
  function i(e) {
    if (o[e]) return o[e].exports;
    var t = o[e] = {
      i: e,
      l: !1,
      exports: {}
    };
    return n[e].call(t.exports, t, t.exports, i), t.l = !0, t.exports;
  }
  i.m = n, i.c = o, i.d = function (e, t, n) {
    i.o(e, t) || Object.defineProperty(e, t, {
      enumerable: !0,
      get: n
    });
  }, i.r = function (e) {
    "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {
      value: "Module"
    }), Object.defineProperty(e, "__esModule", {
      value: !0
    });
  }, i.t = function (t, e) {
    if (1 & e && (t = i(t)), 8 & e) return t;
    if (4 & e && "object" == _typeof(t) && t && t.__esModule) return t;
    var n = Object.create(null);
    if (i.r(n), Object.defineProperty(n, "default", {
      enumerable: !0,
      value: t
    }), 2 & e && "string" != typeof t) for (var o in t) i.d(n, o, function (e) {
      return t[e];
    }.bind(null, o));
    return n;
  }, i.n = function (e) {
    var t = e && e.__esModule ? function () {
      return e["default"];
    } : function () {
      return e;
    };
    return i.d(t, "a", t), t;
  }, i.o = function (e, t) {
    return Object.prototype.hasOwnProperty.call(e, t);
  }, i.p = "", i(i.s = 11);
}([,, function (e, t) {
  e.exports = function (e) {
    "complete" === document.readyState || "interactive" === document.readyState ? e.call() : document.attachEvent ? document.attachEvent("onreadystatechange", function () {
      "interactive" === document.readyState && e.call();
    }) : document.addEventListener && document.addEventListener("DOMContentLoaded", e);
  };
},, function (n, e, t) {
  (function (e) {
    var t;
    t = "undefined" != typeof window ? window : void 0 !== e ? e : "undefined" != typeof self ? self : {}, n.exports = t;
  }).call(this, t(5));
}, function (e, t) {
  function n(e) {
    return (n = "function" == typeof Symbol && "symbol" == _typeof(Symbol.iterator) ? function (e) {
      return _typeof(e);
    } : function (e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : _typeof(e);
    })(e);
  }
  var o;
  o = function () {
    return this;
  }();
  try {
    o = o || new Function("return this")();
  } catch (e) {
    "object" === ("undefined" == typeof window ? "undefined" : n(window)) && (o = window);
  }
  e.exports = o;
},,,,,, function (e, t, n) {
  e.exports = n(12);
}, function (e, t, n) {
  "use strict";

  n.r(t);
  var o = n(2),
    i = n.n(o),
    a = n(4),
    r = n(13);
  function l(e) {
    return (l = "function" == typeof Symbol && "symbol" == _typeof(Symbol.iterator) ? function (e) {
      return _typeof(e);
    } : function (e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : _typeof(e);
    })(e);
  }
  var s = a.window.jarallax;
  if (a.window.jarallax = r["default"], a.window.jarallax.noConflict = function () {
    return a.window.jarallax = s, this;
  }, void 0 !== a.jQuery) {
    var c = function c() {
      var e = arguments || [];
      Array.prototype.unshift.call(e, this);
      var t = r["default"].apply(a.window, e);
      return "object" !== l(t) ? t : this;
    };
    c.constructor = r["default"].constructor;
    var u = a.jQuery.fn.jarallax;
    a.jQuery.fn.jarallax = c, a.jQuery.fn.jarallax.noConflict = function () {
      return a.jQuery.fn.jarallax = u, this;
    };
  }
  i()(function () {
    Object(r["default"])(document.querySelectorAll("[data-jarallax]"));
  });
}, function (e, t, n) {
  "use strict";

  n.r(t);
  var o = n(2),
    i = n.n(o),
    a = n(14),
    r = n.n(a),
    b = n(4);
  function c(e, t) {
    return function (e) {
      if (Array.isArray(e)) return e;
    }(e) || function (e, t) {
      if (!(Symbol.iterator in Object(e) || "[object Arguments]" === Object.prototype.toString.call(e))) return;
      var n = [],
        o = !0,
        i = !1,
        a = void 0;
      try {
        for (var r, l = e[Symbol.iterator](); !(o = (r = l.next()).done) && (n.push(r.value), !t || n.length !== t); o = !0);
      } catch (e) {
        i = !0, a = e;
      } finally {
        try {
          o || null == l["return"] || l["return"]();
        } finally {
          if (i) throw a;
        }
      }
      return n;
    }(e, t) || function () {
      throw new TypeError("Invalid attempt to destructure non-iterable instance");
    }();
  }
  function u(e) {
    return (u = "function" == typeof Symbol && "symbol" == _typeof(Symbol.iterator) ? function (e) {
      return _typeof(e);
    } : function (e) {
      return e && "function" == typeof Symbol && e.constructor === Symbol && e !== Symbol.prototype ? "symbol" : _typeof(e);
    })(e);
  }
  function l(e, t) {
    for (var n = 0; n < t.length; n++) {
      var o = t[n];
      o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, o.key, o);
    }
  }
  var s,
    v,
    m = -1 < navigator.userAgent.indexOf("MSIE ") || -1 < navigator.userAgent.indexOf("Trident/") || -1 < navigator.userAgent.indexOf("Edge/"),
    p = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
    d = function () {
      for (var e = "transform WebkitTransform MozTransform".split(" "), t = document.createElement("div"), n = 0; n < e.length; n++) if (t && void 0 !== t.style[e[n]]) return e[n];
      return !1;
    }();
  function f() {
    v = p ? (!s && document.body && ((s = document.createElement("div")).style.cssText = "position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;", document.body.appendChild(s)), (s ? s.clientHeight : 0) || b.window.innerHeight || document.documentElement.clientHeight) : b.window.innerHeight || document.documentElement.clientHeight;
  }
  f(), b.window.addEventListener("resize", f), b.window.addEventListener("orientationchange", f), b.window.addEventListener("load", f), i()(function () {
    f();
  });
  var g = [];
  function y() {
    g.length && (g.forEach(function (e, t) {
      var n = e.instance,
        o = e.oldData,
        i = n.$item.getBoundingClientRect(),
        a = {
          width: i.width,
          height: i.height,
          top: i.top,
          bottom: i.bottom,
          wndW: b.window.innerWidth,
          wndH: v
        },
        r = !o || o.wndW !== a.wndW || o.wndH !== a.wndH || o.width !== a.width || o.height !== a.height,
        l = r || !o || o.top !== a.top || o.bottom !== a.bottom;
      g[t].oldData = a, r && n.onResize(), l && n.onScroll();
    }), r()(y));
  }
  function h(e, t) {
    ("object" === ("undefined" == typeof HTMLElement ? "undefined" : u(HTMLElement)) ? e instanceof HTMLElement : e && "object" === u(e) && null !== e && 1 === e.nodeType && "string" == typeof e.nodeName) && (e = [e]);
    for (var n, o = t, i = Array.prototype.slice.call(arguments, 2), a = e.length, r = 0; r < a; r++) if ("object" === u(o) || void 0 === o ? e[r].jarallax || (e[r].jarallax = new w(e[r], o)) : e[r].jarallax && (n = e[r].jarallax[o].apply(e[r].jarallax, i)), void 0 !== n) return n;
    return e;
  }
  var x = 0,
    w = function () {
      function s(e, t) {
        !function (e, t) {
          if (!(e instanceof t)) throw new TypeError("Cannot call a class as a function");
        }(this, s);
        var n = this;
        n.instanceID = x++, n.$item = e, n.defaults = {
          type: "scroll",
          speed: .5,
          imgSrc: null,
          imgElement: ".jarallax-img",
          imgSize: "cover",
          imgPosition: "50% 50%",
          imgRepeat: "no-repeat",
          keepImg: !1,
          elementInViewport: null,
          zIndex: -100,
          disableParallax: !1,
          disableVideo: !1,
          videoSrc: null,
          videoStartTime: 0,
          videoEndTime: 0,
          videoVolume: 0,
          videoLoop: !0,
          videoPlayOnlyVisible: !0,
          videoLazyLoading: !0,
          onScroll: null,
          onInit: null,
          onDestroy: null,
          onCoverImage: null
        };
        var o = n.$item.dataset || {},
          i = {};
        if (Object.keys(o).forEach(function (e) {
          var t = e.substr(0, 1).toLowerCase() + e.substr(1);
          t && void 0 !== n.defaults[t] && (i[t] = o[e]);
        }), n.options = n.extend({}, n.defaults, i, t), n.pureOptions = n.extend({}, n.options), Object.keys(n.options).forEach(function (e) {
          "true" === n.options[e] ? n.options[e] = !0 : "false" === n.options[e] && (n.options[e] = !1);
        }), n.options.speed = Math.min(2, Math.max(-1, parseFloat(n.options.speed))), "string" == typeof n.options.disableParallax && (n.options.disableParallax = new RegExp(n.options.disableParallax)), n.options.disableParallax instanceof RegExp) {
          var a = n.options.disableParallax;
          n.options.disableParallax = function () {
            return a.test(navigator.userAgent);
          };
        }
        if ("function" != typeof n.options.disableParallax && (n.options.disableParallax = function () {
          return !1;
        }), "string" == typeof n.options.disableVideo && (n.options.disableVideo = new RegExp(n.options.disableVideo)), n.options.disableVideo instanceof RegExp) {
          var r = n.options.disableVideo;
          n.options.disableVideo = function () {
            return r.test(navigator.userAgent);
          };
        }
        "function" != typeof n.options.disableVideo && (n.options.disableVideo = function () {
          return !1;
        });
        var l = n.options.elementInViewport;
        l && "object" === u(l) && void 0 !== l.length && (l = c(l, 1)[0]);
        l instanceof Element || (l = null), n.options.elementInViewport = l, n.image = {
          src: n.options.imgSrc || null,
          $container: null,
          useImgTag: !1,
          position: /iPad|iPhone|iPod|Android/.test(navigator.userAgent) ? "absolute" : "fixed"
        }, n.initImg() && n.canInitParallax() && n.init();
      }
      return function (e, t, n) {
        t && l(e.prototype, t), n && l(e, n);
      }(s, [{
        key: "css",
        value: function value(t, n) {
          return "string" == typeof n ? b.window.getComputedStyle(t).getPropertyValue(n) : (n.transform && d && (n[d] = n.transform), Object.keys(n).forEach(function (e) {
            t.style[e] = n[e];
          }), t);
        }
      }, {
        key: "extend",
        value: function value(n) {
          var o = arguments;
          return n = n || {}, Object.keys(arguments).forEach(function (t) {
            o[t] && Object.keys(o[t]).forEach(function (e) {
              n[e] = o[t][e];
            });
          }), n;
        }
      }, {
        key: "getWindowData",
        value: function value() {
          return {
            width: b.window.innerWidth || document.documentElement.clientWidth,
            height: v,
            y: document.documentElement.scrollTop
          };
        }
      }, {
        key: "initImg",
        value: function value() {
          var e = this,
            t = e.options.imgElement;
          return t && "string" == typeof t && (t = e.$item.querySelector(t)), t instanceof Element || (e.options.imgSrc ? (t = new Image()).src = e.options.imgSrc : t = null), t && (e.options.keepImg ? e.image.$item = t.cloneNode(!0) : (e.image.$item = t, e.image.$itemParent = t.parentNode), e.image.useImgTag = !0), !!e.image.$item || (null === e.image.src && (e.image.src = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7", e.image.bgImage = e.css(e.$item, "background-image")), !(!e.image.bgImage || "none" === e.image.bgImage));
        }
      }, {
        key: "canInitParallax",
        value: function value() {
          return d && !this.options.disableParallax();
        }
      }, {
        key: "init",
        value: function value() {
          var e = this,
            t = {
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              overflow: "hidden",
              pointerEvents: "none"
            },
            n = {};
          if (!e.options.keepImg) {
            var o = e.$item.getAttribute("style");
            if (o && e.$item.setAttribute("data-jarallax-original-styles", o), e.image.useImgTag) {
              var i = e.image.$item.getAttribute("style");
              i && e.image.$item.setAttribute("data-jarallax-original-styles", i);
            }
          }
          if ("static" === e.css(e.$item, "position") && e.css(e.$item, {
            position: "relative"
          }), "auto" === e.css(e.$item, "z-index") && e.css(e.$item, {
            zIndex: 0
          }), e.image.$container = document.createElement("div"), e.css(e.image.$container, t), e.css(e.image.$container, {
            "z-index": e.options.zIndex
          }), m && e.css(e.image.$container, {
            opacity: .9999
          }), e.image.$container.setAttribute("id", "jarallax-container-".concat(e.instanceID)), e.$item.appendChild(e.image.$container), e.image.useImgTag ? n = e.extend({
            "object-fit": e.options.imgSize,
            "object-position": e.options.imgPosition,
            "font-family": "object-fit: ".concat(e.options.imgSize, "; object-position: ").concat(e.options.imgPosition, ";"),
            "max-width": "none"
          }, t, n) : (e.image.$item = document.createElement("div"), e.image.src && (n = e.extend({
            "background-position": e.options.imgPosition,
            "background-size": e.options.imgSize,
            "background-repeat": e.options.imgRepeat,
            "background-image": e.image.bgImage || 'url("'.concat(e.image.src, '")')
          }, t, n))), "opacity" !== e.options.type && "scale" !== e.options.type && "scale-opacity" !== e.options.type && 1 !== e.options.speed || (e.image.position = "absolute"), "fixed" === e.image.position) {
            var a = function (e) {
              for (var t = []; null !== e.parentElement;) 1 === (e = e.parentElement).nodeType && t.push(e);
              return t;
            }(e.$item).filter(function (e) {
              var t = b.window.getComputedStyle(e),
                n = t["-webkit-transform"] || t["-moz-transform"] || t.transform;
              return n && "none" !== n || /(auto|scroll)/.test(t.overflow + t["overflow-y"] + t["overflow-x"]);
            });
            e.image.position = a.length ? "absolute" : "fixed";
          }
          n.position = e.image.position, e.css(e.image.$item, n), e.image.$container.appendChild(e.image.$item), e.onResize(), e.onScroll(!0), e.options.onInit && e.options.onInit.call(e), "none" !== e.css(e.$item, "background-image") && e.css(e.$item, {
            "background-image": "none"
          }), e.addToParallaxList();
        }
      }, {
        key: "addToParallaxList",
        value: function value() {
          g.push({
            instance: this
          }), 1 === g.length && y();
        }
      }, {
        key: "removeFromParallaxList",
        value: function value() {
          var n = this;
          g.forEach(function (e, t) {
            e.instance.instanceID === n.instanceID && g.splice(t, 1);
          });
        }
      }, {
        key: "destroy",
        value: function value() {
          var e = this;
          e.removeFromParallaxList();
          var t = e.$item.getAttribute("data-jarallax-original-styles");
          if (e.$item.removeAttribute("data-jarallax-original-styles"), t ? e.$item.setAttribute("style", t) : e.$item.removeAttribute("style"), e.image.useImgTag) {
            var n = e.image.$item.getAttribute("data-jarallax-original-styles");
            e.image.$item.removeAttribute("data-jarallax-original-styles"), n ? e.image.$item.setAttribute("style", t) : e.image.$item.removeAttribute("style"), e.image.$itemParent && e.image.$itemParent.appendChild(e.image.$item);
          }
          e.$clipStyles && e.$clipStyles.parentNode.removeChild(e.$clipStyles), e.image.$container && e.image.$container.parentNode.removeChild(e.image.$container), e.options.onDestroy && e.options.onDestroy.call(e), delete e.$item.jarallax;
        }
      }, {
        key: "clipContainer",
        value: function value() {
          if ("fixed" === this.image.position) {
            var e = this,
              t = e.image.$container.getBoundingClientRect(),
              n = t.width,
              o = t.height;
            if (!e.$clipStyles) e.$clipStyles = document.createElement("style"), e.$clipStyles.setAttribute("type", "text/css"), e.$clipStyles.setAttribute("id", "jarallax-clip-".concat(e.instanceID)), (document.head || document.getElementsByTagName("head")[0]).appendChild(e.$clipStyles);
            var i = "#jarallax-container-".concat(e.instanceID, " {\n           clip: rect(0 ").concat(n, "px ").concat(o, "px 0);\n           clip: rect(0, ").concat(n, "px, ").concat(o, "px, 0);\n        }");
            e.$clipStyles.styleSheet ? e.$clipStyles.styleSheet.cssText = i : e.$clipStyles.innerHTML = i;
          }
        }
      }, {
        key: "coverImage",
        value: function value() {
          var e = this,
            t = e.image.$container.getBoundingClientRect(),
            n = t.height,
            o = e.options.speed,
            i = "scroll" === e.options.type || "scroll-opacity" === e.options.type,
            a = 0,
            r = n,
            l = 0;
          return i && (o < 0 ? (a = o * Math.max(n, v), v < n && (a -= o * (n - v))) : a = o * (n + v), 1 < o ? r = Math.abs(a - v) : o < 0 ? r = a / o + Math.abs(a) : r += (v - n) * (1 - o), a /= 2), e.parallaxScrollDistance = a, l = i ? (v - r) / 2 : (n - r) / 2, e.css(e.image.$item, {
            height: "".concat(r, "px"),
            marginTop: "".concat(l, "px"),
            left: "fixed" === e.image.position ? "".concat(t.left, "px") : "0",
            width: "".concat(t.width, "px")
          }), e.options.onCoverImage && e.options.onCoverImage.call(e), {
            image: {
              height: r,
              marginTop: l
            },
            container: t
          };
        }
      }, {
        key: "isVisible",
        value: function value() {
          return this.isElementInViewport || !1;
        }
      }, {
        key: "onScroll",
        value: function value(e) {
          var t = this,
            n = t.$item.getBoundingClientRect(),
            o = n.top,
            i = n.height,
            a = {},
            r = n;
          if (t.options.elementInViewport && (r = t.options.elementInViewport.getBoundingClientRect()), t.isElementInViewport = 0 <= r.bottom && 0 <= r.right && r.top <= v && r.left <= b.window.innerWidth, e || t.isElementInViewport) {
            var l = Math.max(0, o),
              s = Math.max(0, i + o),
              c = Math.max(0, -o),
              u = Math.max(0, o + i - v),
              m = Math.max(0, i - (o + i - v)),
              p = Math.max(0, -o + v - i),
              d = 1 - 2 * (v - o) / (v + i),
              f = 1;
            if (i < v ? f = 1 - (c || u) / i : s <= v ? f = s / v : m <= v && (f = m / v), "opacity" !== t.options.type && "scale-opacity" !== t.options.type && "scroll-opacity" !== t.options.type || (a.transform = "translate3d(0,0,0)", a.opacity = f), "scale" === t.options.type || "scale-opacity" === t.options.type) {
              var g = 1;
              t.options.speed < 0 ? g -= t.options.speed * f : g += t.options.speed * (1 - f), a.transform = "scale(".concat(g, ") translate3d(0,0,0)");
            }
            if ("scroll" === t.options.type || "scroll-opacity" === t.options.type) {
              var y = t.parallaxScrollDistance * d;
              "absolute" === t.image.position && (y -= o), a.transform = "translate3d(0,".concat(y, "px,0)");
            }
            t.css(t.image.$item, a), t.options.onScroll && t.options.onScroll.call(t, {
              section: n,
              beforeTop: l,
              beforeTopEnd: s,
              afterTop: c,
              beforeBottom: u,
              beforeBottomEnd: m,
              afterBottom: p,
              visiblePercent: f,
              fromViewportCenter: d
            });
          }
        }
      }, {
        key: "onResize",
        value: function value() {
          this.coverImage(), this.clipContainer();
        }
      }]), s;
    }();
  h.constructor = w, t["default"] = h;
}, function (e, t, n) {
  var o = n(15),
    i = o.requestAnimationFrame || o.webkitRequestAnimationFrame || o.mozRequestAnimationFrame || function (e) {
      var t = +new Date(),
        n = Math.max(0, 16 - (t - a)),
        o = setTimeout(e, n);
      return a = t, o;
    },
    a = +new Date();
  var r = o.cancelAnimationFrame || o.webkitCancelAnimationFrame || o.mozCancelAnimationFrame || clearTimeout;
  Function.prototype.bind && (i = i.bind(o), r = r.bind(o)), (e.exports = i).cancel = r;
}, function (n, e, t) {
  (function (e) {
    var t;
    t = "undefined" != typeof window ? window : void 0 !== e ? e : "undefined" != typeof self ? self : {}, n.exports = t;
  }).call(this, t(5));
}]);

/***/ }),

/***/ "./resources/js/libs/jquery.animatedheadline.min.js":
/*!**********************************************************!*\
  !*** ./resources/js/libs/jquery.animatedheadline.min.js ***!
  \**********************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

!function (a) {
  a.fn.animatedHeadline = function (e) {
    function n(e) {
      e.each(function () {
        var e = a(this),
          n = e.text().split(""),
          s = e.hasClass("is-visible");
        for (i in n) e.parents(".rotate-2").length > 0 && (n[i] = "<em>" + n[i] + "</em>"), n[i] = s ? '<i class="in">' + n[i] + "</i>" : "<i>" + n[i] + "</i>";
        var t = n.join("");
        e.html(t).css("opacity", 1);
      });
    }
    function s(a) {
      var i = r(a);
      if (a.parents(".ah-headline").hasClass("type")) {
        var e = a.parent(".ah-words-wrapper");
        e.addClass("selected").removeClass("waiting"), setTimeout(function () {
          e.removeClass("selected"), a.removeClass("is-visible").addClass("is-hidden").children("i").removeClass("in").addClass("out");
        }, h.selectionDuration), setTimeout(function () {
          t(i, h.typeLettersDelay);
        }, h.typeAnimationDelay);
      } else if (a.parents(".ah-headline").hasClass("letters")) {
        var n = a.children("i").length >= i.children("i").length;
        l(a.find("i").eq(0), a, n, h.lettersDelay), o(i.find("i").eq(0), i, n, h.lettersDelay);
      } else a.parents(".ah-headline").hasClass("clip") ? a.parents(".ah-words-wrapper").animate({
        width: "2px"
      }, h.revealDuration, function () {
        d(a, i), t(i);
      }) : a.parents(".ah-headline").hasClass("loading-bar") ? (a.parents(".ah-words-wrapper").removeClass("is-loading"), d(a, i), setTimeout(function () {
        s(i);
      }, h.barAnimationDelay), setTimeout(function () {
        a.parents(".ah-words-wrapper").addClass("is-loading");
      }, h.barWaiting)) : (d(a, i), setTimeout(function () {
        s(i);
      }, h.animationDelay));
    }
    function t(a, i) {
      a.parents(".ah-headline").hasClass("type") ? (o(a.find("i").eq(0), a, !1, i), a.addClass("is-visible").removeClass("is-hidden")) : a.parents(".ah-headline").hasClass("clip") && a.parents(".ah-words-wrapper").animate({
        width: a.width() + 10
      }, h.revealDuration, function () {
        setTimeout(function () {
          s(a);
        }, h.revealAnimationDelay);
      });
    }
    function l(i, e, n, t) {
      if (i.removeClass("in").addClass("out"), i.is(":last-child") ? n && setTimeout(function () {
        s(r(e));
      }, h.animationDelay) : setTimeout(function () {
        l(i.next(), e, n, t);
      }, t), i.is(":last-child") && a("html").hasClass("no-csstransitions")) {
        var o = r(e);
        d(e, o);
      }
    }
    function o(a, i, e, n) {
      a.addClass("in").removeClass("out"), a.is(":last-child") ? (i.parents(".ah-headline").hasClass("type") && setTimeout(function () {
        i.parents(".ah-words-wrapper").addClass("waiting");
      }, 200), e || setTimeout(function () {
        s(i);
      }, h.animationDelay)) : setTimeout(function () {
        o(a.next(), i, e, n);
      }, n);
    }
    function r(a) {
      return a.is(":last-child") ? a.parent().children().eq(0) : a.next();
    }
    function d(a, i) {
      a.removeClass("is-visible").addClass("is-hidden"), i.removeClass("is-hidden").addClass("is-visible");
    }
    var h = a.extend({
        animationType: "rotate-1",
        animationDelay: 2500,
        barAnimationDelay: 3800,
        barWaiting: 800,
        lettersDelay: 50,
        typeLettersDelay: 150,
        selectionDuration: 500,
        typeAnimationDelay: 1300,
        revealDuration: 600,
        revealAnimationDelay: 1500
      }, e),
      p = h.animationDelay;
    this.each(function () {
      var i = a(this);
      if (h.animationType && ("type" == h.animationType || "rotate-2" == h.animationType || "rotate-3" == h.animationType || "scale" == h.animationType ? i.find(".ah-headline").addClass("letters " + h.animationType) : "clip" == h.animationType ? i.find(".ah-headline").addClass(h.animationType + " is-full-width") : i.find(".ah-headline").addClass(h.animationType)), n(a(".ah-headline.letters").find("b")), i.hasClass("loading-bar")) p = h.barAnimationDelay, setTimeout(function () {
        i.find(".ah-words-wrapper").addClass("is-loading");
      }, h.barWaiting);else if (i.hasClass("clip")) {
        var e = i.find(".ah-words-wrapper"),
          t = e.width() + 10;
        e.css("width", t);
      } else if (!i.find(".ah-headline").hasClass("type")) {
        var l = i.find(".ah-words-wrapper b"),
          o = 0;
        l.each(function () {
          var i = a(this).width();
          i > o && (o = i);
        }), i.find(".ah-words-wrapper").css("width", o);
      }
      setTimeout(function () {
        s(i.find(".is-visible").eq(0));
      }, p);
    });
  };
}(jQuery);

/***/ }),

/***/ "./resources/js/libs/jquery.counterup.min.js":
/*!***************************************************!*\
  !*** ./resources/js/libs/jquery.counterup.min.js ***!
  \***************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*!
* jquery.counterup.js 1.0
*
* Copyright 2013, Benjamin Intal http://gambit.ph @bfintal
* Released under the GPL v2 License
*
* Date: Nov 26, 2013
*/(function (e) {
  "use strict";

  e.fn.counterUp = function (t) {
    var n = e.extend({
      time: 400,
      delay: 10
    }, t);
    return this.each(function () {
      var t = e(this),
        r = n,
        i = function i() {
          var e = [],
            n = r.time / r.delay,
            i = t.text(),
            s = /[0-9]+,[0-9]+/.test(i);
          i = i.replace(/,/g, "");
          var o = /^[0-9]+$/.test(i),
            u = /^[0-9]+\.[0-9]+$/.test(i),
            a = u ? (i.split(".")[1] || []).length : 0;
          for (var f = n; f >= 1; f--) {
            var l = parseInt(i / n * f);
            u && (l = parseFloat(i / n * f).toFixed(a));
            if (s) while (/(\d+)(\d{3})/.test(l.toString())) l = l.toString().replace(/(\d+)(\d{3})/, "$1,$2");
            e.unshift(l);
          }
          t.data("counterup-nums", e);
          t.text("0");
          var c = function c() {
            t.text(t.data("counterup-nums").shift());
            if (t.data("counterup-nums").length) setTimeout(t.data("counterup-func"), r.delay);else {
              delete t.data("counterup-nums");
              t.data("counterup-nums", null);
              t.data("counterup-func", null);
            }
          };
          t.data("counterup-func", c);
          setTimeout(t.data("counterup-func"), r.delay);
        };
      t.waypoint(i, {
        offset: "100%",
        triggerOnce: !0
      });
    });
  };
})(jQuery);

/***/ }),

/***/ "./resources/js/libs/jquery.magnific-popup.min.js":
/*!********************************************************!*\
  !*** ./resources/js/libs/jquery.magnific-popup.min.js ***!
  \********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/*! Magnific Popup - v1.1.0 - 2016-02-20
* http://dimsemenov.com/plugins/magnific-popup/
* Copyright (c) 2016 Dmitry Semenov; */
!function (a) {
   true ? !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! jquery */ "./node_modules/jquery/dist/jquery.js")], __WEBPACK_AMD_DEFINE_FACTORY__ = (a),
				__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?
				(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__)) : undefined;
}(function (a) {
  var b,
    c,
    d,
    e,
    f,
    g,
    h = "Close",
    i = "BeforeClose",
    j = "AfterClose",
    k = "BeforeAppend",
    l = "MarkupParse",
    m = "Open",
    n = "Change",
    o = "mfp",
    p = "." + o,
    q = "mfp-ready",
    r = "mfp-removing",
    s = "mfp-prevent-close",
    t = function t() {},
    u = !!window.jQuery,
    v = a(window),
    w = function w(a, c) {
      b.ev.on(o + a + p, c);
    },
    x = function x(b, c, d, e) {
      var f = document.createElement("div");
      return f.className = "mfp-" + b, d && (f.innerHTML = d), e ? c && c.appendChild(f) : (f = a(f), c && f.appendTo(c)), f;
    },
    y = function y(c, d) {
      b.ev.triggerHandler(o + c, d), b.st.callbacks && (c = c.charAt(0).toLowerCase() + c.slice(1), b.st.callbacks[c] && b.st.callbacks[c].apply(b, a.isArray(d) ? d : [d]));
    },
    z = function z(c) {
      return c === g && b.currTemplate.closeBtn || (b.currTemplate.closeBtn = a(b.st.closeMarkup.replace("%title%", b.st.tClose)), g = c), b.currTemplate.closeBtn;
    },
    A = function A() {
      a.magnificPopup.instance || (b = new t(), b.init(), a.magnificPopup.instance = b);
    },
    B = function B() {
      var a = document.createElement("p").style,
        b = ["ms", "O", "Moz", "Webkit"];
      if (void 0 !== a.transition) return !0;
      for (; b.length;) if (b.pop() + "Transition" in a) return !0;
      return !1;
    };
  t.prototype = {
    constructor: t,
    init: function init() {
      var c = navigator.appVersion;
      b.isLowIE = b.isIE8 = document.all && !document.addEventListener, b.isAndroid = /android/gi.test(c), b.isIOS = /iphone|ipad|ipod/gi.test(c), b.supportsTransition = B(), b.probablyMobile = b.isAndroid || b.isIOS || /(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent), d = a(document), b.popupsCache = {};
    },
    open: function open(c) {
      var e;
      if (c.isObj === !1) {
        b.items = c.items.toArray(), b.index = 0;
        var g,
          h = c.items;
        for (e = 0; e < h.length; e++) if (g = h[e], g.parsed && (g = g.el[0]), g === c.el[0]) {
          b.index = e;
          break;
        }
      } else b.items = a.isArray(c.items) ? c.items : [c.items], b.index = c.index || 0;
      if (b.isOpen) return void b.updateItemHTML();
      b.types = [], f = "", c.mainEl && c.mainEl.length ? b.ev = c.mainEl.eq(0) : b.ev = d, c.key ? (b.popupsCache[c.key] || (b.popupsCache[c.key] = {}), b.currTemplate = b.popupsCache[c.key]) : b.currTemplate = {}, b.st = a.extend(!0, {}, a.magnificPopup.defaults, c), b.fixedContentPos = "auto" === b.st.fixedContentPos ? !b.probablyMobile : b.st.fixedContentPos, b.st.modal && (b.st.closeOnContentClick = !1, b.st.closeOnBgClick = !1, b.st.showCloseBtn = !1, b.st.enableEscapeKey = !1), b.bgOverlay || (b.bgOverlay = x("bg").on("click" + p, function () {
        b.close();
      }), b.wrap = x("wrap").attr("tabindex", -1).on("click" + p, function (a) {
        b._checkIfClose(a.target) && b.close();
      }), b.container = x("container", b.wrap)), b.contentContainer = x("content"), b.st.preloader && (b.preloader = x("preloader", b.container, b.st.tLoading));
      var i = a.magnificPopup.modules;
      for (e = 0; e < i.length; e++) {
        var j = i[e];
        j = j.charAt(0).toUpperCase() + j.slice(1), b["init" + j].call(b);
      }
      y("BeforeOpen"), b.st.showCloseBtn && (b.st.closeBtnInside ? (w(l, function (a, b, c, d) {
        c.close_replaceWith = z(d.type);
      }), f += " mfp-close-btn-in") : b.wrap.append(z())), b.st.alignTop && (f += " mfp-align-top"), b.fixedContentPos ? b.wrap.css({
        overflow: b.st.overflowY,
        overflowX: "hidden",
        overflowY: b.st.overflowY
      }) : b.wrap.css({
        top: v.scrollTop(),
        position: "absolute"
      }), (b.st.fixedBgPos === !1 || "auto" === b.st.fixedBgPos && !b.fixedContentPos) && b.bgOverlay.css({
        height: d.height(),
        position: "absolute"
      }), b.st.enableEscapeKey && d.on("keyup" + p, function (a) {
        27 === a.keyCode && b.close();
      }), v.on("resize" + p, function () {
        b.updateSize();
      }), b.st.closeOnContentClick || (f += " mfp-auto-cursor"), f && b.wrap.addClass(f);
      var k = b.wH = v.height(),
        n = {};
      if (b.fixedContentPos && b._hasScrollBar(k)) {
        var o = b._getScrollbarSize();
        o && (n.marginRight = o);
      }
      b.fixedContentPos && (b.isIE7 ? a("body, html").css("overflow", "hidden") : n.overflow = "hidden");
      var r = b.st.mainClass;
      return b.isIE7 && (r += " mfp-ie7"), r && b._addClassToMFP(r), b.updateItemHTML(), y("BuildControls"), a("html").css(n), b.bgOverlay.add(b.wrap).prependTo(b.st.prependTo || a(document.body)), b._lastFocusedEl = document.activeElement, setTimeout(function () {
        b.content ? (b._addClassToMFP(q), b._setFocus()) : b.bgOverlay.addClass(q), d.on("focusin" + p, b._onFocusIn);
      }, 16), b.isOpen = !0, b.updateSize(k), y(m), c;
    },
    close: function close() {
      b.isOpen && (y(i), b.isOpen = !1, b.st.removalDelay && !b.isLowIE && b.supportsTransition ? (b._addClassToMFP(r), setTimeout(function () {
        b._close();
      }, b.st.removalDelay)) : b._close());
    },
    _close: function _close() {
      y(h);
      var c = r + " " + q + " ";
      if (b.bgOverlay.detach(), b.wrap.detach(), b.container.empty(), b.st.mainClass && (c += b.st.mainClass + " "), b._removeClassFromMFP(c), b.fixedContentPos) {
        var e = {
          marginRight: ""
        };
        b.isIE7 ? a("body, html").css("overflow", "") : e.overflow = "", a("html").css(e);
      }
      d.off("keyup" + p + " focusin" + p), b.ev.off(p), b.wrap.attr("class", "mfp-wrap").removeAttr("style"), b.bgOverlay.attr("class", "mfp-bg"), b.container.attr("class", "mfp-container"), !b.st.showCloseBtn || b.st.closeBtnInside && b.currTemplate[b.currItem.type] !== !0 || b.currTemplate.closeBtn && b.currTemplate.closeBtn.detach(), b.st.autoFocusLast && b._lastFocusedEl && a(b._lastFocusedEl).focus(), b.currItem = null, b.content = null, b.currTemplate = null, b.prevHeight = 0, y(j);
    },
    updateSize: function updateSize(a) {
      if (b.isIOS) {
        var c = document.documentElement.clientWidth / window.innerWidth,
          d = window.innerHeight * c;
        b.wrap.css("height", d), b.wH = d;
      } else b.wH = a || v.height();
      b.fixedContentPos || b.wrap.css("height", b.wH), y("Resize");
    },
    updateItemHTML: function updateItemHTML() {
      var c = b.items[b.index];
      b.contentContainer.detach(), b.content && b.content.detach(), c.parsed || (c = b.parseEl(b.index));
      var d = c.type;
      if (y("BeforeChange", [b.currItem ? b.currItem.type : "", d]), b.currItem = c, !b.currTemplate[d]) {
        var f = b.st[d] ? b.st[d].markup : !1;
        y("FirstMarkupParse", f), f ? b.currTemplate[d] = a(f) : b.currTemplate[d] = !0;
      }
      e && e !== c.type && b.container.removeClass("mfp-" + e + "-holder");
      var g = b["get" + d.charAt(0).toUpperCase() + d.slice(1)](c, b.currTemplate[d]);
      b.appendContent(g, d), c.preloaded = !0, y(n, c), e = c.type, b.container.prepend(b.contentContainer), y("AfterChange");
    },
    appendContent: function appendContent(a, c) {
      b.content = a, a ? b.st.showCloseBtn && b.st.closeBtnInside && b.currTemplate[c] === !0 ? b.content.find(".mfp-close").length || b.content.append(z()) : b.content = a : b.content = "", y(k), b.container.addClass("mfp-" + c + "-holder"), b.contentContainer.append(b.content);
    },
    parseEl: function parseEl(c) {
      var d,
        e = b.items[c];
      if (e.tagName ? e = {
        el: a(e)
      } : (d = e.type, e = {
        data: e,
        src: e.src
      }), e.el) {
        for (var f = b.types, g = 0; g < f.length; g++) if (e.el.hasClass("mfp-" + f[g])) {
          d = f[g];
          break;
        }
        e.src = e.el.attr("data-mfp-src"), e.src || (e.src = e.el.attr("href"));
      }
      return e.type = d || b.st.type || "inline", e.index = c, e.parsed = !0, b.items[c] = e, y("ElementParse", e), b.items[c];
    },
    addGroup: function addGroup(a, c) {
      var d = function d(_d) {
        _d.mfpEl = this, b._openClick(_d, a, c);
      };
      c || (c = {});
      var e = "click.magnificPopup";
      c.mainEl = a, c.items ? (c.isObj = !0, a.off(e).on(e, d)) : (c.isObj = !1, c.delegate ? a.off(e).on(e, c.delegate, d) : (c.items = a, a.off(e).on(e, d)));
    },
    _openClick: function _openClick(c, d, e) {
      var f = void 0 !== e.midClick ? e.midClick : a.magnificPopup.defaults.midClick;
      if (f || !(2 === c.which || c.ctrlKey || c.metaKey || c.altKey || c.shiftKey)) {
        var g = void 0 !== e.disableOn ? e.disableOn : a.magnificPopup.defaults.disableOn;
        if (g) if (a.isFunction(g)) {
          if (!g.call(b)) return !0;
        } else if (v.width() < g) return !0;
        c.type && (c.preventDefault(), b.isOpen && c.stopPropagation()), e.el = a(c.mfpEl), e.delegate && (e.items = d.find(e.delegate)), b.open(e);
      }
    },
    updateStatus: function updateStatus(a, d) {
      if (b.preloader) {
        c !== a && b.container.removeClass("mfp-s-" + c), d || "loading" !== a || (d = b.st.tLoading);
        var e = {
          status: a,
          text: d
        };
        y("UpdateStatus", e), a = e.status, d = e.text, b.preloader.html(d), b.preloader.find("a").on("click", function (a) {
          a.stopImmediatePropagation();
        }), b.container.addClass("mfp-s-" + a), c = a;
      }
    },
    _checkIfClose: function _checkIfClose(c) {
      if (!a(c).hasClass(s)) {
        var d = b.st.closeOnContentClick,
          e = b.st.closeOnBgClick;
        if (d && e) return !0;
        if (!b.content || a(c).hasClass("mfp-close") || b.preloader && c === b.preloader[0]) return !0;
        if (c === b.content[0] || a.contains(b.content[0], c)) {
          if (d) return !0;
        } else if (e && a.contains(document, c)) return !0;
        return !1;
      }
    },
    _addClassToMFP: function _addClassToMFP(a) {
      b.bgOverlay.addClass(a), b.wrap.addClass(a);
    },
    _removeClassFromMFP: function _removeClassFromMFP(a) {
      this.bgOverlay.removeClass(a), b.wrap.removeClass(a);
    },
    _hasScrollBar: function _hasScrollBar(a) {
      return (b.isIE7 ? d.height() : document.body.scrollHeight) > (a || v.height());
    },
    _setFocus: function _setFocus() {
      (b.st.focus ? b.content.find(b.st.focus).eq(0) : b.wrap).focus();
    },
    _onFocusIn: function _onFocusIn(c) {
      return c.target === b.wrap[0] || a.contains(b.wrap[0], c.target) ? void 0 : (b._setFocus(), !1);
    },
    _parseMarkup: function _parseMarkup(b, c, d) {
      var e;
      d.data && (c = a.extend(d.data, c)), y(l, [b, c, d]), a.each(c, function (c, d) {
        if (void 0 === d || d === !1) return !0;
        if (e = c.split("_"), e.length > 1) {
          var f = b.find(p + "-" + e[0]);
          if (f.length > 0) {
            var g = e[1];
            "replaceWith" === g ? f[0] !== d[0] && f.replaceWith(d) : "img" === g ? f.is("img") ? f.attr("src", d) : f.replaceWith(a("<img>").attr("src", d).attr("class", f.attr("class"))) : f.attr(e[1], d);
          }
        } else b.find(p + "-" + c).html(d);
      });
    },
    _getScrollbarSize: function _getScrollbarSize() {
      if (void 0 === b.scrollbarSize) {
        var a = document.createElement("div");
        a.style.cssText = "width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;", document.body.appendChild(a), b.scrollbarSize = a.offsetWidth - a.clientWidth, document.body.removeChild(a);
      }
      return b.scrollbarSize;
    }
  }, a.magnificPopup = {
    instance: null,
    proto: t.prototype,
    modules: [],
    open: function open(b, c) {
      return A(), b = b ? a.extend(!0, {}, b) : {}, b.isObj = !0, b.index = c || 0, this.instance.open(b);
    },
    close: function close() {
      return a.magnificPopup.instance && a.magnificPopup.instance.close();
    },
    registerModule: function registerModule(b, c) {
      c.options && (a.magnificPopup.defaults[b] = c.options), a.extend(this.proto, c.proto), this.modules.push(b);
    },
    defaults: {
      disableOn: 0,
      key: null,
      midClick: !1,
      mainClass: "",
      preloader: !0,
      focus: "",
      closeOnContentClick: !1,
      closeOnBgClick: !0,
      closeBtnInside: !0,
      showCloseBtn: !0,
      enableEscapeKey: !0,
      modal: !1,
      alignTop: !1,
      removalDelay: 0,
      prependTo: null,
      fixedContentPos: "auto",
      fixedBgPos: "auto",
      overflowY: "auto",
      closeMarkup: '<button title="%title%" type="button" class="mfp-close">&#215;</button>',
      tClose: "Close (Esc)",
      tLoading: "Loading...",
      autoFocusLast: !0
    }
  }, a.fn.magnificPopup = function (c) {
    A();
    var d = a(this);
    if ("string" == typeof c) {
      if ("open" === c) {
        var e,
          f = u ? d.data("magnificPopup") : d[0].magnificPopup,
          g = parseInt(arguments[1], 10) || 0;
        f.items ? e = f.items[g] : (e = d, f.delegate && (e = e.find(f.delegate)), e = e.eq(g)), b._openClick({
          mfpEl: e
        }, d, f);
      } else b.isOpen && b[c].apply(b, Array.prototype.slice.call(arguments, 1));
    } else c = a.extend(!0, {}, c), u ? d.data("magnificPopup", c) : d[0].magnificPopup = c, b.addGroup(d, c);
    return d;
  };
  var C,
    D,
    E,
    F = "inline",
    G = function G() {
      E && (D.after(E.addClass(C)).detach(), E = null);
    };
  a.magnificPopup.registerModule(F, {
    options: {
      hiddenClass: "hide",
      markup: "",
      tNotFound: "Content not found"
    },
    proto: {
      initInline: function initInline() {
        b.types.push(F), w(h + "." + F, function () {
          G();
        });
      },
      getInline: function getInline(c, d) {
        if (G(), c.src) {
          var e = b.st.inline,
            f = a(c.src);
          if (f.length) {
            var g = f[0].parentNode;
            g && g.tagName && (D || (C = e.hiddenClass, D = x(C), C = "mfp-" + C), E = f.after(D).detach().removeClass(C)), b.updateStatus("ready");
          } else b.updateStatus("error", e.tNotFound), f = a("<div>");
          return c.inlineElement = f, f;
        }
        return b.updateStatus("ready"), b._parseMarkup(d, {}, c), d;
      }
    }
  });
  var H,
    I = "ajax",
    J = function J() {
      H && a(document.body).removeClass(H);
    },
    K = function K() {
      J(), b.req && b.req.abort();
    };
  a.magnificPopup.registerModule(I, {
    options: {
      settings: null,
      cursor: "mfp-ajax-cur",
      tError: '<a href="%url%">The content</a> could not be loaded.'
    },
    proto: {
      initAjax: function initAjax() {
        b.types.push(I), H = b.st.ajax.cursor, w(h + "." + I, K), w("BeforeChange." + I, K);
      },
      getAjax: function getAjax(c) {
        H && a(document.body).addClass(H), b.updateStatus("loading");
        var d = a.extend({
          url: c.src,
          success: function success(d, e, f) {
            var g = {
              data: d,
              xhr: f
            };
            y("ParseAjax", g), b.appendContent(a(g.data), I), c.finished = !0, J(), b._setFocus(), setTimeout(function () {
              b.wrap.addClass(q);
            }, 16), b.updateStatus("ready"), y("AjaxContentAdded");
          },
          error: function error() {
            J(), c.finished = c.loadError = !0, b.updateStatus("error", b.st.ajax.tError.replace("%url%", c.src));
          }
        }, b.st.ajax.settings);
        return b.req = a.ajax(d), "";
      }
    }
  });
  var L,
    M = function M(c) {
      if (c.data && void 0 !== c.data.title) return c.data.title;
      var d = b.st.image.titleSrc;
      if (d) {
        if (a.isFunction(d)) return d.call(b, c);
        if (c.el) return c.el.attr(d) || "";
      }
      return "";
    };
  a.magnificPopup.registerModule("image", {
    options: {
      markup: '<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',
      cursor: "mfp-zoom-out-cur",
      titleSrc: "title",
      verticalFit: !0,
      tError: '<a href="%url%">The image</a> could not be loaded.'
    },
    proto: {
      initImage: function initImage() {
        var c = b.st.image,
          d = ".image";
        b.types.push("image"), w(m + d, function () {
          "image" === b.currItem.type && c.cursor && a(document.body).addClass(c.cursor);
        }), w(h + d, function () {
          c.cursor && a(document.body).removeClass(c.cursor), v.off("resize" + p);
        }), w("Resize" + d, b.resizeImage), b.isLowIE && w("AfterChange", b.resizeImage);
      },
      resizeImage: function resizeImage() {
        var a = b.currItem;
        if (a && a.img && b.st.image.verticalFit) {
          var c = 0;
          b.isLowIE && (c = parseInt(a.img.css("padding-top"), 10) + parseInt(a.img.css("padding-bottom"), 10)), a.img.css("max-height", b.wH - c);
        }
      },
      _onImageHasSize: function _onImageHasSize(a) {
        a.img && (a.hasSize = !0, L && clearInterval(L), a.isCheckingImgSize = !1, y("ImageHasSize", a), a.imgHidden && (b.content && b.content.removeClass("mfp-loading"), a.imgHidden = !1));
      },
      findImageSize: function findImageSize(a) {
        var c = 0,
          d = a.img[0],
          e = function e(f) {
            L && clearInterval(L), L = setInterval(function () {
              return d.naturalWidth > 0 ? void b._onImageHasSize(a) : (c > 200 && clearInterval(L), c++, void (3 === c ? e(10) : 40 === c ? e(50) : 100 === c && e(500)));
            }, f);
          };
        e(1);
      },
      getImage: function getImage(c, d) {
        var e = 0,
          f = function f() {
            c && (c.img[0].complete ? (c.img.off(".mfploader"), c === b.currItem && (b._onImageHasSize(c), b.updateStatus("ready")), c.hasSize = !0, c.loaded = !0, y("ImageLoadComplete")) : (e++, 200 > e ? setTimeout(f, 100) : g()));
          },
          g = function g() {
            c && (c.img.off(".mfploader"), c === b.currItem && (b._onImageHasSize(c), b.updateStatus("error", h.tError.replace("%url%", c.src))), c.hasSize = !0, c.loaded = !0, c.loadError = !0);
          },
          h = b.st.image,
          i = d.find(".mfp-img");
        if (i.length) {
          var j = document.createElement("img");
          j.className = "mfp-img", c.el && c.el.find("img").length && (j.alt = c.el.find("img").attr("alt")), c.img = a(j).on("load.mfploader", f).on("error.mfploader", g), j.src = c.src, i.is("img") && (c.img = c.img.clone()), j = c.img[0], j.naturalWidth > 0 ? c.hasSize = !0 : j.width || (c.hasSize = !1);
        }
        return b._parseMarkup(d, {
          title: M(c),
          img_replaceWith: c.img
        }, c), b.resizeImage(), c.hasSize ? (L && clearInterval(L), c.loadError ? (d.addClass("mfp-loading"), b.updateStatus("error", h.tError.replace("%url%", c.src))) : (d.removeClass("mfp-loading"), b.updateStatus("ready")), d) : (b.updateStatus("loading"), c.loading = !0, c.hasSize || (c.imgHidden = !0, d.addClass("mfp-loading"), b.findImageSize(c)), d);
      }
    }
  });
  var N,
    O = function O() {
      return void 0 === N && (N = void 0 !== document.createElement("p").style.MozTransform), N;
    };
  a.magnificPopup.registerModule("zoom", {
    options: {
      enabled: !1,
      easing: "ease-in-out",
      duration: 300,
      opener: function opener(a) {
        return a.is("img") ? a : a.find("img");
      }
    },
    proto: {
      initZoom: function initZoom() {
        var a,
          c = b.st.zoom,
          d = ".zoom";
        if (c.enabled && b.supportsTransition) {
          var e,
            f,
            g = c.duration,
            j = function j(a) {
              var b = a.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),
                d = "all " + c.duration / 1e3 + "s " + c.easing,
                e = {
                  position: "fixed",
                  zIndex: 9999,
                  left: 0,
                  top: 0,
                  "-webkit-backface-visibility": "hidden"
                },
                f = "transition";
              return e["-webkit-" + f] = e["-moz-" + f] = e["-o-" + f] = e[f] = d, b.css(e), b;
            },
            k = function k() {
              b.content.css("visibility", "visible");
            };
          w("BuildControls" + d, function () {
            if (b._allowZoom()) {
              if (clearTimeout(e), b.content.css("visibility", "hidden"), a = b._getItemToZoom(), !a) return void k();
              f = j(a), f.css(b._getOffset()), b.wrap.append(f), e = setTimeout(function () {
                f.css(b._getOffset(!0)), e = setTimeout(function () {
                  k(), setTimeout(function () {
                    f.remove(), a = f = null, y("ZoomAnimationEnded");
                  }, 16);
                }, g);
              }, 16);
            }
          }), w(i + d, function () {
            if (b._allowZoom()) {
              if (clearTimeout(e), b.st.removalDelay = g, !a) {
                if (a = b._getItemToZoom(), !a) return;
                f = j(a);
              }
              f.css(b._getOffset(!0)), b.wrap.append(f), b.content.css("visibility", "hidden"), setTimeout(function () {
                f.css(b._getOffset());
              }, 16);
            }
          }), w(h + d, function () {
            b._allowZoom() && (k(), f && f.remove(), a = null);
          });
        }
      },
      _allowZoom: function _allowZoom() {
        return "image" === b.currItem.type;
      },
      _getItemToZoom: function _getItemToZoom() {
        return b.currItem.hasSize ? b.currItem.img : !1;
      },
      _getOffset: function _getOffset(c) {
        var d;
        d = c ? b.currItem.img : b.st.zoom.opener(b.currItem.el || b.currItem);
        var e = d.offset(),
          f = parseInt(d.css("padding-top"), 10),
          g = parseInt(d.css("padding-bottom"), 10);
        e.top -= a(window).scrollTop() - f;
        var h = {
          width: d.width(),
          height: (u ? d.innerHeight() : d[0].offsetHeight) - g - f
        };
        return O() ? h["-moz-transform"] = h.transform = "translate(" + e.left + "px," + e.top + "px)" : (h.left = e.left, h.top = e.top), h;
      }
    }
  });
  var P = "iframe",
    Q = "//about:blank",
    R = function R(a) {
      if (b.currTemplate[P]) {
        var c = b.currTemplate[P].find("iframe");
        c.length && (a || (c[0].src = Q), b.isIE8 && c.css("display", a ? "block" : "none"));
      }
    };
  a.magnificPopup.registerModule(P, {
    options: {
      markup: '<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',
      srcAction: "iframe_src",
      patterns: {
        youtube: {
          index: "youtube.com",
          id: "v=",
          src: "//www.youtube.com/embed/%id%?autoplay=1"
        },
        vimeo: {
          index: "vimeo.com/",
          id: "/",
          src: "//player.vimeo.com/video/%id%?autoplay=1"
        },
        gmaps: {
          index: "//maps.google.",
          src: "%id%&output=embed"
        }
      }
    },
    proto: {
      initIframe: function initIframe() {
        b.types.push(P), w("BeforeChange", function (a, b, c) {
          b !== c && (b === P ? R() : c === P && R(!0));
        }), w(h + "." + P, function () {
          R();
        });
      },
      getIframe: function getIframe(c, d) {
        var e = c.src,
          f = b.st.iframe;
        a.each(f.patterns, function () {
          return e.indexOf(this.index) > -1 ? (this.id && (e = "string" == typeof this.id ? e.substr(e.lastIndexOf(this.id) + this.id.length, e.length) : this.id.call(this, e)), e = this.src.replace("%id%", e), !1) : void 0;
        });
        var g = {};
        return f.srcAction && (g[f.srcAction] = e), b._parseMarkup(d, g, c), b.updateStatus("ready"), d;
      }
    }
  });
  var S = function S(a) {
      var c = b.items.length;
      return a > c - 1 ? a - c : 0 > a ? c + a : a;
    },
    T = function T(a, b, c) {
      return a.replace(/%curr%/gi, b + 1).replace(/%total%/gi, c);
    };
  a.magnificPopup.registerModule("gallery", {
    options: {
      enabled: !1,
      arrowMarkup: '<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',
      preload: [0, 2],
      navigateByImgClick: !0,
      arrows: !0,
      tPrev: "Previous (Left arrow key)",
      tNext: "Next (Right arrow key)",
      tCounter: "%curr% of %total%"
    },
    proto: {
      initGallery: function initGallery() {
        var c = b.st.gallery,
          e = ".mfp-gallery";
        return b.direction = !0, c && c.enabled ? (f += " mfp-gallery", w(m + e, function () {
          c.navigateByImgClick && b.wrap.on("click" + e, ".mfp-img", function () {
            return b.items.length > 1 ? (b.next(), !1) : void 0;
          }), d.on("keydown" + e, function (a) {
            37 === a.keyCode ? b.prev() : 39 === a.keyCode && b.next();
          });
        }), w("UpdateStatus" + e, function (a, c) {
          c.text && (c.text = T(c.text, b.currItem.index, b.items.length));
        }), w(l + e, function (a, d, e, f) {
          var g = b.items.length;
          e.counter = g > 1 ? T(c.tCounter, f.index, g) : "";
        }), w("BuildControls" + e, function () {
          if (b.items.length > 1 && c.arrows && !b.arrowLeft) {
            var d = c.arrowMarkup,
              e = b.arrowLeft = a(d.replace(/%title%/gi, c.tPrev).replace(/%dir%/gi, "left")).addClass(s),
              f = b.arrowRight = a(d.replace(/%title%/gi, c.tNext).replace(/%dir%/gi, "right")).addClass(s);
            e.click(function () {
              b.prev();
            }), f.click(function () {
              b.next();
            }), b.container.append(e.add(f));
          }
        }), w(n + e, function () {
          b._preloadTimeout && clearTimeout(b._preloadTimeout), b._preloadTimeout = setTimeout(function () {
            b.preloadNearbyImages(), b._preloadTimeout = null;
          }, 16);
        }), void w(h + e, function () {
          d.off(e), b.wrap.off("click" + e), b.arrowRight = b.arrowLeft = null;
        })) : !1;
      },
      next: function next() {
        b.direction = !0, b.index = S(b.index + 1), b.updateItemHTML();
      },
      prev: function prev() {
        b.direction = !1, b.index = S(b.index - 1), b.updateItemHTML();
      },
      goTo: function goTo(a) {
        b.direction = a >= b.index, b.index = a, b.updateItemHTML();
      },
      preloadNearbyImages: function preloadNearbyImages() {
        var a,
          c = b.st.gallery.preload,
          d = Math.min(c[0], b.items.length),
          e = Math.min(c[1], b.items.length);
        for (a = 1; a <= (b.direction ? e : d); a++) b._preloadItem(b.index + a);
        for (a = 1; a <= (b.direction ? d : e); a++) b._preloadItem(b.index - a);
      },
      _preloadItem: function _preloadItem(c) {
        if (c = S(c), !b.items[c].preloaded) {
          var d = b.items[c];
          d.parsed || (d = b.parseEl(c)), y("LazyLoad", d), "image" === d.type && (d.img = a('<img class="mfp-img" />').on("load.mfploader", function () {
            d.hasSize = !0;
          }).on("error.mfploader", function () {
            d.hasSize = !0, d.loadError = !0, y("LazyLoadError", d);
          }).attr("src", d.src)), d.preloaded = !0;
        }
      }
    }
  });
  var U = "retina";
  a.magnificPopup.registerModule(U, {
    options: {
      replaceSrc: function replaceSrc(a) {
        return a.src.replace(/\.\w+$/, function (a) {
          return "@2x" + a;
        });
      },
      ratio: 1
    },
    proto: {
      initRetina: function initRetina() {
        if (window.devicePixelRatio > 1) {
          var a = b.st.retina,
            c = a.ratio;
          c = isNaN(c) ? c() : c, c > 1 && (w("ImageHasSize." + U, function (a, b) {
            b.img.css({
              "max-width": b.img[0].naturalWidth / c,
              width: "100%"
            });
          }), w("ElementParse." + U, function (b, d) {
            d.src = a.replaceSrc(d, c);
          }));
        }
      }
    }
  }), A();
});

/***/ }),

/***/ "./resources/js/libs/owl.carousel.min.js":
/*!***********************************************!*\
  !*** ./resources/js/libs/owl.carousel.min.js ***!
  \***********************************************/
/*! no static exports found */
/***/ (function(module, exports) {

function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
/**
 * Owl Carousel v2.2.1
 * Copyright 2013-2017 Saurabh Sharma
 * Licensed under  ()
 */
!function (a, b, c, d) {
  function e(b, c) {
    this.settings = null, this.options = a.extend({}, e.Defaults, c), this.$element = a(b), this._handlers = {}, this._plugins = {}, this._supress = {}, this._current = null, this._speed = null, this._coordinates = [], this._breakpoint = null, this._width = null, this._items = [], this._clones = [], this._mergers = [], this._widths = [], this._invalidated = {}, this._pipe = [], this._drag = {
      time: null,
      target: null,
      pointer: null,
      stage: {
        start: null,
        current: null
      },
      direction: null
    }, this._states = {
      current: {},
      tags: {
        initializing: ["busy"],
        animating: ["busy"],
        dragging: ["interacting"]
      }
    }, a.each(["onResize", "onThrottledResize"], a.proxy(function (b, c) {
      this._handlers[c] = a.proxy(this[c], this);
    }, this)), a.each(e.Plugins, a.proxy(function (a, b) {
      this._plugins[a.charAt(0).toLowerCase() + a.slice(1)] = new b(this);
    }, this)), a.each(e.Workers, a.proxy(function (b, c) {
      this._pipe.push({
        filter: c.filter,
        run: a.proxy(c.run, this)
      });
    }, this)), this.setup(), this.initialize();
  }
  e.Defaults = {
    items: 3,
    loop: !1,
    center: !1,
    rewind: !1,
    mouseDrag: !0,
    touchDrag: !0,
    pullDrag: !0,
    freeDrag: !1,
    margin: 0,
    stagePadding: 0,
    merge: !1,
    mergeFit: !0,
    autoWidth: !1,
    startPosition: 0,
    rtl: !1,
    smartSpeed: 250,
    fluidSpeed: !1,
    dragEndSpeed: !1,
    responsive: {},
    responsiveRefreshRate: 200,
    responsiveBaseElement: b,
    fallbackEasing: "swing",
    info: !1,
    nestedItemSelector: !1,
    itemElement: "div",
    stageElement: "div",
    refreshClass: "owl-refresh",
    loadedClass: "owl-loaded",
    loadingClass: "owl-loading",
    rtlClass: "owl-rtl",
    responsiveClass: "owl-responsive",
    dragClass: "owl-drag",
    itemClass: "owl-item",
    stageClass: "owl-stage",
    stageOuterClass: "owl-stage-outer",
    grabClass: "owl-grab"
  }, e.Width = {
    Default: "default",
    Inner: "inner",
    Outer: "outer"
  }, e.Type = {
    Event: "event",
    State: "state"
  }, e.Plugins = {}, e.Workers = [{
    filter: ["width", "settings"],
    run: function run() {
      this._width = this.$element.width();
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run(a) {
      a.current = this._items && this._items[this.relative(this._current)];
    }
  }, {
    filter: ["items", "settings"],
    run: function run() {
      this.$stage.children(".cloned").remove();
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run(a) {
      var b = this.settings.margin || "",
        c = !this.settings.autoWidth,
        d = this.settings.rtl,
        e = {
          width: "auto",
          "margin-left": d ? b : "",
          "margin-right": d ? "" : b
        };
      !c && this.$stage.children().css(e), a.css = e;
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run(a) {
      var b = (this.width() / this.settings.items).toFixed(3) - this.settings.margin,
        c = null,
        d = this._items.length,
        e = !this.settings.autoWidth,
        f = [];
      for (a.items = {
        merge: !1,
        width: b
      }; d--;) c = this._mergers[d], c = this.settings.mergeFit && Math.min(c, this.settings.items) || c, a.items.merge = c > 1 || a.items.merge, f[d] = e ? b * c : this._items[d].width();
      this._widths = f;
    }
  }, {
    filter: ["items", "settings"],
    run: function run() {
      var b = [],
        c = this._items,
        d = this.settings,
        e = Math.max(2 * d.items, 4),
        f = 2 * Math.ceil(c.length / 2),
        g = d.loop && c.length ? d.rewind ? e : Math.max(e, f) : 0;
      for (g /= 2; g--;) b.push(this.normalize(b.length / 2, !0)), a(c[b[b.length - 1]][0]).clone(!0).addClass("cloned").appendTo(this.$stage), b.push(this.normalize(c.length - 1 - (b.length - 1) / 2, !0)), a(c[b[b.length - 1]][0]).clone(!0).addClass("cloned").prependTo(this.$stage);
      this._clones = b;
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run() {
      for (var a = this.settings.rtl ? 1 : -1, b = this._clones.length + this._items.length, c = -1, d = 0, e = 0, f = []; ++c < b;) d = f[c - 1] || 0, e = this._widths[this.relative(c)] + this.settings.margin, f.push(d + e * a);
      this._coordinates = f;
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run() {
      var a = this.settings.stagePadding,
        b = this._coordinates,
        c = {
          width: Math.ceil(Math.abs(b[b.length - 1])) + 2 * a,
          "padding-left": a || "",
          "padding-right": a || ""
        };
      this.$stage.css(c);
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run(a) {
      var b = this._coordinates.length,
        c = !this.settings.autoWidth,
        d = this.$stage.children();
      if (c && a.items.merge) for (; b--;) a.css.width = this._widths[this.relative(b)], d.eq(b).css(a.css);else c && (a.css.width = a.items.width, d.css(a.css));
    }
  }, {
    filter: ["items"],
    run: function run() {
      this._coordinates.length < 1 && this.$stage.removeAttr("style");
    }
  }, {
    filter: ["width", "items", "settings"],
    run: function run(a) {
      a.current = a.current ? this.$stage.children().index(a.current) : 0, a.current = Math.max(this.minimum(), Math.min(this.maximum(), a.current)), this.reset(a.current);
    }
  }, {
    filter: ["position"],
    run: function run() {
      this.animate(this.coordinates(this._current));
    }
  }, {
    filter: ["width", "position", "items", "settings"],
    run: function run() {
      var a,
        b,
        c,
        d,
        e = this.settings.rtl ? 1 : -1,
        f = 2 * this.settings.stagePadding,
        g = this.coordinates(this.current()) + f,
        h = g + this.width() * e,
        i = [];
      for (c = 0, d = this._coordinates.length; c < d; c++) a = this._coordinates[c - 1] || 0, b = Math.abs(this._coordinates[c]) + f * e, (this.op(a, "<=", g) && this.op(a, ">", h) || this.op(b, "<", g) && this.op(b, ">", h)) && i.push(c);
      this.$stage.children(".active").removeClass("active"), this.$stage.children(":eq(" + i.join("), :eq(") + ")").addClass("active"), this.settings.center && (this.$stage.children(".center").removeClass("center"), this.$stage.children().eq(this.current()).addClass("center"));
    }
  }], e.prototype.initialize = function () {
    if (this.enter("initializing"), this.trigger("initialize"), this.$element.toggleClass(this.settings.rtlClass, this.settings.rtl), this.settings.autoWidth && !this.is("pre-loading")) {
      var b, c, e;
      b = this.$element.find("img"), c = this.settings.nestedItemSelector ? "." + this.settings.nestedItemSelector : d, e = this.$element.children(c).width(), b.length && e <= 0 && this.preloadAutoWidthImages(b);
    }
    this.$element.addClass(this.options.loadingClass), this.$stage = a("<" + this.settings.stageElement + ' class="' + this.settings.stageClass + '"/>').wrap('<div class="' + this.settings.stageOuterClass + '"/>'), this.$element.append(this.$stage.parent()), this.replace(this.$element.children().not(this.$stage.parent())), this.$element.is(":visible") ? this.refresh() : this.invalidate("width"), this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass), this.registerEventHandlers(), this.leave("initializing"), this.trigger("initialized");
  }, e.prototype.setup = function () {
    var b = this.viewport(),
      c = this.options.responsive,
      d = -1,
      e = null;
    c ? (a.each(c, function (a) {
      a <= b && a > d && (d = Number(a));
    }), e = a.extend({}, this.options, c[d]), "function" == typeof e.stagePadding && (e.stagePadding = e.stagePadding()), delete e.responsive, e.responsiveClass && this.$element.attr("class", this.$element.attr("class").replace(new RegExp("(" + this.options.responsiveClass + "-)\\S+\\s", "g"), "$1" + d))) : e = a.extend({}, this.options), this.trigger("change", {
      property: {
        name: "settings",
        value: e
      }
    }), this._breakpoint = d, this.settings = e, this.invalidate("settings"), this.trigger("changed", {
      property: {
        name: "settings",
        value: this.settings
      }
    });
  }, e.prototype.optionsLogic = function () {
    this.settings.autoWidth && (this.settings.stagePadding = !1, this.settings.merge = !1);
  }, e.prototype.prepare = function (b) {
    var c = this.trigger("prepare", {
      content: b
    });
    return c.data || (c.data = a("<" + this.settings.itemElement + "/>").addClass(this.options.itemClass).append(b)), this.trigger("prepared", {
      content: c.data
    }), c.data;
  }, e.prototype.update = function () {
    for (var b = 0, c = this._pipe.length, d = a.proxy(function (a) {
        return this[a];
      }, this._invalidated), e = {}; b < c;) (this._invalidated.all || a.grep(this._pipe[b].filter, d).length > 0) && this._pipe[b].run(e), b++;
    this._invalidated = {}, !this.is("valid") && this.enter("valid");
  }, e.prototype.width = function (a) {
    switch (a = a || e.Width.Default) {
      case e.Width.Inner:
      case e.Width.Outer:
        return this._width;
      default:
        return this._width - 2 * this.settings.stagePadding + this.settings.margin;
    }
  }, e.prototype.refresh = function () {
    this.enter("refreshing"), this.trigger("refresh"), this.setup(), this.optionsLogic(), this.$element.addClass(this.options.refreshClass), this.update(), this.$element.removeClass(this.options.refreshClass), this.leave("refreshing"), this.trigger("refreshed");
  }, e.prototype.onThrottledResize = function () {
    b.clearTimeout(this.resizeTimer), this.resizeTimer = b.setTimeout(this._handlers.onResize, this.settings.responsiveRefreshRate);
  }, e.prototype.onResize = function () {
    return !!this._items.length && this._width !== this.$element.width() && !!this.$element.is(":visible") && (this.enter("resizing"), this.trigger("resize").isDefaultPrevented() ? (this.leave("resizing"), !1) : (this.invalidate("width"), this.refresh(), this.leave("resizing"), void this.trigger("resized")));
  }, e.prototype.registerEventHandlers = function () {
    a.support.transition && this.$stage.on(a.support.transition.end + ".owl.core", a.proxy(this.onTransitionEnd, this)), !1 !== this.settings.responsive && this.on(b, "resize", this._handlers.onThrottledResize), this.settings.mouseDrag && (this.$element.addClass(this.options.dragClass), this.$stage.on("mousedown.owl.core", a.proxy(this.onDragStart, this)), this.$stage.on("dragstart.owl.core selectstart.owl.core", function () {
      return !1;
    })), this.settings.touchDrag && (this.$stage.on("touchstart.owl.core", a.proxy(this.onDragStart, this)), this.$stage.on("touchcancel.owl.core", a.proxy(this.onDragEnd, this)));
  }, e.prototype.onDragStart = function (b) {
    var d = null;
    3 !== b.which && (a.support.transform ? (d = this.$stage.css("transform").replace(/.*\(|\)| /g, "").split(","), d = {
      x: d[16 === d.length ? 12 : 4],
      y: d[16 === d.length ? 13 : 5]
    }) : (d = this.$stage.position(), d = {
      x: this.settings.rtl ? d.left + this.$stage.width() - this.width() + this.settings.margin : d.left,
      y: d.top
    }), this.is("animating") && (a.support.transform ? this.animate(d.x) : this.$stage.stop(), this.invalidate("position")), this.$element.toggleClass(this.options.grabClass, "mousedown" === b.type), this.speed(0), this._drag.time = new Date().getTime(), this._drag.target = a(b.target), this._drag.stage.start = d, this._drag.stage.current = d, this._drag.pointer = this.pointer(b), a(c).on("mouseup.owl.core touchend.owl.core", a.proxy(this.onDragEnd, this)), a(c).one("mousemove.owl.core touchmove.owl.core", a.proxy(function (b) {
      var d = this.difference(this._drag.pointer, this.pointer(b));
      a(c).on("mousemove.owl.core touchmove.owl.core", a.proxy(this.onDragMove, this)), Math.abs(d.x) < Math.abs(d.y) && this.is("valid") || (b.preventDefault(), this.enter("dragging"), this.trigger("drag"));
    }, this)));
  }, e.prototype.onDragMove = function (a) {
    var b = null,
      c = null,
      d = null,
      e = this.difference(this._drag.pointer, this.pointer(a)),
      f = this.difference(this._drag.stage.start, e);
    this.is("dragging") && (a.preventDefault(), this.settings.loop ? (b = this.coordinates(this.minimum()), c = this.coordinates(this.maximum() + 1) - b, f.x = ((f.x - b) % c + c) % c + b) : (b = this.settings.rtl ? this.coordinates(this.maximum()) : this.coordinates(this.minimum()), c = this.settings.rtl ? this.coordinates(this.minimum()) : this.coordinates(this.maximum()), d = this.settings.pullDrag ? -1 * e.x / 5 : 0, f.x = Math.max(Math.min(f.x, b + d), c + d)), this._drag.stage.current = f, this.animate(f.x));
  }, e.prototype.onDragEnd = function (b) {
    var d = this.difference(this._drag.pointer, this.pointer(b)),
      e = this._drag.stage.current,
      f = d.x > 0 ^ this.settings.rtl ? "left" : "right";
    a(c).off(".owl.core"), this.$element.removeClass(this.options.grabClass), (0 !== d.x && this.is("dragging") || !this.is("valid")) && (this.speed(this.settings.dragEndSpeed || this.settings.smartSpeed), this.current(this.closest(e.x, 0 !== d.x ? f : this._drag.direction)), this.invalidate("position"), this.update(), this._drag.direction = f, (Math.abs(d.x) > 3 || new Date().getTime() - this._drag.time > 300) && this._drag.target.one("click.owl.core", function () {
      return !1;
    })), this.is("dragging") && (this.leave("dragging"), this.trigger("dragged"));
  }, e.prototype.closest = function (b, c) {
    var d = -1,
      e = 30,
      f = this.width(),
      g = this.coordinates();
    return this.settings.freeDrag || a.each(g, a.proxy(function (a, h) {
      return "left" === c && b > h - e && b < h + e ? d = a : "right" === c && b > h - f - e && b < h - f + e ? d = a + 1 : this.op(b, "<", h) && this.op(b, ">", g[a + 1] || h - f) && (d = "left" === c ? a + 1 : a), -1 === d;
    }, this)), this.settings.loop || (this.op(b, ">", g[this.minimum()]) ? d = b = this.minimum() : this.op(b, "<", g[this.maximum()]) && (d = b = this.maximum())), d;
  }, e.prototype.animate = function (b) {
    var c = this.speed() > 0;
    this.is("animating") && this.onTransitionEnd(), c && (this.enter("animating"), this.trigger("translate")), a.support.transform3d && a.support.transition ? this.$stage.css({
      transform: "translate3d(" + b + "px,0px,0px)",
      transition: this.speed() / 1e3 + "s"
    }) : c ? this.$stage.animate({
      left: b + "px"
    }, this.speed(), this.settings.fallbackEasing, a.proxy(this.onTransitionEnd, this)) : this.$stage.css({
      left: b + "px"
    });
  }, e.prototype.is = function (a) {
    return this._states.current[a] && this._states.current[a] > 0;
  }, e.prototype.current = function (a) {
    if (a === d) return this._current;
    if (0 === this._items.length) return d;
    if (a = this.normalize(a), this._current !== a) {
      var b = this.trigger("change", {
        property: {
          name: "position",
          value: a
        }
      });
      b.data !== d && (a = this.normalize(b.data)), this._current = a, this.invalidate("position"), this.trigger("changed", {
        property: {
          name: "position",
          value: this._current
        }
      });
    }
    return this._current;
  }, e.prototype.invalidate = function (b) {
    return "string" === a.type(b) && (this._invalidated[b] = !0, this.is("valid") && this.leave("valid")), a.map(this._invalidated, function (a, b) {
      return b;
    });
  }, e.prototype.reset = function (a) {
    (a = this.normalize(a)) !== d && (this._speed = 0, this._current = a, this.suppress(["translate", "translated"]), this.animate(this.coordinates(a)), this.release(["translate", "translated"]));
  }, e.prototype.normalize = function (a, b) {
    var c = this._items.length,
      e = b ? 0 : this._clones.length;
    return !this.isNumeric(a) || c < 1 ? a = d : (a < 0 || a >= c + e) && (a = ((a - e / 2) % c + c) % c + e / 2), a;
  }, e.prototype.relative = function (a) {
    return a -= this._clones.length / 2, this.normalize(a, !0);
  }, e.prototype.maximum = function (a) {
    var b,
      c,
      d,
      e = this.settings,
      f = this._coordinates.length;
    if (e.loop) f = this._clones.length / 2 + this._items.length - 1;else if (e.autoWidth || e.merge) {
      for (b = this._items.length, c = this._items[--b].width(), d = this.$element.width(); b-- && !((c += this._items[b].width() + this.settings.margin) > d););
      f = b + 1;
    } else f = e.center ? this._items.length - 1 : this._items.length - e.items;
    return a && (f -= this._clones.length / 2), Math.max(f, 0);
  }, e.prototype.minimum = function (a) {
    return a ? 0 : this._clones.length / 2;
  }, e.prototype.items = function (a) {
    return a === d ? this._items.slice() : (a = this.normalize(a, !0), this._items[a]);
  }, e.prototype.mergers = function (a) {
    return a === d ? this._mergers.slice() : (a = this.normalize(a, !0), this._mergers[a]);
  }, e.prototype.clones = function (b) {
    var c = this._clones.length / 2,
      e = c + this._items.length,
      f = function f(a) {
        return a % 2 == 0 ? e + a / 2 : c - (a + 1) / 2;
      };
    return b === d ? a.map(this._clones, function (a, b) {
      return f(b);
    }) : a.map(this._clones, function (a, c) {
      return a === b ? f(c) : null;
    });
  }, e.prototype.speed = function (a) {
    return a !== d && (this._speed = a), this._speed;
  }, e.prototype.coordinates = function (b) {
    var c,
      e = 1,
      f = b - 1;
    return b === d ? a.map(this._coordinates, a.proxy(function (a, b) {
      return this.coordinates(b);
    }, this)) : (this.settings.center ? (this.settings.rtl && (e = -1, f = b + 1), c = this._coordinates[b], c += (this.width() - c + (this._coordinates[f] || 0)) / 2 * e) : c = this._coordinates[f] || 0, c = Math.ceil(c));
  }, e.prototype.duration = function (a, b, c) {
    return 0 === c ? 0 : Math.min(Math.max(Math.abs(b - a), 1), 6) * Math.abs(c || this.settings.smartSpeed);
  }, e.prototype.to = function (a, b) {
    var c = this.current(),
      d = null,
      e = a - this.relative(c),
      f = (e > 0) - (e < 0),
      g = this._items.length,
      h = this.minimum(),
      i = this.maximum();
    this.settings.loop ? (!this.settings.rewind && Math.abs(e) > g / 2 && (e += -1 * f * g), a = c + e, (d = ((a - h) % g + g) % g + h) !== a && d - e <= i && d - e > 0 && (c = d - e, a = d, this.reset(c))) : this.settings.rewind ? (i += 1, a = (a % i + i) % i) : a = Math.max(h, Math.min(i, a)), this.speed(this.duration(c, a, b)), this.current(a), this.$element.is(":visible") && this.update();
  }, e.prototype.next = function (a) {
    a = a || !1, this.to(this.relative(this.current()) + 1, a);
  }, e.prototype.prev = function (a) {
    a = a || !1, this.to(this.relative(this.current()) - 1, a);
  }, e.prototype.onTransitionEnd = function (a) {
    if (a !== d && (a.stopPropagation(), (a.target || a.srcElement || a.originalTarget) !== this.$stage.get(0))) return !1;
    this.leave("animating"), this.trigger("translated");
  }, e.prototype.viewport = function () {
    var d;
    return this.options.responsiveBaseElement !== b ? d = a(this.options.responsiveBaseElement).width() : b.innerWidth ? d = b.innerWidth : c.documentElement && c.documentElement.clientWidth ? d = c.documentElement.clientWidth : console.warn("Can not detect viewport width."), d;
  }, e.prototype.replace = function (b) {
    this.$stage.empty(), this._items = [], b && (b = b instanceof jQuery ? b : a(b)), this.settings.nestedItemSelector && (b = b.find("." + this.settings.nestedItemSelector)), b.filter(function () {
      return 1 === this.nodeType;
    }).each(a.proxy(function (a, b) {
      b = this.prepare(b), this.$stage.append(b), this._items.push(b), this._mergers.push(1 * b.find("[data-merge]").addBack("[data-merge]").attr("data-merge") || 1);
    }, this)), this.reset(this.isNumeric(this.settings.startPosition) ? this.settings.startPosition : 0), this.invalidate("items");
  }, e.prototype.add = function (b, c) {
    var e = this.relative(this._current);
    c = c === d ? this._items.length : this.normalize(c, !0), b = b instanceof jQuery ? b : a(b), this.trigger("add", {
      content: b,
      position: c
    }), b = this.prepare(b), 0 === this._items.length || c === this._items.length ? (0 === this._items.length && this.$stage.append(b), 0 !== this._items.length && this._items[c - 1].after(b), this._items.push(b), this._mergers.push(1 * b.find("[data-merge]").addBack("[data-merge]").attr("data-merge") || 1)) : (this._items[c].before(b), this._items.splice(c, 0, b), this._mergers.splice(c, 0, 1 * b.find("[data-merge]").addBack("[data-merge]").attr("data-merge") || 1)), this._items[e] && this.reset(this._items[e].index()), this.invalidate("items"), this.trigger("added", {
      content: b,
      position: c
    });
  }, e.prototype.remove = function (a) {
    (a = this.normalize(a, !0)) !== d && (this.trigger("remove", {
      content: this._items[a],
      position: a
    }), this._items[a].remove(), this._items.splice(a, 1), this._mergers.splice(a, 1), this.invalidate("items"), this.trigger("removed", {
      content: null,
      position: a
    }));
  }, e.prototype.preloadAutoWidthImages = function (b) {
    b.each(a.proxy(function (b, c) {
      this.enter("pre-loading"), c = a(c), a(new Image()).one("load", a.proxy(function (a) {
        c.attr("src", a.target.src), c.css("opacity", 1), this.leave("pre-loading"), !this.is("pre-loading") && !this.is("initializing") && this.refresh();
      }, this)).attr("src", c.attr("src") || c.attr("data-src") || c.attr("data-src-retina"));
    }, this));
  }, e.prototype.destroy = function () {
    this.$element.off(".owl.core"), this.$stage.off(".owl.core"), a(c).off(".owl.core"), !1 !== this.settings.responsive && (b.clearTimeout(this.resizeTimer), this.off(b, "resize", this._handlers.onThrottledResize));
    for (var d in this._plugins) this._plugins[d].destroy();
    this.$stage.children(".cloned").remove(), this.$stage.unwrap(), this.$stage.children().contents().unwrap(), this.$stage.children().unwrap(), this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class", this.$element.attr("class").replace(new RegExp(this.options.responsiveClass + "-\\S+\\s", "g"), "")).removeData("owl.carousel");
  }, e.prototype.op = function (a, b, c) {
    var d = this.settings.rtl;
    switch (b) {
      case "<":
        return d ? a > c : a < c;
      case ">":
        return d ? a < c : a > c;
      case ">=":
        return d ? a <= c : a >= c;
      case "<=":
        return d ? a >= c : a <= c;
    }
  }, e.prototype.on = function (a, b, c, d) {
    a.addEventListener ? a.addEventListener(b, c, d) : a.attachEvent && a.attachEvent("on" + b, c);
  }, e.prototype.off = function (a, b, c, d) {
    a.removeEventListener ? a.removeEventListener(b, c, d) : a.detachEvent && a.detachEvent("on" + b, c);
  }, e.prototype.trigger = function (b, c, d, f, g) {
    var h = {
        item: {
          count: this._items.length,
          index: this.current()
        }
      },
      i = a.camelCase(a.grep(["on", b, d], function (a) {
        return a;
      }).join("-").toLowerCase()),
      j = a.Event([b, "owl", d || "carousel"].join(".").toLowerCase(), a.extend({
        relatedTarget: this
      }, h, c));
    return this._supress[b] || (a.each(this._plugins, function (a, b) {
      b.onTrigger && b.onTrigger(j);
    }), this.register({
      type: e.Type.Event,
      name: b
    }), this.$element.trigger(j), this.settings && "function" == typeof this.settings[i] && this.settings[i].call(this, j)), j;
  }, e.prototype.enter = function (b) {
    a.each([b].concat(this._states.tags[b] || []), a.proxy(function (a, b) {
      this._states.current[b] === d && (this._states.current[b] = 0), this._states.current[b]++;
    }, this));
  }, e.prototype.leave = function (b) {
    a.each([b].concat(this._states.tags[b] || []), a.proxy(function (a, b) {
      this._states.current[b]--;
    }, this));
  }, e.prototype.register = function (b) {
    if (b.type === e.Type.Event) {
      if (a.event.special[b.name] || (a.event.special[b.name] = {}), !a.event.special[b.name].owl) {
        var c = a.event.special[b.name]._default;
        a.event.special[b.name]._default = function (a) {
          return !c || !c.apply || a.namespace && -1 !== a.namespace.indexOf("owl") ? a.namespace && a.namespace.indexOf("owl") > -1 : c.apply(this, arguments);
        }, a.event.special[b.name].owl = !0;
      }
    } else b.type === e.Type.State && (this._states.tags[b.name] ? this._states.tags[b.name] = this._states.tags[b.name].concat(b.tags) : this._states.tags[b.name] = b.tags, this._states.tags[b.name] = a.grep(this._states.tags[b.name], a.proxy(function (c, d) {
      return a.inArray(c, this._states.tags[b.name]) === d;
    }, this)));
  }, e.prototype.suppress = function (b) {
    a.each(b, a.proxy(function (a, b) {
      this._supress[b] = !0;
    }, this));
  }, e.prototype.release = function (b) {
    a.each(b, a.proxy(function (a, b) {
      delete this._supress[b];
    }, this));
  }, e.prototype.pointer = function (a) {
    var c = {
      x: null,
      y: null
    };
    return a = a.originalEvent || a || b.event, a = a.touches && a.touches.length ? a.touches[0] : a.changedTouches && a.changedTouches.length ? a.changedTouches[0] : a, a.pageX ? (c.x = a.pageX, c.y = a.pageY) : (c.x = a.clientX, c.y = a.clientY), c;
  }, e.prototype.isNumeric = function (a) {
    return !isNaN(parseFloat(a));
  }, e.prototype.difference = function (a, b) {
    return {
      x: a.x - b.x,
      y: a.y - b.y
    };
  }, a.fn.owlCarousel = function (b) {
    var c = Array.prototype.slice.call(arguments, 1);
    return this.each(function () {
      var d = a(this),
        f = d.data("owl.carousel");
      f || (f = new e(this, "object" == _typeof(b) && b), d.data("owl.carousel", f), a.each(["next", "prev", "to", "destroy", "refresh", "replace", "add", "remove"], function (b, c) {
        f.register({
          type: e.Type.Event,
          name: c
        }), f.$element.on(c + ".owl.carousel.core", a.proxy(function (a) {
          a.namespace && a.relatedTarget !== this && (this.suppress([c]), f[c].apply(this, [].slice.call(arguments, 1)), this.release([c]));
        }, f));
      })), "string" == typeof b && "_" !== b.charAt(0) && f[b].apply(f, c);
    });
  }, a.fn.owlCarousel.Constructor = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this._core = b, this._interval = null, this._visible = null, this._handlers = {
      "initialized.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.autoRefresh && this.watch();
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this._core.$element.on(this._handlers);
  };
  e.Defaults = {
    autoRefresh: !0,
    autoRefreshInterval: 500
  }, e.prototype.watch = function () {
    this._interval || (this._visible = this._core.$element.is(":visible"), this._interval = b.setInterval(a.proxy(this.refresh, this), this._core.settings.autoRefreshInterval));
  }, e.prototype.refresh = function () {
    this._core.$element.is(":visible") !== this._visible && (this._visible = !this._visible, this._core.$element.toggleClass("owl-hidden", !this._visible), this._visible && this._core.invalidate("width") && this._core.refresh());
  }, e.prototype.destroy = function () {
    var a, c;
    b.clearInterval(this._interval);
    for (a in this._handlers) this._core.$element.off(a, this._handlers[a]);
    for (c in Object.getOwnPropertyNames(this)) "function" != typeof this[c] && (this[c] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.AutoRefresh = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this._core = b, this._loaded = [], this._handlers = {
      "initialized.owl.carousel change.owl.carousel resized.owl.carousel": a.proxy(function (b) {
        if (b.namespace && this._core.settings && this._core.settings.lazyLoad && (b.property && "position" == b.property.name || "initialized" == b.type)) for (var c = this._core.settings, e = c.center && Math.ceil(c.items / 2) || c.items, f = c.center && -1 * e || 0, g = (b.property && b.property.value !== d ? b.property.value : this._core.current()) + f, h = this._core.clones().length, i = a.proxy(function (a, b) {
            this.load(b);
          }, this); f++ < e;) this.load(h / 2 + this._core.relative(g)), h && a.each(this._core.clones(this._core.relative(g)), i), g++;
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this._core.$element.on(this._handlers);
  };
  e.Defaults = {
    lazyLoad: !1
  }, e.prototype.load = function (c) {
    var d = this._core.$stage.children().eq(c),
      e = d && d.find(".owl-lazy");
    !e || a.inArray(d.get(0), this._loaded) > -1 || (e.each(a.proxy(function (c, d) {
      var e,
        f = a(d),
        g = b.devicePixelRatio > 1 && f.attr("data-src-retina") || f.attr("data-src");
      this._core.trigger("load", {
        element: f,
        url: g
      }, "lazy"), f.is("img") ? f.one("load.owl.lazy", a.proxy(function () {
        f.css("opacity", 1), this._core.trigger("loaded", {
          element: f,
          url: g
        }, "lazy");
      }, this)).attr("src", g) : (e = new Image(), e.onload = a.proxy(function () {
        f.css({
          "background-image": 'url("' + g + '")',
          opacity: "1"
        }), this._core.trigger("loaded", {
          element: f,
          url: g
        }, "lazy");
      }, this), e.src = g);
    }, this)), this._loaded.push(d.get(0)));
  }, e.prototype.destroy = function () {
    var a, b;
    for (a in this.handlers) this._core.$element.off(a, this.handlers[a]);
    for (b in Object.getOwnPropertyNames(this)) "function" != typeof this[b] && (this[b] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.Lazy = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this._core = b, this._handlers = {
      "initialized.owl.carousel refreshed.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.autoHeight && this.update();
      }, this),
      "changed.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.autoHeight && "position" == a.property.name && this.update();
      }, this),
      "loaded.owl.lazy": a.proxy(function (a) {
        a.namespace && this._core.settings.autoHeight && a.element.closest("." + this._core.settings.itemClass).index() === this._core.current() && this.update();
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this._core.$element.on(this._handlers);
  };
  e.Defaults = {
    autoHeight: !1,
    autoHeightClass: "owl-height"
  }, e.prototype.update = function () {
    var b = this._core._current,
      c = b + this._core.settings.items,
      d = this._core.$stage.children().toArray().slice(b, c),
      e = [],
      f = 0;
    a.each(d, function (b, c) {
      e.push(a(c).height());
    }), f = Math.max.apply(null, e), this._core.$stage.parent().height(f).addClass(this._core.settings.autoHeightClass);
  }, e.prototype.destroy = function () {
    var a, b;
    for (a in this._handlers) this._core.$element.off(a, this._handlers[a]);
    for (b in Object.getOwnPropertyNames(this)) "function" != typeof this[b] && (this[b] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.AutoHeight = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this._core = b, this._videos = {}, this._playing = null, this._handlers = {
      "initialized.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.register({
          type: "state",
          name: "playing",
          tags: ["interacting"]
        });
      }, this),
      "resize.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.video && this.isInFullScreen() && a.preventDefault();
      }, this),
      "refreshed.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.is("resizing") && this._core.$stage.find(".cloned .owl-video-frame").remove();
      }, this),
      "changed.owl.carousel": a.proxy(function (a) {
        a.namespace && "position" === a.property.name && this._playing && this.stop();
      }, this),
      "prepared.owl.carousel": a.proxy(function (b) {
        if (b.namespace) {
          var c = a(b.content).find(".owl-video");
          c.length && (c.css("display", "none"), this.fetch(c, a(b.content)));
        }
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this._core.$element.on(this._handlers), this._core.$element.on("click.owl.video", ".owl-video-play-icon", a.proxy(function (a) {
      this.play(a);
    }, this));
  };
  e.Defaults = {
    video: !1,
    videoHeight: !1,
    videoWidth: !1
  }, e.prototype.fetch = function (a, b) {
    var c = function () {
        return a.attr("data-vimeo-id") ? "vimeo" : a.attr("data-vzaar-id") ? "vzaar" : "youtube";
      }(),
      d = a.attr("data-vimeo-id") || a.attr("data-youtube-id") || a.attr("data-vzaar-id"),
      e = a.attr("data-width") || this._core.settings.videoWidth,
      f = a.attr("data-height") || this._core.settings.videoHeight,
      g = a.attr("href");
    if (!g) throw new Error("Missing video URL.");
    if (d = g.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/), d[3].indexOf("youtu") > -1) c = "youtube";else if (d[3].indexOf("vimeo") > -1) c = "vimeo";else {
      if (!(d[3].indexOf("vzaar") > -1)) throw new Error("Video URL not supported.");
      c = "vzaar";
    }
    d = d[6], this._videos[g] = {
      type: c,
      id: d,
      width: e,
      height: f
    }, b.attr("data-video", g), this.thumbnail(a, this._videos[g]);
  }, e.prototype.thumbnail = function (b, c) {
    var d,
      e,
      f,
      g = c.width && c.height ? 'style="width:' + c.width + "px;height:" + c.height + 'px;"' : "",
      h = b.find("img"),
      i = "src",
      j = "",
      k = this._core.settings,
      l = function l(a) {
        e = '<div class="owl-video-play-icon"></div>', d = k.lazyLoad ? '<div class="owl-video-tn ' + j + '" ' + i + '="' + a + '"></div>' : '<div class="owl-video-tn" style="opacity:1;background-image:url(' + a + ')"></div>', b.after(d), b.after(e);
      };
    if (b.wrap('<div class="owl-video-wrapper"' + g + "></div>"), this._core.settings.lazyLoad && (i = "data-src", j = "owl-lazy"), h.length) return l(h.attr(i)), h.remove(), !1;
    "youtube" === c.type ? (f = "//img.youtube.com/vi/" + c.id + "/hqdefault.jpg", l(f)) : "vimeo" === c.type ? a.ajax({
      type: "GET",
      url: "//vimeo.com/api/v2/video/" + c.id + ".json",
      jsonp: "callback",
      dataType: "jsonp",
      success: function success(a) {
        f = a[0].thumbnail_large, l(f);
      }
    }) : "vzaar" === c.type && a.ajax({
      type: "GET",
      url: "//vzaar.com/api/videos/" + c.id + ".json",
      jsonp: "callback",
      dataType: "jsonp",
      success: function success(a) {
        f = a.framegrab_url, l(f);
      }
    });
  }, e.prototype.stop = function () {
    this._core.trigger("stop", null, "video"), this._playing.find(".owl-video-frame").remove(), this._playing.removeClass("owl-video-playing"), this._playing = null, this._core.leave("playing"), this._core.trigger("stopped", null, "video");
  }, e.prototype.play = function (b) {
    var c,
      d = a(b.target),
      e = d.closest("." + this._core.settings.itemClass),
      f = this._videos[e.attr("data-video")],
      g = f.width || "100%",
      h = f.height || this._core.$stage.height();
    this._playing || (this._core.enter("playing"), this._core.trigger("play", null, "video"), e = this._core.items(this._core.relative(e.index())), this._core.reset(e.index()), "youtube" === f.type ? c = '<iframe width="' + g + '" height="' + h + '" src="//www.youtube.com/embed/' + f.id + "?autoplay=1&rel=0&v=" + f.id + '" frameborder="0" allowfullscreen></iframe>' : "vimeo" === f.type ? c = '<iframe src="//player.vimeo.com/video/' + f.id + '?autoplay=1" width="' + g + '" height="' + h + '" frameborder="0" webkitallowfullscreen mozallowfullscreen allowfullscreen></iframe>' : "vzaar" === f.type && (c = '<iframe frameborder="0"height="' + h + '"width="' + g + '" allowfullscreen mozallowfullscreen webkitAllowFullScreen src="//view.vzaar.com/' + f.id + '/player?autoplay=true"></iframe>'), a('<div class="owl-video-frame">' + c + "</div>").insertAfter(e.find(".owl-video")), this._playing = e.addClass("owl-video-playing"));
  }, e.prototype.isInFullScreen = function () {
    var b = c.fullscreenElement || c.mozFullScreenElement || c.webkitFullscreenElement;
    return b && a(b).parent().hasClass("owl-video-frame");
  }, e.prototype.destroy = function () {
    var a, b;
    this._core.$element.off("click.owl.video");
    for (a in this._handlers) this._core.$element.off(a, this._handlers[a]);
    for (b in Object.getOwnPropertyNames(this)) "function" != typeof this[b] && (this[b] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.Video = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this.core = b, this.core.options = a.extend({}, e.Defaults, this.core.options), this.swapping = !0, this.previous = d, this.next = d, this.handlers = {
      "change.owl.carousel": a.proxy(function (a) {
        a.namespace && "position" == a.property.name && (this.previous = this.core.current(), this.next = a.property.value);
      }, this),
      "drag.owl.carousel dragged.owl.carousel translated.owl.carousel": a.proxy(function (a) {
        a.namespace && (this.swapping = "translated" == a.type);
      }, this),
      "translate.owl.carousel": a.proxy(function (a) {
        a.namespace && this.swapping && (this.core.options.animateOut || this.core.options.animateIn) && this.swap();
      }, this)
    }, this.core.$element.on(this.handlers);
  };
  e.Defaults = {
    animateOut: !1,
    animateIn: !1
  }, e.prototype.swap = function () {
    if (1 === this.core.settings.items && a.support.animation && a.support.transition) {
      this.core.speed(0);
      var b,
        c = a.proxy(this.clear, this),
        d = this.core.$stage.children().eq(this.previous),
        e = this.core.$stage.children().eq(this.next),
        f = this.core.settings.animateIn,
        g = this.core.settings.animateOut;
      this.core.current() !== this.previous && (g && (b = this.core.coordinates(this.previous) - this.core.coordinates(this.next), d.one(a.support.animation.end, c).css({
        left: b + "px"
      }).addClass("animated owl-animated-out").addClass(g)), f && e.one(a.support.animation.end, c).addClass("animated owl-animated-in").addClass(f));
    }
  }, e.prototype.clear = function (b) {
    a(b.target).css({
      left: ""
    }).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut), this.core.onTransitionEnd();
  }, e.prototype.destroy = function () {
    var a, b;
    for (a in this.handlers) this.core.$element.off(a, this.handlers[a]);
    for (b in Object.getOwnPropertyNames(this)) "function" != typeof this[b] && (this[b] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.Animate = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  var e = function e(b) {
    this._core = b, this._timeout = null, this._paused = !1, this._handlers = {
      "changed.owl.carousel": a.proxy(function (a) {
        a.namespace && "settings" === a.property.name ? this._core.settings.autoplay ? this.play() : this.stop() : a.namespace && "position" === a.property.name && this._core.settings.autoplay && this._setAutoPlayInterval();
      }, this),
      "initialized.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.autoplay && this.play();
      }, this),
      "play.owl.autoplay": a.proxy(function (a, b, c) {
        a.namespace && this.play(b, c);
      }, this),
      "stop.owl.autoplay": a.proxy(function (a) {
        a.namespace && this.stop();
      }, this),
      "mouseover.owl.autoplay": a.proxy(function () {
        this._core.settings.autoplayHoverPause && this._core.is("rotating") && this.pause();
      }, this),
      "mouseleave.owl.autoplay": a.proxy(function () {
        this._core.settings.autoplayHoverPause && this._core.is("rotating") && this.play();
      }, this),
      "touchstart.owl.core": a.proxy(function () {
        this._core.settings.autoplayHoverPause && this._core.is("rotating") && this.pause();
      }, this),
      "touchend.owl.core": a.proxy(function () {
        this._core.settings.autoplayHoverPause && this.play();
      }, this)
    }, this._core.$element.on(this._handlers), this._core.options = a.extend({}, e.Defaults, this._core.options);
  };
  e.Defaults = {
    autoplay: !1,
    autoplayTimeout: 5e3,
    autoplayHoverPause: !1,
    autoplaySpeed: !1
  }, e.prototype.play = function (a, b) {
    this._paused = !1, this._core.is("rotating") || (this._core.enter("rotating"), this._setAutoPlayInterval());
  }, e.prototype._getNextTimeout = function (d, e) {
    return this._timeout && b.clearTimeout(this._timeout), b.setTimeout(a.proxy(function () {
      this._paused || this._core.is("busy") || this._core.is("interacting") || c.hidden || this._core.next(e || this._core.settings.autoplaySpeed);
    }, this), d || this._core.settings.autoplayTimeout);
  }, e.prototype._setAutoPlayInterval = function () {
    this._timeout = this._getNextTimeout();
  }, e.prototype.stop = function () {
    this._core.is("rotating") && (b.clearTimeout(this._timeout), this._core.leave("rotating"));
  }, e.prototype.pause = function () {
    this._core.is("rotating") && (this._paused = !0);
  }, e.prototype.destroy = function () {
    var a, b;
    this.stop();
    for (a in this._handlers) this._core.$element.off(a, this._handlers[a]);
    for (b in Object.getOwnPropertyNames(this)) "function" != typeof this[b] && (this[b] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.autoplay = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  "use strict";

  var e = function e(b) {
    this._core = b, this._initialized = !1, this._pages = [], this._controls = {}, this._templates = [], this.$element = this._core.$element, this._overrides = {
      next: this._core.next,
      prev: this._core.prev,
      to: this._core.to
    }, this._handlers = {
      "prepared.owl.carousel": a.proxy(function (b) {
        b.namespace && this._core.settings.dotsData && this._templates.push('<div class="' + this._core.settings.dotClass + '">' + a(b.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot") + "</div>");
      }, this),
      "added.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.dotsData && this._templates.splice(a.position, 0, this._templates.pop());
      }, this),
      "remove.owl.carousel": a.proxy(function (a) {
        a.namespace && this._core.settings.dotsData && this._templates.splice(a.position, 1);
      }, this),
      "changed.owl.carousel": a.proxy(function (a) {
        a.namespace && "position" == a.property.name && this.draw();
      }, this),
      "initialized.owl.carousel": a.proxy(function (a) {
        a.namespace && !this._initialized && (this._core.trigger("initialize", null, "navigation"), this.initialize(), this.update(), this.draw(), this._initialized = !0, this._core.trigger("initialized", null, "navigation"));
      }, this),
      "refreshed.owl.carousel": a.proxy(function (a) {
        a.namespace && this._initialized && (this._core.trigger("refresh", null, "navigation"), this.update(), this.draw(), this._core.trigger("refreshed", null, "navigation"));
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this.$element.on(this._handlers);
  };
  e.Defaults = {
    nav: !1,
    navText: ["prev", "next"],
    navSpeed: !1,
    navElement: "div",
    navContainer: !1,
    navContainerClass: "owl-nav",
    navClass: ["owl-prev", "owl-next"],
    slideBy: 1,
    dotClass: "owl-dot",
    dotsClass: "owl-dots",
    dots: !0,
    dotsEach: !1,
    dotsData: !1,
    dotsSpeed: !1,
    dotsContainer: !1
  }, e.prototype.initialize = function () {
    var b,
      c = this._core.settings;
    this._controls.$relative = (c.navContainer ? a(c.navContainer) : a("<div>").addClass(c.navContainerClass).appendTo(this.$element)).addClass("disabled"), this._controls.$previous = a("<" + c.navElement + ">").addClass(c.navClass[0]).html(c.navText[0]).prependTo(this._controls.$relative).on("click", a.proxy(function (a) {
      this.prev(c.navSpeed);
    }, this)), this._controls.$next = a("<" + c.navElement + ">").addClass(c.navClass[1]).html(c.navText[1]).appendTo(this._controls.$relative).on("click", a.proxy(function (a) {
      this.next(c.navSpeed);
    }, this)), c.dotsData || (this._templates = [a("<div>").addClass(c.dotClass).append(a("<span>")).prop("outerHTML")]), this._controls.$absolute = (c.dotsContainer ? a(c.dotsContainer) : a("<div>").addClass(c.dotsClass).appendTo(this.$element)).addClass("disabled"), this._controls.$absolute.on("click", "div", a.proxy(function (b) {
      var d = a(b.target).parent().is(this._controls.$absolute) ? a(b.target).index() : a(b.target).parent().index();
      b.preventDefault(), this.to(d, c.dotsSpeed);
    }, this));
    for (b in this._overrides) this._core[b] = a.proxy(this[b], this);
  }, e.prototype.destroy = function () {
    var a, b, c, d;
    for (a in this._handlers) this.$element.off(a, this._handlers[a]);
    for (b in this._controls) this._controls[b].remove();
    for (d in this.overides) this._core[d] = this._overrides[d];
    for (c in Object.getOwnPropertyNames(this)) "function" != typeof this[c] && (this[c] = null);
  }, e.prototype.update = function () {
    var a,
      b,
      c,
      d = this._core.clones().length / 2,
      e = d + this._core.items().length,
      f = this._core.maximum(!0),
      g = this._core.settings,
      h = g.center || g.autoWidth || g.dotsData ? 1 : g.dotsEach || g.items;
    if ("page" !== g.slideBy && (g.slideBy = Math.min(g.slideBy, g.items)), g.dots || "page" == g.slideBy) for (this._pages = [], a = d, b = 0, c = 0; a < e; a++) {
      if (b >= h || 0 === b) {
        if (this._pages.push({
          start: Math.min(f, a - d),
          end: a - d + h - 1
        }), Math.min(f, a - d) === f) break;
        b = 0, ++c;
      }
      b += this._core.mergers(this._core.relative(a));
    }
  }, e.prototype.draw = function () {
    var b,
      c = this._core.settings,
      d = this._core.items().length <= c.items,
      e = this._core.relative(this._core.current()),
      f = c.loop || c.rewind;
    this._controls.$relative.toggleClass("disabled", !c.nav || d), c.nav && (this._controls.$previous.toggleClass("disabled", !f && e <= this._core.minimum(!0)), this._controls.$next.toggleClass("disabled", !f && e >= this._core.maximum(!0))), this._controls.$absolute.toggleClass("disabled", !c.dots || d), c.dots && (b = this._pages.length - this._controls.$absolute.children().length, c.dotsData && 0 !== b ? this._controls.$absolute.html(this._templates.join("")) : b > 0 ? this._controls.$absolute.append(new Array(b + 1).join(this._templates[0])) : b < 0 && this._controls.$absolute.children().slice(b).remove(), this._controls.$absolute.find(".active").removeClass("active"), this._controls.$absolute.children().eq(a.inArray(this.current(), this._pages)).addClass("active"));
  }, e.prototype.onTrigger = function (b) {
    var c = this._core.settings;
    b.page = {
      index: a.inArray(this.current(), this._pages),
      count: this._pages.length,
      size: c && (c.center || c.autoWidth || c.dotsData ? 1 : c.dotsEach || c.items)
    };
  }, e.prototype.current = function () {
    var b = this._core.relative(this._core.current());
    return a.grep(this._pages, a.proxy(function (a, c) {
      return a.start <= b && a.end >= b;
    }, this)).pop();
  }, e.prototype.getPosition = function (b) {
    var c,
      d,
      e = this._core.settings;
    return "page" == e.slideBy ? (c = a.inArray(this.current(), this._pages), d = this._pages.length, b ? ++c : --c, c = this._pages[(c % d + d) % d].start) : (c = this._core.relative(this._core.current()), d = this._core.items().length, b ? c += e.slideBy : c -= e.slideBy), c;
  }, e.prototype.next = function (b) {
    a.proxy(this._overrides.to, this._core)(this.getPosition(!0), b);
  }, e.prototype.prev = function (b) {
    a.proxy(this._overrides.to, this._core)(this.getPosition(!1), b);
  }, e.prototype.to = function (b, c, d) {
    var e;
    !d && this._pages.length ? (e = this._pages.length, a.proxy(this._overrides.to, this._core)(this._pages[(b % e + e) % e].start, c)) : a.proxy(this._overrides.to, this._core)(b, c);
  }, a.fn.owlCarousel.Constructor.Plugins.Navigation = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  "use strict";

  var e = function e(c) {
    this._core = c, this._hashes = {}, this.$element = this._core.$element, this._handlers = {
      "initialized.owl.carousel": a.proxy(function (c) {
        c.namespace && "URLHash" === this._core.settings.startPosition && a(b).trigger("hashchange.owl.navigation");
      }, this),
      "prepared.owl.carousel": a.proxy(function (b) {
        if (b.namespace) {
          var c = a(b.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash");
          if (!c) return;
          this._hashes[c] = b.content;
        }
      }, this),
      "changed.owl.carousel": a.proxy(function (c) {
        if (c.namespace && "position" === c.property.name) {
          var d = this._core.items(this._core.relative(this._core.current())),
            e = a.map(this._hashes, function (a, b) {
              return a === d ? b : null;
            }).join();
          if (!e || b.location.hash.slice(1) === e) return;
          b.location.hash = e;
        }
      }, this)
    }, this._core.options = a.extend({}, e.Defaults, this._core.options), this.$element.on(this._handlers), a(b).on("hashchange.owl.navigation", a.proxy(function (a) {
      var c = b.location.hash.substring(1),
        e = this._core.$stage.children(),
        f = this._hashes[c] && e.index(this._hashes[c]);
      f !== d && f !== this._core.current() && this._core.to(this._core.relative(f), !1, !0);
    }, this));
  };
  e.Defaults = {
    URLhashListener: !1
  }, e.prototype.destroy = function () {
    var c, d;
    a(b).off("hashchange.owl.navigation");
    for (c in this._handlers) this._core.$element.off(c, this._handlers[c]);
    for (d in Object.getOwnPropertyNames(this)) "function" != typeof this[d] && (this[d] = null);
  }, a.fn.owlCarousel.Constructor.Plugins.Hash = e;
}(window.Zepto || window.jQuery, window, document), function (a, b, c, d) {
  function e(b, c) {
    var e = !1,
      f = b.charAt(0).toUpperCase() + b.slice(1);
    return a.each((b + " " + h.join(f + " ") + f).split(" "), function (a, b) {
      if (g[b] !== d) return e = !c || b, !1;
    }), e;
  }
  function f(a) {
    return e(a, !0);
  }
  var g = a("<support>").get(0).style,
    h = "Webkit Moz O ms".split(" "),
    i = {
      transition: {
        end: {
          WebkitTransition: "webkitTransitionEnd",
          MozTransition: "transitionend",
          OTransition: "oTransitionEnd",
          transition: "transitionend"
        }
      },
      animation: {
        end: {
          WebkitAnimation: "webkitAnimationEnd",
          MozAnimation: "animationend",
          OAnimation: "oAnimationEnd",
          animation: "animationend"
        }
      }
    },
    j = {
      csstransforms: function csstransforms() {
        return !!e("transform");
      },
      csstransforms3d: function csstransforms3d() {
        return !!e("perspective");
      },
      csstransitions: function csstransitions() {
        return !!e("transition");
      },
      cssanimations: function cssanimations() {
        return !!e("animation");
      }
    };
  j.csstransitions() && (a.support.transition = new String(f("transition")), a.support.transition.end = i.transition.end[a.support.transition]), j.cssanimations() && (a.support.animation = new String(f("animation")), a.support.animation.end = i.animation.end[a.support.animation]), j.csstransforms() && (a.support.transform = new String(f("transform")), a.support.transform3d = j.csstransforms3d());
}(window.Zepto || window.jQuery, window, document);

/***/ }),

/***/ "./resources/js/libs/waypoints.min.js":
/*!********************************************!*\
  !*** ./resources/js/libs/waypoints.min.js ***!
  \********************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// Generated by CoffeeScript 1.6.2
/*!
jQuery Waypoints - v2.0.5
Copyright (c) 2011-2014 Caleb Troughton
Licensed under the MIT license.
https://github.com/imakewebthings/jquery-waypoints/blob/master/licenses.txt
*/
(function () {
  var t = [].indexOf || function (t) {
      for (var e = 0, n = this.length; e < n; e++) {
        if (e in this && this[e] === t) return e;
      }
      return -1;
    },
    e = [].slice;
  (function (t, e) {
    if (true) {
      return !(__WEBPACK_AMD_DEFINE_ARRAY__ = [__webpack_require__(/*! jquery */ "./node_modules/jquery/dist/jquery.js")], __WEBPACK_AMD_DEFINE_RESULT__ = (function (n) {
        return e(n, t);
      }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),
				__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));
    } else {}
  })(window, function (n, r) {
    var i, o, l, s, f, u, c, a, h, d, p, y, v, w, g, m;
    i = n(r);
    a = t.call(r, "ontouchstart") >= 0;
    s = {
      horizontal: {},
      vertical: {}
    };
    f = 1;
    c = {};
    u = "waypoints-context-id";
    p = "resize.waypoints";
    y = "scroll.waypoints";
    v = 1;
    w = "waypoints-waypoint-ids";
    g = "waypoint";
    m = "waypoints";
    o = function () {
      function t(t) {
        var e = this;
        this.$element = t;
        this.element = t[0];
        this.didResize = false;
        this.didScroll = false;
        this.id = "context" + f++;
        this.oldScroll = {
          x: t.scrollLeft(),
          y: t.scrollTop()
        };
        this.waypoints = {
          horizontal: {},
          vertical: {}
        };
        this.element[u] = this.id;
        c[this.id] = this;
        t.bind(y, function () {
          var t;
          if (!(e.didScroll || a)) {
            e.didScroll = true;
            t = function t() {
              e.doScroll();
              return e.didScroll = false;
            };
            return r.setTimeout(t, n[m].settings.scrollThrottle);
          }
        });
        t.bind(p, function () {
          var t;
          if (!e.didResize) {
            e.didResize = true;
            t = function t() {
              n[m]("refresh");
              return e.didResize = false;
            };
            return r.setTimeout(t, n[m].settings.resizeThrottle);
          }
        });
      }
      t.prototype.doScroll = function () {
        var t,
          e = this;
        t = {
          horizontal: {
            newScroll: this.$element.scrollLeft(),
            oldScroll: this.oldScroll.x,
            forward: "right",
            backward: "left"
          },
          vertical: {
            newScroll: this.$element.scrollTop(),
            oldScroll: this.oldScroll.y,
            forward: "down",
            backward: "up"
          }
        };
        if (a && (!t.vertical.oldScroll || !t.vertical.newScroll)) {
          n[m]("refresh");
        }
        n.each(t, function (t, r) {
          var i, o, l;
          l = [];
          o = r.newScroll > r.oldScroll;
          i = o ? r.forward : r.backward;
          n.each(e.waypoints[t], function (t, e) {
            var n, i;
            if (r.oldScroll < (n = e.offset) && n <= r.newScroll) {
              return l.push(e);
            } else if (r.newScroll < (i = e.offset) && i <= r.oldScroll) {
              return l.push(e);
            }
          });
          l.sort(function (t, e) {
            return t.offset - e.offset;
          });
          if (!o) {
            l.reverse();
          }
          return n.each(l, function (t, e) {
            if (e.options.continuous || t === l.length - 1) {
              return e.trigger([i]);
            }
          });
        });
        return this.oldScroll = {
          x: t.horizontal.newScroll,
          y: t.vertical.newScroll
        };
      };
      t.prototype.refresh = function () {
        var t,
          e,
          r,
          i = this;
        r = n.isWindow(this.element);
        e = this.$element.offset();
        this.doScroll();
        t = {
          horizontal: {
            contextOffset: r ? 0 : e.left,
            contextScroll: r ? 0 : this.oldScroll.x,
            contextDimension: this.$element.width(),
            oldScroll: this.oldScroll.x,
            forward: "right",
            backward: "left",
            offsetProp: "left"
          },
          vertical: {
            contextOffset: r ? 0 : e.top,
            contextScroll: r ? 0 : this.oldScroll.y,
            contextDimension: r ? n[m]("viewportHeight") : this.$element.height(),
            oldScroll: this.oldScroll.y,
            forward: "down",
            backward: "up",
            offsetProp: "top"
          }
        };
        return n.each(t, function (t, e) {
          return n.each(i.waypoints[t], function (t, r) {
            var i, o, l, s, f;
            i = r.options.offset;
            l = r.offset;
            o = n.isWindow(r.element) ? 0 : r.$element.offset()[e.offsetProp];
            if (n.isFunction(i)) {
              i = i.apply(r.element);
            } else if (typeof i === "string") {
              i = parseFloat(i);
              if (r.options.offset.indexOf("%") > -1) {
                i = Math.ceil(e.contextDimension * i / 100);
              }
            }
            r.offset = o - e.contextOffset + e.contextScroll - i;
            if (r.options.onlyOnScroll && l != null || !r.enabled) {
              return;
            }
            if (l !== null && l < (s = e.oldScroll) && s <= r.offset) {
              return r.trigger([e.backward]);
            } else if (l !== null && l > (f = e.oldScroll) && f >= r.offset) {
              return r.trigger([e.forward]);
            } else if (l === null && e.oldScroll >= r.offset) {
              return r.trigger([e.forward]);
            }
          });
        });
      };
      t.prototype.checkEmpty = function () {
        if (n.isEmptyObject(this.waypoints.horizontal) && n.isEmptyObject(this.waypoints.vertical)) {
          this.$element.unbind([p, y].join(" "));
          return delete c[this.id];
        }
      };
      return t;
    }();
    l = function () {
      function t(t, e, r) {
        var i, o;
        if (r.offset === "bottom-in-view") {
          r.offset = function () {
            var t;
            t = n[m]("viewportHeight");
            if (!n.isWindow(e.element)) {
              t = e.$element.height();
            }
            return t - n(this).outerHeight();
          };
        }
        this.$element = t;
        this.element = t[0];
        this.axis = r.horizontal ? "horizontal" : "vertical";
        this.callback = r.handler;
        this.context = e;
        this.enabled = r.enabled;
        this.id = "waypoints" + v++;
        this.offset = null;
        this.options = r;
        e.waypoints[this.axis][this.id] = this;
        s[this.axis][this.id] = this;
        i = (o = this.element[w]) != null ? o : [];
        i.push(this.id);
        this.element[w] = i;
      }
      t.prototype.trigger = function (t) {
        if (!this.enabled) {
          return;
        }
        if (this.callback != null) {
          this.callback.apply(this.element, t);
        }
        if (this.options.triggerOnce) {
          return this.destroy();
        }
      };
      t.prototype.disable = function () {
        return this.enabled = false;
      };
      t.prototype.enable = function () {
        this.context.refresh();
        return this.enabled = true;
      };
      t.prototype.destroy = function () {
        delete s[this.axis][this.id];
        delete this.context.waypoints[this.axis][this.id];
        return this.context.checkEmpty();
      };
      t.getWaypointsByElement = function (t) {
        var e, r;
        r = t[w];
        if (!r) {
          return [];
        }
        e = n.extend({}, s.horizontal, s.vertical);
        return n.map(r, function (t) {
          return e[t];
        });
      };
      return t;
    }();
    d = {
      init: function init(t, e) {
        var r;
        e = n.extend({}, n.fn[g].defaults, e);
        if ((r = e.handler) == null) {
          e.handler = t;
        }
        this.each(function () {
          var t, r, i, s;
          t = n(this);
          i = (s = e.context) != null ? s : n.fn[g].defaults.context;
          if (!n.isWindow(i)) {
            i = t.closest(i);
          }
          i = n(i);
          r = c[i[0][u]];
          if (!r) {
            r = new o(i);
          }
          return new l(t, r, e);
        });
        n[m]("refresh");
        return this;
      },
      disable: function disable() {
        return d._invoke.call(this, "disable");
      },
      enable: function enable() {
        return d._invoke.call(this, "enable");
      },
      destroy: function destroy() {
        return d._invoke.call(this, "destroy");
      },
      prev: function prev(t, e) {
        return d._traverse.call(this, t, e, function (t, e, n) {
          if (e > 0) {
            return t.push(n[e - 1]);
          }
        });
      },
      next: function next(t, e) {
        return d._traverse.call(this, t, e, function (t, e, n) {
          if (e < n.length - 1) {
            return t.push(n[e + 1]);
          }
        });
      },
      _traverse: function _traverse(t, e, i) {
        var o, l;
        if (t == null) {
          t = "vertical";
        }
        if (e == null) {
          e = r;
        }
        l = h.aggregate(e);
        o = [];
        this.each(function () {
          var e;
          e = n.inArray(this, l[t]);
          return i(o, e, l[t]);
        });
        return this.pushStack(o);
      },
      _invoke: function _invoke(t) {
        this.each(function () {
          var e;
          e = l.getWaypointsByElement(this);
          return n.each(e, function (e, n) {
            n[t]();
            return true;
          });
        });
        return this;
      }
    };
    n.fn[g] = function () {
      var t, r;
      r = arguments[0], t = 2 <= arguments.length ? e.call(arguments, 1) : [];
      if (d[r]) {
        return d[r].apply(this, t);
      } else if (n.isFunction(r)) {
        return d.init.apply(this, arguments);
      } else if (n.isPlainObject(r)) {
        return d.init.apply(this, [null, r]);
      } else if (!r) {
        return n.error("jQuery Waypoints needs a callback function or handler option.");
      } else {
        return n.error("The " + r + " method does not exist in jQuery Waypoints.");
      }
    };
    n.fn[g].defaults = {
      context: r,
      continuous: true,
      enabled: true,
      horizontal: false,
      offset: 0,
      triggerOnce: false
    };
    h = {
      refresh: function refresh() {
        return n.each(c, function (t, e) {
          return e.refresh();
        });
      },
      viewportHeight: function viewportHeight() {
        var t;
        return (t = r.innerHeight) != null ? t : i.height();
      },
      aggregate: function aggregate(t) {
        var e, r, i;
        e = s;
        if (t) {
          e = (i = c[n(t)[0][u]]) != null ? i.waypoints : void 0;
        }
        if (!e) {
          return [];
        }
        r = {
          horizontal: [],
          vertical: []
        };
        n.each(r, function (t, i) {
          n.each(e[t], function (t, e) {
            return i.push(e);
          });
          i.sort(function (t, e) {
            return t.offset - e.offset;
          });
          r[t] = n.map(i, function (t) {
            return t.element;
          });
          return r[t] = n.unique(r[t]);
        });
        return r;
      },
      above: function above(t) {
        if (t == null) {
          t = r;
        }
        return h._filter(t, "vertical", function (t, e) {
          return e.offset <= t.oldScroll.y;
        });
      },
      below: function below(t) {
        if (t == null) {
          t = r;
        }
        return h._filter(t, "vertical", function (t, e) {
          return e.offset > t.oldScroll.y;
        });
      },
      left: function left(t) {
        if (t == null) {
          t = r;
        }
        return h._filter(t, "horizontal", function (t, e) {
          return e.offset <= t.oldScroll.x;
        });
      },
      right: function right(t) {
        if (t == null) {
          t = r;
        }
        return h._filter(t, "horizontal", function (t, e) {
          return e.offset > t.oldScroll.x;
        });
      },
      enable: function enable() {
        return h._invoke("enable");
      },
      disable: function disable() {
        return h._invoke("disable");
      },
      destroy: function destroy() {
        return h._invoke("destroy");
      },
      extendFn: function extendFn(t, e) {
        return d[t] = e;
      },
      _invoke: function _invoke(t) {
        var e;
        e = n.extend({}, s.vertical, s.horizontal);
        return n.each(e, function (e, n) {
          n[t]();
          return true;
        });
      },
      _filter: function _filter(t, e, r) {
        var i, o;
        i = c[n(t)[0][u]];
        if (!i) {
          return [];
        }
        o = [];
        n.each(i.waypoints[e], function (t, e) {
          if (r(i, e)) {
            return o.push(e);
          }
        });
        o.sort(function (t, e) {
          return t.offset - e.offset;
        });
        return n.map(o, function (t) {
          return t.element;
        });
      }
    };
    n[m] = function () {
      var t, n;
      n = arguments[0], t = 2 <= arguments.length ? e.call(arguments, 1) : [];
      if (h[n]) {
        return h[n].apply(null, t);
      } else {
        return h.aggregate.call(null, n);
      }
    };
    n[m].settings = {
      resizeThrottle: 100,
      scrollThrottle: 30
    };
    return i.on("load.waypoints", function () {
      return n[m]("refresh");
    });
  });
}).call(this);

/***/ }),

/***/ "./resources/js/libs/wow.min.js":
/*!**************************************!*\
  !*** ./resources/js/libs/wow.min.js ***!
  \**************************************/
/*! no static exports found */
/***/ (function(module, exports) {

/*! WOW - v1.1.2 - 2016-04-08
* Copyright (c) 2016 Matthieu Aussaguel;*/(function () {
  var a,
    b,
    c,
    d,
    e,
    f = function f(a, b) {
      return function () {
        return a.apply(b, arguments);
      };
    },
    g = [].indexOf || function (a) {
      for (var b = 0, c = this.length; c > b; b++) if (b in this && this[b] === a) return b;
      return -1;
    };
  b = function () {
    function a() {}
    return a.prototype.extend = function (a, b) {
      var c, d;
      for (c in b) d = b[c], null == a[c] && (a[c] = d);
      return a;
    }, a.prototype.isMobile = function (a) {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(a);
    }, a.prototype.createEvent = function (a, b, c, d) {
      var e;
      return null == b && (b = !1), null == c && (c = !1), null == d && (d = null), null != document.createEvent ? (e = document.createEvent("CustomEvent"), e.initCustomEvent(a, b, c, d)) : null != document.createEventObject ? (e = document.createEventObject(), e.eventType = a) : e.eventName = a, e;
    }, a.prototype.emitEvent = function (a, b) {
      return null != a.dispatchEvent ? a.dispatchEvent(b) : b in (null != a) ? a[b]() : "on" + b in (null != a) ? a["on" + b]() : void 0;
    }, a.prototype.addEvent = function (a, b, c) {
      return null != a.addEventListener ? a.addEventListener(b, c, !1) : null != a.attachEvent ? a.attachEvent("on" + b, c) : a[b] = c;
    }, a.prototype.removeEvent = function (a, b, c) {
      return null != a.removeEventListener ? a.removeEventListener(b, c, !1) : null != a.detachEvent ? a.detachEvent("on" + b, c) : delete a[b];
    }, a.prototype.innerHeight = function () {
      return "innerHeight" in window ? window.innerHeight : document.documentElement.clientHeight;
    }, a;
  }(), c = this.WeakMap || this.MozWeakMap || (c = function () {
    function a() {
      this.keys = [], this.values = [];
    }
    return a.prototype.get = function (a) {
      var b, c, d, e, f;
      for (f = this.keys, b = d = 0, e = f.length; e > d; b = ++d) if (c = f[b], c === a) return this.values[b];
    }, a.prototype.set = function (a, b) {
      var c, d, e, f, g;
      for (g = this.keys, c = e = 0, f = g.length; f > e; c = ++e) if (d = g[c], d === a) return void (this.values[c] = b);
      return this.keys.push(a), this.values.push(b);
    }, a;
  }()), a = this.MutationObserver || this.WebkitMutationObserver || this.MozMutationObserver || (a = function () {
    function a() {
      "undefined" != typeof console && null !== console && console.warn("MutationObserver is not supported by your browser."), "undefined" != typeof console && null !== console && console.warn("WOW.js cannot detect dom mutations, please call .sync() after loading new content.");
    }
    return a.notSupported = !0, a.prototype.observe = function () {}, a;
  }()), d = this.getComputedStyle || function (a, b) {
    return this.getPropertyValue = function (b) {
      var c;
      return "float" === b && (b = "styleFloat"), e.test(b) && b.replace(e, function (a, b) {
        return b.toUpperCase();
      }), (null != (c = a.currentStyle) ? c[b] : void 0) || null;
    }, this;
  }, e = /(\-([a-z]){1})/g, this.WOW = function () {
    function e(a) {
      null == a && (a = {}), this.scrollCallback = f(this.scrollCallback, this), this.scrollHandler = f(this.scrollHandler, this), this.resetAnimation = f(this.resetAnimation, this), this.start = f(this.start, this), this.scrolled = !0, this.config = this.util().extend(a, this.defaults), null != a.scrollContainer && (this.config.scrollContainer = document.querySelector(a.scrollContainer)), this.animationNameCache = new c(), this.wowEvent = this.util().createEvent(this.config.boxClass);
    }
    return e.prototype.defaults = {
      boxClass: "wow",
      animateClass: "animated",
      offset: 0,
      mobile: !0,
      live: !0,
      callback: null,
      scrollContainer: null
    }, e.prototype.init = function () {
      var a;
      return this.element = window.document.documentElement, "interactive" === (a = document.readyState) || "complete" === a ? this.start() : this.util().addEvent(document, "DOMContentLoaded", this.start), this.finished = [];
    }, e.prototype.start = function () {
      var b, c, d, e;
      if (this.stopped = !1, this.boxes = function () {
        var a, c, d, e;
        for (d = this.element.querySelectorAll("." + this.config.boxClass), e = [], a = 0, c = d.length; c > a; a++) b = d[a], e.push(b);
        return e;
      }.call(this), this.all = function () {
        var a, c, d, e;
        for (d = this.boxes, e = [], a = 0, c = d.length; c > a; a++) b = d[a], e.push(b);
        return e;
      }.call(this), this.boxes.length) if (this.disabled()) this.resetStyle();else for (e = this.boxes, c = 0, d = e.length; d > c; c++) b = e[c], this.applyStyle(b, !0);
      return this.disabled() || (this.util().addEvent(this.config.scrollContainer || window, "scroll", this.scrollHandler), this.util().addEvent(window, "resize", this.scrollHandler), this.interval = setInterval(this.scrollCallback, 50)), this.config.live ? new a(function (a) {
        return function (b) {
          var c, d, e, f, g;
          for (g = [], c = 0, d = b.length; d > c; c++) f = b[c], g.push(function () {
            var a, b, c, d;
            for (c = f.addedNodes || [], d = [], a = 0, b = c.length; b > a; a++) e = c[a], d.push(this.doSync(e));
            return d;
          }.call(a));
          return g;
        };
      }(this)).observe(document.body, {
        childList: !0,
        subtree: !0
      }) : void 0;
    }, e.prototype.stop = function () {
      return this.stopped = !0, this.util().removeEvent(this.config.scrollContainer || window, "scroll", this.scrollHandler), this.util().removeEvent(window, "resize", this.scrollHandler), null != this.interval ? clearInterval(this.interval) : void 0;
    }, e.prototype.sync = function (b) {
      return a.notSupported ? this.doSync(this.element) : void 0;
    }, e.prototype.doSync = function (a) {
      var b, c, d, e, f;
      if (null == a && (a = this.element), 1 === a.nodeType) {
        for (a = a.parentNode || a, e = a.querySelectorAll("." + this.config.boxClass), f = [], c = 0, d = e.length; d > c; c++) b = e[c], g.call(this.all, b) < 0 ? (this.boxes.push(b), this.all.push(b), this.stopped || this.disabled() ? this.resetStyle() : this.applyStyle(b, !0), f.push(this.scrolled = !0)) : f.push(void 0);
        return f;
      }
    }, e.prototype.show = function (a) {
      return this.applyStyle(a), a.className = a.className + " " + this.config.animateClass, null != this.config.callback && this.config.callback(a), this.util().emitEvent(a, this.wowEvent), this.util().addEvent(a, "animationend", this.resetAnimation), this.util().addEvent(a, "oanimationend", this.resetAnimation), this.util().addEvent(a, "webkitAnimationEnd", this.resetAnimation), this.util().addEvent(a, "MSAnimationEnd", this.resetAnimation), a;
    }, e.prototype.applyStyle = function (a, b) {
      var c, d, e;
      return d = a.getAttribute("data-wow-duration"), c = a.getAttribute("data-wow-delay"), e = a.getAttribute("data-wow-iteration"), this.animate(function (f) {
        return function () {
          return f.customStyle(a, b, d, c, e);
        };
      }(this));
    }, e.prototype.animate = function () {
      return "requestAnimationFrame" in window ? function (a) {
        return window.requestAnimationFrame(a);
      } : function (a) {
        return a();
      };
    }(), e.prototype.resetStyle = function () {
      var a, b, c, d, e;
      for (d = this.boxes, e = [], b = 0, c = d.length; c > b; b++) a = d[b], e.push(a.style.visibility = "visible");
      return e;
    }, e.prototype.resetAnimation = function (a) {
      var b;
      return a.type.toLowerCase().indexOf("animationend") >= 0 ? (b = a.target || a.srcElement, b.className = b.className.replace(this.config.animateClass, "").trim()) : void 0;
    }, e.prototype.customStyle = function (a, b, c, d, e) {
      return b && this.cacheAnimationName(a), a.style.visibility = b ? "hidden" : "visible", c && this.vendorSet(a.style, {
        animationDuration: c
      }), d && this.vendorSet(a.style, {
        animationDelay: d
      }), e && this.vendorSet(a.style, {
        animationIterationCount: e
      }), this.vendorSet(a.style, {
        animationName: b ? "none" : this.cachedAnimationName(a)
      }), a;
    }, e.prototype.vendors = ["moz", "webkit"], e.prototype.vendorSet = function (a, b) {
      var c, d, e, f;
      d = [];
      for (c in b) e = b[c], a["" + c] = e, d.push(function () {
        var b, d, g, h;
        for (g = this.vendors, h = [], b = 0, d = g.length; d > b; b++) f = g[b], h.push(a["" + f + c.charAt(0).toUpperCase() + c.substr(1)] = e);
        return h;
      }.call(this));
      return d;
    }, e.prototype.vendorCSS = function (a, b) {
      var c, e, f, g, h, i;
      for (h = d(a), g = h.getPropertyCSSValue(b), f = this.vendors, c = 0, e = f.length; e > c; c++) i = f[c], g = g || h.getPropertyCSSValue("-" + i + "-" + b);
      return g;
    }, e.prototype.animationName = function (a) {
      var b;
      try {
        b = this.vendorCSS(a, "animation-name").cssText;
      } catch (c) {
        b = d(a).getPropertyValue("animation-name");
      }
      return "none" === b ? "" : b;
    }, e.prototype.cacheAnimationName = function (a) {
      return this.animationNameCache.set(a, this.animationName(a));
    }, e.prototype.cachedAnimationName = function (a) {
      return this.animationNameCache.get(a);
    }, e.prototype.scrollHandler = function () {
      return this.scrolled = !0;
    }, e.prototype.scrollCallback = function () {
      var a;
      return !this.scrolled || (this.scrolled = !1, this.boxes = function () {
        var b, c, d, e;
        for (d = this.boxes, e = [], b = 0, c = d.length; c > b; b++) a = d[b], a && (this.isVisible(a) ? this.show(a) : e.push(a));
        return e;
      }.call(this), this.boxes.length || this.config.live) ? void 0 : this.stop();
    }, e.prototype.offsetTop = function (a) {
      for (var b; void 0 === a.offsetTop;) a = a.parentNode;
      for (b = a.offsetTop; a = a.offsetParent;) b += a.offsetTop;
      return b;
    }, e.prototype.isVisible = function (a) {
      var b, c, d, e, f;
      return c = a.getAttribute("data-wow-offset") || this.config.offset, f = this.config.scrollContainer && this.config.scrollContainer.scrollTop || window.pageYOffset, e = f + Math.min(this.element.clientHeight, this.util().innerHeight()) - c, d = this.offsetTop(a), b = d + a.clientHeight, e >= d && b >= f;
    }, e.prototype.util = function () {
      return null != this._util ? this._util : this._util = new b();
    }, e.prototype.disabled = function () {
      return !this.config.mobile && this.util().isMobile(navigator.userAgent);
    }, e;
  }();
}).call(this);

/***/ })

}]);
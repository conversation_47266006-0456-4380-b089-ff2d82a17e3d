<?php
//Also refer to deposits controller for deposits code

namespace App\Http\Controllers;

use App\User;
use DateTime;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Nominee;
use Illuminate\Support\Facades\Http;
use App\Models\Member;
use App\Models\Applicant;
use Paynow\Payments\Paynow;
use App\Service;
use Illuminate\Http\Request;
use App\Notifications\PaymentCompleted;
use Illuminate\Support\Facades\DB;
use App\Notifications\DepositNotification;
use Exception;
use Illuminate\Support\Facades\Notification;



class ServicesController extends Controller
{
    public function getAll(){
       return response()->json(Service::all());
    }

}

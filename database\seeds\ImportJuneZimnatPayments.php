<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ImportJuneZimnatPayments extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $csvFile = database_path('seeds/JUNE_Zimnat_payments.csv');
        
        if (!file_exists($csvFile)) {
            $this->command->error("CSV file not found: {$csvFile}");
            return;
        }

        $this->command->info('Starting import of JUNE_Zimnat_payments.csv...');
        
        $handle = fopen($csvFile, 'r');
        
        // Skip the header row
        fgetcsv($handle);
        
        $currentMember = null;
        $membersImported = 0;
        $nomineesImported = 0;
        $errors = [];
        
        while (($data = fgetcsv($handle)) !== false) {
            try {
                // Skip empty rows
                if (empty(array_filter($data))) {
                    continue;
                }
                
                $memberType = trim($data[0]);
                $firstName = trim($data[1]);
                $surname = trim($data[2]);
                $birthDate = trim($data[3]);
                $gender = trim($data[4]);
                $nationalId = trim($data[5]);
                $phoneNumber = trim($data[6]);
                $address = trim($data[7]);
                $amountPaid = trim($data[8]);
                $policyOption = trim($data[9]);
                $tenure = trim($data[10]);
                
                // Skip rows with no name data
                if (empty($firstName) && empty($surname)) {
                    continue;
                }
                
                if ($memberType === 'H') {
                    // This is a member (head)
                    $currentMember = $this->createMember([
                        'first_name' => $firstName,
                        'last_name' => $surname,
                        'dob' => $this->parseDate($birthDate),
                        'gender' => $this->normalizeGender($gender),
                        'gov_id' => $nationalId,
                        'phone' => $phoneNumber,
                        'street' => $address,
                        'amount_paid' => $this->parseAmount($amountPaid),
                        'policy_option' => $policyOption,
                        'tenure' => $tenure
                    ]);
                    
                    if ($currentMember) {
                        $membersImported++;
                        $this->command->info("Imported member: {$firstName} {$surname}");
                    }
                } else {
                    // This is a nominee/dependant
                    if ($currentMember) {
                        $nominee = $this->createNominee($currentMember['id'], [
                            'full_name' => trim($firstName . ' ' . $surname),
                            'dob' => $this->parseDate($birthDate),
                            'gov_id' => $nationalId,
                            'gender' => $this->normalizeGender($gender),
                            'phone' => $phoneNumber,
                            'address' => $address,
                            'amount_paid' => $this->parseAmount($amountPaid),
                            'policy_option' => $policyOption,
                            'tenure' => $tenure
                        ]);
                        
                        if ($nominee) {
                            $nomineesImported++;
                            $this->command->info("  - Imported nominee: {$firstName} {$surname}");
                        }
                    } else {
                        $errors[] = "Nominee found without member: {$firstName} {$surname}";
                    }
                }
                
            } catch (Exception $e) {
                $errors[] = "Error processing row: " . implode(',', $data) . " - " . $e->getMessage();
            }
        }
        
        fclose($handle);
        
        $this->command->info("\n=== Import Summary ===");
        $this->command->info("Members imported: {$membersImported}");
        $this->command->info("Nominees imported: {$nomineesImported}");
        
        if (!empty($errors)) {
            $this->command->error("\n=== Errors ===");
            foreach ($errors as $error) {
                $this->command->error($error);
            }
        }
        
        $this->command->info("\nImport completed!");
    }
    
    /**
     * Create a member record
     */
    private function createMember($data)
    {
        try {
            // Generate a unique member ID
            $memberId = $this->generateMemberId();
            
            $memberData = [
                'id' => $memberId,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'dob' => $data['dob'],
                'gender' => $data['gender'],
                'gov_id' => $data['gov_id'],
                'phone' => $data['phone'],
                'street' => $data['street'],
                'country' => 'Zimbabwe', // Default country
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if member already exists by gov_id or name combination
            $existingMember = DB::table('members')
                ->where('gov_id', $data['gov_id'])
                ->orWhere(function($query) use ($data) {
                    $query->where('first_name', $data['first_name'])
                          ->where('last_name', $data['last_name'])
                          ->where('dob', $data['dob']);
                })
                ->first();
                
            if ($existingMember) {
                $this->command->warn("Member already exists: {$data['first_name']} {$data['last_name']}");
                return ['id' => $existingMember->id];
            }
            
            DB::table('members')->insert($memberData);
            
            return ['id' => $memberId];
            
        } catch (Exception $e) {
            $this->command->error("Error creating member {$data['first_name']} {$data['last_name']}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create a nominee record
     */
    private function createNominee($memberId, $data)
    {
        try {
            $nomineeData = [
                'member_id' => $memberId,
                'full_name' => $data['full_name'],
                'dob' => $data['dob'],
                'gov_id' => $data['gov_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ];
            
            // Check if nominee already exists
            $existingNominee = DB::table('nominees')
                ->where('member_id', $memberId)
                ->where('full_name', $data['full_name'])
                ->where('dob', $data['dob'])
                ->first();
                
            if ($existingNominee) {
                $this->command->warn("  - Nominee already exists: {$data['full_name']}");
                return ['id' => $existingNominee->id];
            }
            
            $nomineeId = DB::table('nominees')->insertGetId($nomineeData);
            
            return ['id' => $nomineeId];
            
        } catch (Exception $e) {
            $this->command->error("Error creating nominee {$data['full_name']}: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Generate a unique member ID
     */
    private function generateMemberId()
    {
        do {
            $id = rand(1000, 999999);
        } while (DB::table('members')->where('id', $id)->exists());
        
        return $id;
    }
    
    /**
     * Parse date from various formats
     */
    private function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }
        
        try {
            // Try different date formats
            $formats = [
                'd/m/Y',
                'd/m/y',
                'd-m-Y',
                'd-m-y',
                'Y-m-d',
                'd M Y',
                'd/m/Y H:i:s'
            ];
            
            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateString);
                if ($date !== false) {
                    return $date->format('Y-m-d');
                }
            }
            
            // Try Carbon parsing as fallback
            return Carbon::parse($dateString)->format('Y-m-d');
            
        } catch (Exception $e) {
            $this->command->warn("Could not parse date: {$dateString}");
            return null;
        }
    }
    
    /**
     * Normalize gender values
     */
    private function normalizeGender($gender)
    {
        $gender = strtolower(trim($gender));
        
        if (in_array($gender, ['male', 'm', 'man'])) {
            return 'Male';
        } elseif (in_array($gender, ['female', 'f', 'woman'])) {
            return 'Female';
        }
        
        return ucfirst($gender);
    }
    
    /**
     * Parse amount from string
     */
    private function parseAmount($amountString)
    {
        if (empty($amountString)) {
            return null;
        }
        
        // Remove currency symbols and spaces
        $amount = preg_replace('/[^\d.,]/', '', $amountString);
        
        return floatval($amount);
    }
}

{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "", "require": {"php": "^8.0.0", "bavix/laravel-wallet": "^7.3", "fruitcake/laravel-cors": "^3.0.0", "guzzlehttp/guzzle": "^7.0.1", "laravel-notification-channels/telegram": "^2.0.0", "laravel/framework": "^8.0", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "paynow/php-sdk": "*", "paypal/paypal-checkout-sdk": "^1.0", "tcg/voyager": "^1.4"}, "require-dev": {"facade/ignition": "^2.3.6", "mockery/mockery": "^1.3.1", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"hooks": {"type": "composer", "url": "https://larapack.io"}}}